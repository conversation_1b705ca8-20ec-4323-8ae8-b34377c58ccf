# MinIO图片管理系统数据库设计文档

## 📋 概述

本文档详细描述了低代码平台中MinIO图片管理系统的数据库设计，包括表结构、索引、视图、存储过程等。

## 🗄️ 数据库表结构

### 1. 图片管理表 (`image`)

用于存储图片的基本信息和元数据。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | bigint | 20 | NOT NULL | AUTO_INCREMENT | 图片ID（主键） |
| name | varchar | 255 | NOT NULL | - | 图片名称（UUID生成） |
| original_name | varchar | 255 | NOT NULL | - | 原始文件名 |
| extension | varchar | 10 | NULL | - | 文件扩展名 |
| size | bigint | 20 | NOT NULL | - | 文件大小（字节） |
| content_type | varchar | 100 | NOT NULL | - | 文件MIME类型 |
| minio_path | varchar | 500 | NOT NULL | - | MinIO存储路径 |
| bucket_name | varchar | 100 | NOT NULL | - | MinIO存储桶名称 |
| url | varchar | 500 | NOT NULL | - | 图片访问URL |
| width | int | 11 | NULL | 0 | 图片宽度（像素） |
| height | int | 11 | NULL | 0 | 图片高度（像素） |
| md5 | varchar | 32 | NOT NULL | - | 图片MD5值（用于去重） |
| category | int | 11 | NOT NULL | 2 | 图片分类 |
| business_id | bigint | 20 | NULL | - | 关联的业务ID |
| business_type | varchar | 50 | NULL | - | 业务类型 |
| status | int | 11 | NOT NULL | 0 | 图片状态 |
| uploader_id | bigint | 20 | NOT NULL | - | 上传者ID |
| uploader_name | varchar | 100 | NOT NULL | - | 上传者姓名 |
| create_time | datetime | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| delete_time | datetime | - | NULL | - | 删除时间 |
| remark | varchar | 500 | NULL | - | 备注信息 |

#### 图片分类说明 (`category`)
- `1`: 组件图标
- `2`: 用户上传
- `3`: 系统图片
- `4`: 模板缩略图

#### 业务类型说明 (`business_type`)
- `component`: 组件相关
- `project`: 项目相关
- `template`: 模板相关
- `user`: 用户相关

#### 图片状态说明 (`status`)
- `0`: 正常
- `1`: 已删除

### 2. 图片操作日志表 (`image_log`)

用于记录图片的所有操作日志，实现完整的审计追踪。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | bigint | 20 | NOT NULL | AUTO_INCREMENT | 日志ID（主键） |
| image_id | bigint | 20 | NULL | - | 图片ID |
| operation_type | varchar | 20 | NOT NULL | - | 操作类型 |
| operation_desc | varchar | 200 | NOT NULL | - | 操作描述 |
| before_data | longtext | - | NULL | - | 操作前数据（JSON格式） |
| after_data | longtext | - | NULL | - | 操作后数据（JSON格式） |
| operator_id | bigint | 20 | NOT NULL | - | 操作者ID |
| operator_name | varchar | 100 | NOT NULL | - | 操作者姓名 |
| ip_address | varchar | 50 | NULL | - | 操作IP地址 |
| user_agent | varchar | 500 | NULL | - | 用户代理信息 |
| result | varchar | 10 | NOT NULL | - | 操作结果 |
| error_message | varchar | 1000 | NULL | - | 错误信息 |
| duration | bigint | 20 | NULL | - | 操作耗时（毫秒） |
| create_time | datetime | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

#### 操作类型说明 (`operation_type`)
- `CREATE`: 创建/上传
- `UPDATE`: 更新
- `DELETE`: 删除
- `VIEW`: 查看
- `DOWNLOAD`: 下载

#### 操作结果说明 (`result`)
- `SUCCESS`: 成功
- `FAILED`: 失败

## 🔍 索引设计

### 主键索引
- `image.id` (PRIMARY KEY)
- `image_log.id` (PRIMARY KEY)

### 唯一索引
- `image.md5` (UNIQUE) - 防止重复上传相同文件

### 普通索引
- `image.category` - 按分类查询
- `image.uploader_id` - 按上传者查询
- `image.status` - 按状态查询
- `image.create_time` - 按创建时间查询
- `image.minio_path` - 按存储路径查询

### 复合索引
- `image(business_type, business_id)` - 按业务信息查询
- `image(category, status)` - 按分类和状态查询
- `image(business_type, business_id, status)` - 按业务信息和状态查询
- `image(uploader_id, create_time)` - 按上传者和时间查询
- `image_log(image_id, operation_type)` - 按图片和操作类型查询
- `image_log(operator_id, create_time)` - 按操作者和时间查询

## 📊 视图设计

### 1. 图片统计视图 (`v_image_statistics`)

提供按分类的图片统计信息：

```sql
SELECT 
    category,
    category_name,
    total_count,      -- 总数量
    active_count,     -- 正常状态数量
    deleted_count,    -- 已删除数量
    total_size,       -- 总大小
    avg_size,         -- 平均大小
    first_upload_time, -- 首次上传时间
    last_upload_time  -- 最后上传时间
FROM v_image_statistics;
```

### 2. 操作日志统计视图 (`v_image_log_statistics`)

提供操作日志的统计信息：

```sql
SELECT 
    operation_type,
    operation_name,
    total_count,      -- 总操作次数
    success_count,    -- 成功次数
    failed_count,     -- 失败次数
    success_rate,     -- 成功率
    avg_duration,     -- 平均耗时
    first_operation_time, -- 首次操作时间
    last_operation_time   -- 最后操作时间
FROM v_image_log_statistics;
```

## 🔧 存储过程

### 清理过期数据存储过程 (`sp_clean_expired_image_data`)

用于定期清理过期的图片记录和操作日志：

```sql
CALL sp_clean_expired_image_data(30, 90);
-- 参数1: 图片保留天数（已删除的图片）
-- 参数2: 日志保留天数
```

### MySQL 8.0.17 兼容性说明

由于MySQL 8.0.17版本在存储过程语法上的一些限制，我们提供了三个版本的SQL脚本：

1. **完整版本** (`image_tables.sql`) - 包含存储过程和视图
2. **MySQL 8.0.17兼容版本** (`image_tables_mysql8.sql`) - 修复了存储过程语法
3. **简化版本** (`image_tables_simple.sql`) - 仅包含表结构，无存储过程

**推荐使用简化版本**，手动执行清理SQL：

```sql
-- 清理30天前删除的图片记录
DELETE FROM `image`
WHERE status = 1
  AND delete_time IS NOT NULL
  AND delete_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理90天前的操作日志
DELETE FROM `image_log`
WHERE create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

## 📈 数据统计查询示例

### 1. 按分类统计图片数量和大小

```sql
SELECT 
    category,
    CASE category 
        WHEN 1 THEN '组件图标'
        WHEN 2 THEN '用户上传'
        WHEN 3 THEN '系统图片'
        WHEN 4 THEN '模板缩略图'
    END AS category_name,
    COUNT(*) as count,
    ROUND(SUM(size)/1024/1024, 2) as size_mb
FROM image 
WHERE status = 0
GROUP BY category;
```

### 2. 查询最活跃的上传者

```sql
SELECT 
    uploader_id,
    uploader_name,
    COUNT(*) as upload_count,
    ROUND(SUM(size)/1024/1024, 2) as total_size_mb,
    MIN(create_time) as first_upload,
    MAX(create_time) as last_upload
FROM image 
WHERE status = 0
GROUP BY uploader_id, uploader_name
ORDER BY upload_count DESC
LIMIT 10;
```

### 3. 查询操作频率统计

```sql
SELECT 
    DATE(create_time) as date,
    operation_type,
    COUNT(*) as count,
    COUNT(CASE WHEN result = 'SUCCESS' THEN 1 END) as success_count
FROM image_log 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(create_time), operation_type
ORDER BY date DESC, count DESC;
```

### 4. 查询存储空间使用情况

```sql
SELECT 
    bucket_name,
    COUNT(*) as file_count,
    ROUND(SUM(size)/1024/1024, 2) as total_size_mb,
    ROUND(AVG(size)/1024, 2) as avg_size_kb,
    MAX(size) as max_size,
    MIN(size) as min_size
FROM image 
WHERE status = 0
GROUP BY bucket_name;
```

## 🔒 数据安全建议

### 1. 备份策略
- 定期备份图片数据表
- 保留操作日志用于审计
- 建议使用主从复制

### 2. 权限控制
- 限制直接数据库访问权限
- 通过应用层API控制访问
- 记录所有敏感操作

### 3. 数据清理
- 定期清理过期的已删除图片
- 清理过期的操作日志
- 监控存储空间使用情况

## 📝 维护建议

### 1. 定期维护任务
- 每日执行数据清理
- 每周分析存储使用情况
- 每月检查索引性能

### 2. 监控指标
- 图片上传成功率
- 存储空间使用率
- 操作响应时间
- 错误日志数量

### 3. 性能优化
- 根据查询模式调整索引
- 定期分析慢查询
- 考虑分表分库策略

## 🚀 扩展建议

### 1. 功能扩展
- 图片缩略图生成
- 图片格式转换
- 图片水印添加
- 图片压缩优化

### 2. 技术扩展
- 支持多个MinIO集群
- 实现图片CDN分发
- 添加图片内容识别
- 支持图片版本管理

---

**文档版本**: 1.0  
**创建时间**: 2024-01-01  
**最后更新**: 2024-01-01  
**维护人员**: lowcode团队
