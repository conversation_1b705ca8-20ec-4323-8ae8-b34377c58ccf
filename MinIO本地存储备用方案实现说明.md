# MinIO本地存储备用方案实现说明

## 🎯 优化目标

当MinIO服务器没有启动时，自动将图片存储到本地路径，确保图片上传功能的可用性。

## 🏗️ 架构设计

### 存储策略
1. **自动选择模式（默认）**：优先使用MinIO，失败时自动切换到本地存储
2. **MinIO模式**：强制使用MinIO存储
3. **本地存储模式**：强制使用本地存储

### 核心组件

```
FileStorageService (统一存储接口)
├── MinioService (MinIO存储实现)
└── LocalFileService (本地存储实现)
```

## 📁 新增文件结构

```
src/main/java/com/web/lowcode/
├── config/
│   └── FileStorageConfig.java          # 文件存储配置
├── service/
│   ├── FileStorageService.java         # 统一存储服务接口
│   └── LocalFileService.java           # 本地存储服务接口
├── service/impl/
│   ├── FileStorageServiceImpl.java     # 统一存储服务实现
│   └── LocalFileServiceImpl.java       # 本地存储服务实现
└── controller/
    └── FileController.java             # 文件访问控制器
```

## ⚙️ 配置说明

### application.properties 新增配置

```properties
# 文件存储配置
file.storage.type=auto                                    # 存储类型：auto/minio/local
file.storage.local.base-path=./uploads                    # 本地存储基础路径
file.storage.local.url-prefix=http://localhost:8080/api/files  # 本地文件访问URL前缀
file.storage.fallback-to-local=true                       # 是否在MinIO不可用时回退到本地存储
```

### 配置选项详解

- **file.storage.type**：
  - `auto`：自动选择（推荐），优先MinIO，失败时使用本地存储
  - `minio`：强制使用MinIO
  - `local`：强制使用本地存储

- **file.storage.fallback-to-local**：
  - `true`：启用回退机制（推荐）
  - `false`：禁用回退，MinIO失败时直接报错

## 🔄 工作流程

### 上传流程

```mermaid
graph TD
    A[文件上传请求] --> B{检查存储配置}
    B -->|auto| C{MinIO可用?}
    B -->|minio| D[使用MinIO]
    B -->|local| E[使用本地存储]
    
    C -->|是| D
    C -->|否| E
    
    D --> F{上传成功?}
    F -->|是| G[返回MinIO URL]
    F -->|否| H{允许回退?}
    
    H -->|是| E
    H -->|否| I[上传失败]
    
    E --> J{本地上传成功?}
    J -->|是| K[返回本地URL]
    J -->|否| I
```

### URL生成规则

- **MinIO存储**：`http://localhost:9000/lowcode-images/uploads/2024/01/15/xxx.png`
- **本地存储**：`http://localhost:8080/api/files/uploads/2024/01/15/xxx.png`

## 📂 本地存储目录结构

```
./uploads/
├── icons/          # 组件图标 (category=1)
├── uploads/        # 用户上传 (category=2)
├── system/         # 系统图片 (category=3)
├── templates/      # 模板缩略图 (category=4)
└── others/         # 其他类型 (category=其他)
```

每个分类下按日期组织：
```
uploads/
└── 2024/
    └── 01/
        └── 15/
            ├── abc123.png
            └── def456.jpg
```

## 🔧 核心功能

### 1. 自动切换机制

```java
// 自动检测MinIO可用性
if (isMinioAvailable()) {
    return minioService.uploadFile(file, path);
} else {
    return localFileService.uploadFile(file, path);
}
```

### 2. 回退机制

```java
try {
    return minioService.uploadFile(file, path);
} catch (Exception e) {
    if (fallbackToLocal) {
        return localFileService.uploadFile(file, path);
    }
    throw e;
}
```

### 3. 统一文件访问

- MinIO文件：直接通过MinIO URL访问
- 本地文件：通过 `/api/files/**` 接口访问

## 🚀 使用方法

### 1. 启动应用（无需MinIO）

```bash
# 只启动Spring Boot应用
mvn spring-boot:run

# MinIO不启动也可以正常上传图片到本地
```

### 2. 测试存储功能

访问测试页面：`http://localhost:3000/test-upload`

1. 点击"测试存储连接"查看当前存储状态
2. 上传图片测试自动切换功能
3. 查看日志了解存储类型选择过程

### 3. 查看存储状态

```javascript
// 前端调用
const response = await request({
  url: '/images/test/minio',
  method: 'get'
})

// 返回结果包含：
{
  "currentStorageType": "local",
  "minioAvailable": false,
  "localAvailable": true,
  "fallbackToLocal": true,
  "configuredType": "auto"
}
```

## 📊 监控和日志

### 日志信息

```log
# 存储类型选择
INFO: MinIO不可用，使用本地存储
INFO: 文件上传成功，生成URL: http://localhost:8080/api/files/uploads/2024/01/15/xxx.png, 存储类型: local

# 回退机制
WARN: MinIO上传失败，回退到本地存储: uploads/2024/01/15/xxx.png
INFO: 本地存储上传成功: uploads/2024/01/15/xxx.png
```

### 数据库记录

图片记录的 `remark` 字段会自动添加存储类型信息：
```
"用户上传的头像 [存储: local]"
"项目封面图 [存储: minio]"
```

## 🔍 故障排除

### 常见问题

1. **本地存储目录权限问题**
   ```bash
   # 确保应用有写权限
   chmod 755 ./uploads
   ```

2. **URL访问404**
   - 检查 `file.storage.local.url-prefix` 配置
   - 确认 `FileController` 正常工作

3. **MinIO回退不生效**
   - 检查 `file.storage.fallback-to-local=true`
   - 查看日志确认MinIO连接失败

### 调试步骤

1. 查看存储服务状态：访问测试页面
2. 检查配置：确认 `application.properties` 配置正确
3. 查看日志：观察存储类型选择和切换过程
4. 测试文件访问：直接访问生成的URL

## 🎉 优势特性

### 1. 高可用性
- MinIO故障时自动切换到本地存储
- 确保图片上传功能始终可用

### 2. 透明切换
- 对前端完全透明
- 统一的API接口
- 自动生成正确的访问URL

### 3. 灵活配置
- 支持多种存储策略
- 可配置回退机制
- 运行时动态检测

### 4. 完整功能
- 支持上传、下载、删除、复制
- 文件信息查询
- 存在性检查

## 📈 性能考虑

### 本地存储优势
- 无网络延迟
- 不依赖外部服务
- 读写速度快

### MinIO存储优势
- 分布式存储
- 更好的扩展性
- 专业的对象存储功能

---

**实现状态**: ✅ 已完成  
**测试状态**: ⏳ 待验证  
**兼容性**: 完全向后兼容
