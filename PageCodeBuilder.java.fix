    /**
     * 添加组件事件
     * @param componentConfig 组件配置
     */
    private void appendEvents(Map<String, Object> componentConfig) {
        // 检查组件是否有事件配置
        if (!componentConfig.containsKey("events")) {
            logger.debug("Component has no events property: {}", componentConfig.get("id"));
            return;
        }

        Object eventsObj = componentConfig.get("events");
        if (eventsObj == null) {
            logger.debug("Component events is null: {}", componentConfig.get("id"));
            return;
        }

        logger.info("Processing events for component: {}, events: {}", componentConfig.get("id"), eventsObj);

        List<Map<String, Object>> events;

        // 如果是字符串，尝试解析为JSON
        if (eventsObj instanceof String) {
            try {
                logger.debug("Parsing events string: {}", eventsObj);
                events = JsonUtils.parseObject((String) eventsObj, List.class);
                if (events == null) {
                    logger.warn("Failed to parse events, result is null: {}", eventsObj);
                    return;
                }
                logger.debug("Parsed events: {}", events);
            } catch (Exception e) {
                logger.error("Failed to parse events JSON: {}", eventsObj, e);
                return;
            }
        } else if (eventsObj instanceof List) {
            logger.debug("Events is already a list: {}", eventsObj);
            events = (List<Map<String, Object>>) eventsObj;
        } else {
            logger.warn("Unexpected events type: {}", eventsObj.getClass().getName());
            return;
        }

        // 生成事件处理代码
        for (Map<String, Object> event : events) {
            String eventType = (String) event.get("type");
            Map<String, Object> action = (Map<String, Object>) event.get("action");

            logger.info("Processing event: type={}, action={}", eventType, action);

            if (eventType == null || action == null) {
                logger.warn("Invalid event configuration: eventType={}, action={}", eventType, action);
                continue;
            }

            String actionType = (String) action.get("type");
            if (actionType == null) {
                logger.warn("Missing action type in event: {}", event);
                continue;
            }

            // 开始事件处理器 - 使用正确的Vue事件绑定语法
            code.append(" @").append(eventType).append("=\"");
            
            // 使用匿名函数包装事件处理代码
            code.append("() => {");
            
            // 添加调试日志
            code.append("console.log('Event triggered: ").append(eventType).append("');");

            // 根据不同的动作类型生成不同的代码
            switch (actionType) {
                case "navigate":
                    String navigationType = (String) action.get("navigationType");
                    if ("page".equals(navigationType)) {
                        Object pageIdObj = action.get("pageId");
                        if (pageIdObj != null) {
                            // 确保 pageId 是字符串类型
                            String pageId = String.valueOf(pageIdObj);
                            code.append("console.log('Navigating to page ID: ").append(pageId).append("');");
                            // 使用Vue Router导航
                            code.append("navigateTo({ name: '").append(pageId).append("' });");
                        } else {
                            code.append("console.warn('Missing pageId for navigation');");
                            code.append("ElMessage.warning('缺少页面ID，无法跳转');");
                        }
                    } else if ("url".equals(navigationType)) {
                        String url = (String) action.get("url");
                        if (url != null && !url.isEmpty()) {
                            code.append("console.log('Opening URL: ").append(url).append("');");
                            code.append("window.open('").append(url).append("', '_blank');");
                        } else {
                            code.append("console.warn('Missing URL for navigation');");
                            code.append("ElMessage.warning('缺少URL，无法跳转');");
                        }
                    } else {
                        code.append("console.warn('Unknown navigation type: ").append(navigationType != null ? navigationType : "null").append("');");
                        code.append("ElMessage.warning('未知的导航类型');");
                    }
                    break;
                case "message":
                    String message = (String) action.get("message");
                    String messageType = (String) action.get("messageType");
                    if (message != null && !message.isEmpty()) {
                        // 使用Element Plus的消息组件
                        if (messageType == null || "info".equals(messageType)) {
                            code.append("ElMessage.info('").append(message).append("');");
                        } else if ("success".equals(messageType)) {
                            code.append("ElMessage.success('").append(message).append("');");
                        } else if ("warning".equals(messageType)) {
                            code.append("ElMessage.warning('").append(message).append("');");
                        } else if ("error".equals(messageType)) {
                            code.append("ElMessage.error('").append(message).append("');");
                        }
                    }
                    break;
                case "toggleComponent":
                    Object targetComponentId = action.get("targetComponentId");
                    String operation = (String) action.get("operation");
                    if (targetComponentId != null) {
                        code.append("console.log('Toggle component: ").append(targetComponentId).append(", operation: ").append(operation).append("');");
                        // 这里可以添加切换组件可见性的代码
                        code.append("// 组件切换功能在导出项目中暂未实现");
                    }
                    break;
                case "api":
                    String apiUrl = (String) action.get("apiUrl");
                    String apiMethod = (String) action.get("apiMethod");
                    if (apiUrl != null && !apiUrl.isEmpty()) {
                        code.append("console.log('API call: ").append(apiMethod).append(" ").append(apiUrl).append("');");
                        // 这里可以添加API调用的代码
                        code.append("// API调用功能在导出项目中暂未实现");
                    }
                    break;
                case "javascript":
                    String jsCode = (String) action.get("jsCode");
                    if (jsCode != null && !jsCode.isEmpty() && !jsCode.equals("// 在这里编写JavaScript代码")) {
                        // 直接嵌入JavaScript代码
                        code.append(jsCode);
                    } else {
                        code.append("console.log('Empty JavaScript code');");
                    }
                    break;
                default:
                    code.append("console.log('Unsupported action type: ").append(actionType).append("');");
            }

            // 结束匿名函数
            code.append("}\"");

            // 添加数据属性以便于调试 - 这些不会影响功能，但有助于调试
            code.append(" data-event-type=\"").append(eventType).append("\"");
            code.append(" data-action-type=\"").append(actionType).append("\"");

            // 根据不同的动作类型添加额外的数据属性
            switch (actionType) {
                case "navigate":
                    Object pageIdObj = action.get("pageId");
                    if (pageIdObj != null) {
                        code.append(" data-page-id=\"").append(pageIdObj).append("\"");
                    }
                    break;
                case "message":
                    String message = (String) action.get("message");
                    if (message != null) {
                        code.append(" data-message=\"").append(message).append("\"");
                    }
                    break;
                // 其他动作类型的数据属性...
            }
        }
    }
