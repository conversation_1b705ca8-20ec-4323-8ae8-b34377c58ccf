# MinIO图片管理系统 - 编译验证和使用指南

## 🔧 编译问题修复

### 已修复的问题

1. **SecurityUtils类找不到**
   - ✅ 修复了导入路径：从 `com.web.lowcode.util.SecurityUtils` 改为 `com.web.lowcode.security.SecurityUtils`
   - ✅ 扩展了SecurityUtils类，添加了所需的方法：
     - `getCurrentUserId()` - 获取当前用户ID
     - `getCurrentUserName()` - 获取当前用户名
     - `getCurrentUser()` - 获取当前用户实体

2. **Servlet API版本问题**
   - ✅ 修复了 `javax.servlet` 到 `jakarta.servlet` 的导入问题
   - ✅ 修复了 `javax.annotation` 到 `jakarta.annotation` 的导入问题

## 🚀 启动步骤

### 第一步：启动MinIO服务

```bash
# 使用Docker启动MinIO
docker run -p 9000:9000 -p 9001:9001 \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  minio/minio server /data --console-address ":9001"
```

### 第二步：创建数据库表

```sql
-- 连接到MySQL数据库
mysql -u root -p

-- 选择数据库
USE lowcode;

-- 执行简化版本脚本（推荐用于MySQL 8.0.17）
SOURCE src/main/resources/db/image_tables_simple.sql;
```

### 第三步：验证数据库

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE '%image%';

-- 检查表结构
DESCRIBE image;
DESCRIBE image_log;
```

### 第四步：启动后端服务

```bash
# 编译项目
mvn clean compile

# 启动Spring Boot应用
mvn spring-boot:run
```

### 第五步：启动前端服务

```bash
# 进入前端目录
cd low-code-vue

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev
```

## 📋 功能验证清单

### 后端API验证

1. **MinIO连接测试**
   ```bash
   # 访问MinIO控制台
   http://localhost:9001
   # 用户名: minioadmin
   # 密码: minioadmin
   ```

2. **API接口测试**
   ```bash
   # 查看Swagger文档
   http://localhost:8080/swagger-ui.html
   
   # 测试图片上传API
   POST http://localhost:8080/api/images/upload
   ```

3. **数据库连接测试**
   ```sql
   -- 测试插入数据
   INSERT INTO image (name, original_name, extension, size, content_type, minio_path, bucket_name, url, md5, uploader_id, uploader_name) 
   VALUES ('test.jpg', 'test.jpg', 'jpg', 1024, 'image/jpeg', 'test/test.jpg', 'lowcode-images', 'http://localhost:9000/test.jpg', 'test123', 1, 'admin');
   
   -- 查看数据
   SELECT * FROM image;
   ```

### 前端功能验证

1. **访问图片管理页面**
   ```
   http://localhost:5173/image-management
   ```

2. **测试功能**
   - [ ] 图片列表显示
   - [ ] 图片上传功能
   - [ ] 图片搜索和筛选
   - [ ] 图片详情查看
   - [ ] 图片下载功能
   - [ ] 图片删除和恢复

## 🔍 常见问题排查

### 1. 编译错误

**问题**: `SecurityUtils`类找不到
**解决**: 已修复，确保使用正确的导入路径

**问题**: Servlet API版本问题
**解决**: 已修复，使用Jakarta EE规范

### 2. 运行时错误

**问题**: MinIO连接失败
**解决**: 
```bash
# 检查MinIO是否启动
docker ps | grep minio

# 检查端口是否被占用
netstat -an | grep 9000
```

**问题**: 数据库连接失败
**解决**:
```properties
# 检查application.properties中的数据库配置
spring.datasource.url=*********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
```

### 3. 前端错误

**问题**: API调用失败
**解决**:
```javascript
// 检查API基础URL配置
// 确保后端服务已启动
// 检查跨域配置
```

## 📊 性能监控

### 1. 数据库性能

```sql
-- 查看图片数量统计
SELECT 
    category,
    COUNT(*) as count,
    ROUND(SUM(size)/1024/1024, 2) as size_mb
FROM image 
WHERE status = 0
GROUP BY category;

-- 查看操作日志统计
SELECT 
    operation_type,
    COUNT(*) as count,
    COUNT(CASE WHEN result = 'SUCCESS' THEN 1 END) as success_count
FROM image_log 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY operation_type;
```

### 2. MinIO存储监控

```bash
# 查看MinIO存储使用情况
# 访问MinIO控制台查看存储桶信息
http://localhost:9001
```

## 🛠️ 维护任务

### 1. 定期清理

```sql
-- 清理30天前删除的图片记录
DELETE FROM image 
WHERE status = 1 
  AND delete_time IS NOT NULL 
  AND delete_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理90天前的操作日志
DELETE FROM image_log 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 2. 备份建议

```bash
# 数据库备份
mysqldump -u root -p lowcode > lowcode_backup_$(date +%Y%m%d).sql

# MinIO数据备份
# 使用MinIO客户端工具进行备份
```

## 🎯 下一步开发建议

1. **功能增强**
   - 图片缩略图生成
   - 图片格式转换
   - 图片水印添加
   - 批量操作优化

2. **性能优化**
   - 图片CDN集成
   - 缓存策略优化
   - 数据库索引优化
   - 异步处理优化

3. **安全增强**
   - 文件类型严格验证
   - 图片内容安全检查
   - 访问权限细化
   - 操作审计增强

---

**注意**: 如果遇到任何编译或运行问题，请检查：
1. Java版本是否为17
2. MySQL版本是否为8.0.17
3. Node.js版本是否兼容
4. 端口是否被占用
5. 防火墙设置是否正确
