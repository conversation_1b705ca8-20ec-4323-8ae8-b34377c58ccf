# API路径重复问题修复说明

## 🚨 问题描述

前端请求后端接口时出现路径重复问题：
```
实际请求: http://localhost:3000/api/api/images/upload
期望请求: http://localhost:3000/api/images/upload
```

## 🔧 问题原因

### 根本原因
在前端API配置中存在路径重复：

1. **baseURL配置**: `index.js`中设置了`baseURL: '/api'`
2. **API路径**: `image.js`中的URL也包含了`/api`前缀

### 具体分析
```javascript
// index.js - axios实例配置
const request = axios.create({
  baseURL: '/api',  // 基础URL
  timeout: 10000
})

// image.js - API路径配置（修复前）
export function uploadImage(formData) {
  return request({
    url: '/api/images/upload',  // ❌ 重复的/api前缀
    method: 'post',
    data: formData
  })
}

// 最终请求URL = baseURL + url = '/api' + '/api/images/upload' = '/api/api/images/upload'
```

## ✅ 修复方案

### 修复原则
移除`image.js`中所有URL的`/api`前缀，让axios实例的baseURL自动添加。

### 修复前后对比

#### 修复前（错误）:
```javascript
export function uploadImage(formData) {
  return request({
    url: '/api/images/upload',  // ❌ 包含/api前缀
    method: 'post',
    data: formData
  })
}
```

#### 修复后（正确）:
```javascript
export function uploadImage(formData) {
  return request({
    url: '/images/upload',  // ✅ 移除/api前缀
    method: 'post',
    data: formData
  })
}
```

## 📋 修复清单

### 已修复的API端点

| 功能 | 修复前 | 修复后 |
|------|--------|--------|
| 上传单个图片 | `/api/images/upload` | `/images/upload` |
| 批量上传图片 | `/api/images/upload/batch` | `/images/upload/batch` |
| 分页查询图片 | `/api/images/page` | `/images/page` |
| 获取图片详情 | `/api/images/{id}` | `/images/{id}` |
| 根据业务查询 | `/api/images/business` | `/images/business` |
| 更新图片信息 | `/api/images/{id}` | `/images/{id}` |
| 删除图片 | `/api/images/{id}` | `/images/{id}` |
| 批量删除 | `/api/images/batch` | `/images/batch` |
| 恢复图片 | `/api/images/{id}/restore` | `/images/{id}/restore` |
| 永久删除 | `/api/images/{id}/permanent` | `/images/{id}/permanent` |
| 复制图片 | `/api/images/{id}/copy` | `/images/{id}/copy` |
| 下载图片 | `/api/images/{id}/download` | `/images/{id}/download` |
| 图片统计 | `/api/images/statistics` | `/images/statistics` |
| 分类统计 | `/api/images/statistics/category/{category}` | `/images/statistics/category/{category}` |

### 图片日志API端点

| 功能 | 修复前 | 修复后 |
|------|--------|--------|
| 分页查询日志 | `/api/image-logs/page` | `/image-logs/page` |
| 根据图片ID查询日志 | `/api/image-logs/image/{imageId}` | `/image-logs/image/{imageId}` |
| 统计操作次数 | `/api/image-logs/count` | `/image-logs/count` |
| 操作统计信息 | `/api/image-logs/statistics` | `/image-logs/statistics` |
| 清理过期日志 | `/api/image-logs/clean` | `/image-logs/clean` |

## 🔍 验证修复

### 1. 检查请求URL
在浏览器开发者工具的Network标签中，确认请求URL为：
```
✅ http://localhost:3000/api/images/upload
❌ http://localhost:3000/api/api/images/upload
```

### 2. 测试步骤
1. **重启前端服务**:
   ```bash
   cd low-code-vue
   npm run dev
   ```

2. **清除浏览器缓存**:
   - 按 `Ctrl+Shift+R` 强制刷新
   - 或在开发者工具中清空缓存

3. **测试图片上传**:
   - 访问包含ImageUploader组件的页面
   - 选择图片进行上传
   - 检查Network标签中的请求URL

## 🎯 API路径规范

### 当前项目的API路径规范

#### 1. axios实例配置
```javascript
// index.js
const request = axios.create({
  baseURL: '/api',  // 统一的API基础路径
  timeout: 10000
})
```

#### 2. API文件中的路径规范
```javascript
// ✅ 正确的路径写法（不包含/api前缀）
export function someApi() {
  return request({
    url: '/resource/action',  // 相对于baseURL的路径
    method: 'get'
  })
}

// ❌ 错误的路径写法（包含/api前缀）
export function someApi() {
  return request({
    url: '/api/resource/action',  // 会导致路径重复
    method: 'get'
  })
}
```

#### 3. 最终请求URL构成
```
最终URL = baseURL + url
例如: '/api' + '/images/upload' = '/api/images/upload'
```

## 🚀 其他API文件检查

### 需要检查的文件
确保以下文件中的API路径都遵循规范：

- [x] `image.js` - 已修复
- [x] `componentEnable.js` - 已检查，正确
- [x] `component.js` - 已检查，正确
- [x] `code.js` - 已检查，正确
- [x] `icon.js` - 已检查，正确

### 检查方法
```bash
# 搜索可能存在问题的API路径
grep -r "url: '/api/" low-code-vue/src/api/
```

## 📝 最佳实践

### 1. API路径命名规范
```javascript
// 推荐的路径格式
url: '/resource'           // 获取资源列表
url: '/resource/{id}'      // 获取单个资源
url: '/resource/action'    // 执行特定操作
url: '/resource/{id}/action' // 对特定资源执行操作
```

### 2. 避免路径重复
- 在axios实例中设置baseURL
- API文件中的路径不包含baseURL部分
- 保持路径的一致性和可读性

### 3. 调试技巧
- 使用浏览器开发者工具检查实际请求URL
- 在axios拦截器中打印请求信息
- 确保前后端路径匹配

---

**修复状态**: ✅ 已完成  
**测试状态**: ⏳ 待验证  
**影响范围**: 图片管理相关的所有API请求
