# MySQL 8.0.17 图片管理系统安装指南

## 🚨 重要提示

由于您使用的是MySQL 8.0.17版本，在创建存储过程时可能会遇到语法兼容性问题。我们为您提供了专门的解决方案。

## 📋 安装步骤

### 第一步：选择合适的SQL脚本

我们提供了三个版本的SQL脚本，请根据您的需求选择：

#### 1. 推荐方案：简化版本（无存储过程）
```bash
# 文件位置
src/main/resources/db/image_tables_simple.sql
```
**优点**：
- ✅ 完全兼容MySQL 8.0.17
- ✅ 包含所有必要的表结构和索引
- ✅ 无语法兼容性问题
- ✅ 包含详细的查询示例

**缺点**：
- ❌ 需要手动执行数据清理

#### 2. 兼容版本（修复后的存储过程）
```bash
# 文件位置  
src/main/resources/db/image_tables_mysql8.sql
```
**优点**：
- ✅ 针对MySQL 8.0.17优化
- ✅ 包含修复后的存储过程
- ✅ 包含视图和函数

**可能问题**：
- ⚠️ 某些MySQL配置可能仍有问题

#### 3. 完整版本（原始版本）
```bash
# 文件位置
src/main/resources/db/image_tables.sql
```
**注意**：此版本可能在MySQL 8.0.17上出现语法错误

### 第二步：执行SQL脚本

#### 方案A：使用简化版本（推荐）

```sql
-- 1. 连接到MySQL数据库
mysql -u root -p

-- 2. 选择数据库
USE lowcode;

-- 3. 执行简化版本脚本
SOURCE /path/to/src/main/resources/db/image_tables_simple.sql;
```

#### 方案B：使用兼容版本

```sql
-- 1. 连接到MySQL数据库
mysql -u root -p

-- 2. 选择数据库  
USE lowcode;

-- 3. 执行兼容版本脚本
SOURCE /path/to/src/main/resources/db/image_tables_mysql8.sql;
```

### 第三步：验证安装

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE '%image%';

-- 检查表结构
DESCRIBE image;
DESCRIBE image_log;

-- 检查索引
SHOW INDEX FROM image;
SHOW INDEX FROM image_log;

-- 测试插入数据
INSERT INTO image (name, original_name, extension, size, content_type, minio_path, bucket_name, url, md5, uploader_id, uploader_name) 
VALUES ('test.jpg', 'test.jpg', 'jpg', 1024, 'image/jpeg', 'test/test.jpg', 'lowcode-images', 'http://localhost:9000/test.jpg', 'test123', 1, 'admin');

-- 检查数据
SELECT * FROM image;
```

## 🔧 常见问题解决

### 问题1：存储过程创建失败

**错误信息**：
```
ERROR 1064 (42000): You have an error in your SQL syntax
```

**解决方案**：
1. 使用简化版本脚本（推荐）
2. 或者手动创建存储过程：

```sql
DELIMITER $$
DROP PROCEDURE IF EXISTS sp_clean_expired_image_data$$
CREATE PROCEDURE sp_clean_expired_image_data(
    IN p_image_days INT,
    IN p_log_days INT
)
BEGIN
    -- 设置默认值
    IF p_image_days IS NULL THEN SET p_image_days = 30; END IF;
    IF p_log_days IS NULL THEN SET p_log_days = 90; END IF;
    
    -- 清理过期图片
    DELETE FROM image 
    WHERE status = 1 
      AND delete_time < DATE_SUB(NOW(), INTERVAL p_image_days DAY);
    
    -- 清理过期日志
    DELETE FROM image_log 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL p_log_days DAY);
    
    -- 返回结果
    SELECT ROW_COUNT() as cleaned_records;
END$$
DELIMITER ;
```

### 问题2：字符集问题

**错误信息**：
```
ERROR 1273 (HY000): Unknown collation: 'utf8mb4_unicode_ci'
```

**解决方案**：
```sql
-- 检查支持的字符集
SHOW COLLATION LIKE 'utf8mb4%';

-- 如果不支持utf8mb4_unicode_ci，使用utf8mb4_general_ci
ALTER TABLE image CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER TABLE image_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

### 问题3：权限问题

**错误信息**：
```
ERROR 1227 (42000): Access denied; you need (at least one of) the SUPER privilege(s)
```

**解决方案**：
```sql
-- 使用管理员权限连接
mysql -u root -p

-- 或者授予必要权限
GRANT ALL PRIVILEGES ON lowcode.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

## 🛠️ 手动数据清理

如果选择简化版本（无存储过程），可以使用以下SQL进行定期清理：

### 清理脚本

```sql
-- 1. 清理30天前删除的图片记录
DELETE FROM image 
WHERE status = 1 
  AND delete_time IS NOT NULL 
  AND delete_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 2. 清理90天前的操作日志
DELETE FROM image_log 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 3. 查看清理结果
SELECT 
    (SELECT COUNT(*) FROM image WHERE status = 1) as deleted_images,
    (SELECT COUNT(*) FROM image WHERE status = 0) as active_images,
    (SELECT COUNT(*) FROM image_log) as total_logs;
```

### 定期清理建议

1. **创建定时任务**（Linux/Mac）：
```bash
# 编辑crontab
crontab -e

# 添加每周清理任务（每周日凌晨2点执行）
0 2 * * 0 mysql -u username -p'password' lowcode < /path/to/cleanup.sql
```

2. **Windows任务计划**：
   - 打开"任务计划程序"
   - 创建基本任务
   - 设置触发器为每周
   - 操作为执行MySQL清理脚本

## 📊 性能优化建议

### 1. 索引优化
```sql
-- 检查索引使用情况
EXPLAIN SELECT * FROM image WHERE category = 1 AND status = 0;

-- 如果查询慢，可以添加额外索引
CREATE INDEX idx_custom ON image (category, status, create_time);
```

### 2. 分区表（大数据量时）
```sql
-- 如果数据量很大，可以考虑按时间分区
ALTER TABLE image_log 
PARTITION BY RANGE (YEAR(create_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. 配置优化
```sql
-- 检查MySQL配置
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'max_connections';

-- 建议配置（根据服务器内存调整）
-- innodb_buffer_pool_size = 1G
-- max_connections = 200
```

## ✅ 验证清单

安装完成后，请检查以下项目：

- [ ] 表 `image` 创建成功
- [ ] 表 `image_log` 创建成功  
- [ ] 所有索引创建成功
- [ ] 可以正常插入测试数据
- [ ] 可以正常查询数据
- [ ] 字符集设置正确（支持中文）
- [ ] 权限配置正确
- [ ] 应用程序可以连接数据库

## 🆘 获取帮助

如果遇到其他问题，请：

1. 检查MySQL错误日志
2. 确认MySQL版本：`SELECT VERSION();`
3. 检查表结构：`SHOW CREATE TABLE image;`
4. 提供具体的错误信息

---

**注意**：本指南专门针对MySQL 8.0.17版本，其他版本可能需要不同的处理方式。
