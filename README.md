# 低代码可拖拽生成页面的平台

基于uniapp开发的低代码可拖拽生成页面的平台，支持多端适配与发布。

## 项目介绍

本项目是一个低代码开发平台，通过可视化拖拽的方式快速生成页面，支持多端适配，可一键生成H5、小程序、APP多端代码。

### 技术栈

#### 后端技术栈
- JDK 17
- Spring Boot 3.4.2
- MyBatis-Plus
- Druid
- MySQL
- Swagger3

#### 前端技术栈
- Vue3
- UniApp
- VueDraggable/SortableJS
- Pinia
- Element Plus/Uni-UI

## 功能特性

### 基础功能
- 可视化拖拽编辑
- 组件库（按钮、表单、列表、轮播图等）
- 自由拖拽布局（网格、流式、绝对定位）
- 实时预览（PC/移动端多端适配）
- 组件属性配置
- 样式调整（颜色、尺寸、间距等）
- 数据绑定（静态数据、动态API接口）
- 事件交互（点击、滑动等触发动作）

### 多端适配与发布
- 一键生成H5、小程序、APP多端代码
- 自动处理跨端兼容性问题

### 页面管理与数据源
- 页面树形结构管理（增删改查）
- 数据源管理（本地JSON、REST API对接）

### 模板与扩展能力
- 预制模板（行业场景模板库）
- 自定义组件扩展（支持用户上传组件）

### 代码导出与协作
- 导出Vue/UniApp标准代码
- 团队协作（权限管理、版本历史）

## 项目结构

```
low-code/
├── src/                           # 后端源码
│   ├── main/
│   │   ├── java/com/web/lowcode/  # Java代码
│   │   │   ├── common/            # 通用类
│   │   │   ├── config/            # 配置类
│   │   │   ├── controller/        # 控制器
│   │   │   ├── entity/            # 实体类
│   │   │   ├── mapper/            # MyBatis接口
│   │   │   ├── service/           # 服务接口及实现
│   │   │   └── LowCodeApplication.java  # 启动类
│   │   └── resources/             # 资源文件
│   │       ├── mapper/            # MyBatis XML
│   │       ├── db/                # 数据库脚本
│   │       └── application.properties  # 配置文件
│   └── test/                      # 测试代码
├── low-code-web/                  # 前端源码
│   ├── src/
│   │   ├── api/                   # API接口
│   │   ├── components/            # 公共组件
│   │   ├── pages/                 # 页面
│   │   ├── static/                # 静态资源
│   │   ├── store/                 # 状态管理
│   │   ├── App.vue                # 应用入口
│   │   ├── main.js                # 主入口
│   │   └── pages.json             # 页面配置
│   ├── package.json               # 依赖配置
│   └── vite.config.js             # 构建配置
├── pom.xml                        # Maven配置
└── README.md                      # 项目说明
```

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 5.7+
- Node.js 16+

### 后端启动
1. 创建数据库 `low-code`
2. 执行 `src/main/resources/db/schema.sql` 初始化数据库
3. 修改 `application.properties` 中的数据库连接信息
4. 运行 `LowCodeApplication.java` 启动后端服务

### 前端启动
1. 进入前端目录 `cd low-code-web`
2. 安装依赖 `npm install`
3. 启动开发服务器 `npm run dev:h5`

## 部署说明

### 后端部署
1. 执行 `mvn clean package` 打包
2. 将生成的 jar 包上传到服务器
3. 执行 `java -jar low-code-0.0.1-SNAPSHOT.jar` 启动服务

### 前端部署
1. 执行 `npm run build:h5` 构建H5版本
2. 将 `dist/build/h5` 目录下的文件部署到Web服务器

## 贡献指南

1. Fork 本仓库
2. 创建新的分支 `git checkout -b feature/your-feature`
3. 提交你的更改 `git commit -m 'Add some feature'`
4. 推送到分支 `git push origin feature/your-feature`
5. 提交 Pull Request

## 许可证

[MIT](LICENSE)
