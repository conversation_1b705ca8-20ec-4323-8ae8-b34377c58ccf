/**
 * 全局配置文件
 * 用于存储应用程序的全局配置，如API基础URL、环境变量等
 */

// 环境变量
const ENV = process.env.NODE_ENV || 'development';

// 不同环境的配置
const CONFIG = {
  // 开发环境配置
  development: {
    // API基础URL
    apiBaseUrl: '/api',
    // 是否启用API请求日志
    enableApiLog: true,
    // 是否启用模拟数据
    enableMock: false,
    // 超时时间（毫秒）
    timeout: 10000,
  },

  // 测试环境配置
  test: {
    apiBaseUrl: '/api',
    enableApiLog: true,
    enableMock: false,
    timeout: 15000,
  },

  // 生产环境配置
  production: {
    apiBaseUrl: '/api',
    enableApiLog: false,
    enableMock: false,
    timeout: 20000,
  }
};

// 导出当前环境的配置
export default {
  // 当前环境
  env: ENV,
  // 是否为开发环境
  isDev: ENV === 'development',
  // 是否为测试环境
  isTest: ENV === 'test',
  // 是否为生产环境
  isProd: ENV === 'production',
  // 合并当前环境的配置
  ...CONFIG[ENV]
};
