/**
 * component-wrapper-fix.css
 * 这个CSS文件用于修复编辑模式下的组件包装div，使其在视觉上"消失"，但保留功能
 */

/* 
 * 使component-wrapper在视觉上"消失"
 * display: contents 使元素在渲染树中"消失"，但其子元素保持不变
 * 这样可以保留元素的功能，同时不影响布局
 */
.component-wrapper {
  display: contents !important;
}

/* 
 * 使event-handler-wrapper在视觉上"消失"
 */
.event-handler-wrapper {
  display: contents !important;
}

/* 
 * 确保button-component保持原有样式
 * 可能需要将原本应用在包装div上的一些样式移动到这里
 */
.button-component {
  /* 添加必要的样式，确保与预览模式一致 */
  display: inline-block;
  position: relative;
}

/* 
 * 如果在预览模式下有特定的样式，可以在这里覆盖
 * 例如，如果预览模式下的按钮有不同的边距或内边距
 */
.preview-mode .button-component,
.preview .button-component,
#preview .button-component {
  /* 预览模式特有的样式 */
}

/* 
 * 如果需要保留拖拽功能，确保draggable元素有正确的鼠标样式
 */
[draggable="true"] {
  cursor: move;
  user-select: none;
}
