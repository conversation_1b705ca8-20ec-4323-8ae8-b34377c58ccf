// 图片上传工具

/**
 * 将文件转换为Base64编码
 * @param {File} file - 要转换的文件
 * @returns {Promise<string>} - 返回Base64编码的字符串
 */
export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

/**
 * 将Base64编码转换为Blob对象
 * @param {string} base64 - Base64编码的字符串
 * @returns {Blob} - 返回Blob对象
 */
export function base64ToBlob(base64) {
  // 提取MIME类型和Base64数据
  const parts = base64.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  
  // 创建Uint8Array
  const uInt8Array = new Uint8Array(rawLength);
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  
  // 创建Blob对象
  return new Blob([uInt8Array], { type: contentType });
}

/**
 * 创建本地URL
 * @param {Blob|File} blob - Blob或File对象
 * @returns {string} - 返回本地URL
 */
export function createObjectURL(blob) {
  return URL.createObjectURL(blob);
}

/**
 * 释放本地URL
 * @param {string} url - 要释放的URL
 */
export function revokeObjectURL(url) {
  URL.revokeObjectURL(url);
}

/**
 * 图片上传服务
 */
class ImageUploadService {
  constructor() {
    // 存储上传的图片
    this.images = new Map();
  }
  
  /**
   * 上传图片
   * @param {File} file - 图片文件
   * @returns {Promise<string>} - 返回图片URL
   */
  async upload(file) {
    try {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        throw new Error('只能上传图片文件');
      }
      
      // 检查文件大小 (限制为5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('图片大小不能超过5MB');
      }
      
      // 转换为Base64
      const base64 = await fileToBase64(file);
      
      // 生成唯一ID
      const id = `img_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      
      // 存储图片
      this.images.set(id, {
        id,
        name: file.name,
        type: file.type,
        size: file.size,
        base64,
        url: base64
      });
      
      return base64;
    } catch (error) {
      console.error('图片上传失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取图片
   * @param {string} id - 图片ID
   * @returns {Object|null} - 返回图片对象或null
   */
  getImage(id) {
    return this.images.get(id) || null;
  }
  
  /**
   * 删除图片
   * @param {string} id - 图片ID
   * @returns {boolean} - 返回是否删除成功
   */
  deleteImage(id) {
    return this.images.delete(id);
  }
  
  /**
   * 获取所有图片
   * @returns {Array} - 返回所有图片对象的数组
   */
  getAllImages() {
    return Array.from(this.images.values());
  }
}

// 创建单例
const imageUploadService = new ImageUploadService();

export default imageUploadService;
