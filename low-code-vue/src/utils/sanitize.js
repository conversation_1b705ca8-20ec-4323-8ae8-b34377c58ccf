/**
 * Sanitizes an object to make it serializable for IndexedDB
 * Removes circular references, functions, and other non-serializable properties
 *
 * @param {Object} obj - The object to sanitize
 * @returns {Object} - A sanitized copy of the object
 */
export function sanitizeForIndexedDB(obj) {
  // Handle null or undefined
  if (obj === null || obj === undefined) {
    return obj;
  }

  // Handle primitive types
  if (typeof obj !== 'object') {
    return obj;
  }

  // Handle Date objects
  if (obj instanceof Date) {
    return new Date(obj);
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeForIndexedDB(item));
  }

  // Handle regular objects
  const sanitized = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];

      // Skip functions, symbols, and other non-serializable types
      if (typeof value === 'function' || typeof value === 'symbol') {
        continue;
      }

      // Skip DOM nodes
      if (value instanceof Node) {
        continue;
      }

      // Skip properties that start with underscore (often internal properties)
      if (key.startsWith('_')) {
        continue;
      }

      // Recursively sanitize nested objects
      sanitized[key] = sanitizeForIndexedDB(value);
    }
  }

  return sanitized;
}

/**
 * Sanitizes a component for storage in IndexedDB
 * Specifically handles Vue component properties
 *
 * @param {Object} component - The component to sanitize
 * @returns {Object} - A sanitized copy of the component
 */
export function sanitizeComponent(component) {
  if (!component) return null;

  // Create a basic sanitized component with only essential properties
  const sanitized = {
    id: component.id,
    type: component.type,
    props: {},
    styles: {},
    events: component.events ? JSON.parse(JSON.stringify(component.events)) : undefined
  };

  console.log('Sanitizing component:', component.id, component.type);
  if (component.events) {
    console.log('Component has events before sanitizing:', component.events);
  }

  // Sanitize props
  if (component.props) {
    sanitized.props = sanitizeForIndexedDB(component.props);
  }

  // Sanitize styles
  if (component.styles) {
    sanitized.styles = sanitizeForIndexedDB(component.styles);
  }

  // Sanitize events
  if (component.events) {
    console.log('Sanitizing events:', component.events);
    sanitized.events = sanitizeForIndexedDB(component.events);
    console.log('Sanitized events:', sanitized.events);
  }

  return sanitized;
}

export default {
  sanitizeForIndexedDB,
  sanitizeComponent
};
