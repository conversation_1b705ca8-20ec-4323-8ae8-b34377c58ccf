import { componentLibraryService } from '../services/db'
import { sanitizeForIndexedDB } from './sanitize'
import { ElMessage } from 'element-plus'

/**
 * Ensures that all default components are in the database
 * If a component doesn't exist in the database, it will be added
 * 
 * @param {Array} defaultComponents - The default components from the store
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function ensureDefaultComponentsInDb(defaultComponents) {
  try {
    // Get all components from the database
    const dbComponents = await componentLibraryService.getAllComponents()
    
    // Create a map of existing component types
    const existingComponentTypes = new Set(dbComponents.map(c => c.type))
    
    // Find components that don't exist in the database
    const missingComponents = defaultComponents.filter(c => !existingComponentTypes.has(c.type))
    
    if (missingComponents.length > 0) {
      console.log(`Adding ${missingComponents.length} missing components to the database`)
      
      // Add missing components to the database
      for (const component of missingComponents) {
        try {
          // Sanitize the component before saving
          const sanitizedComponent = sanitizeForIndexedDB(component)
          await componentLibraryService.saveComponent(sanitizedComponent)
        } catch (error) {
          console.error(`Failed to add component ${component.type} to database:`, error)
        }
      }
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('Failed to ensure default components in database:', error)
    return false
  }
}

/**
 * Adds a new component to the database
 * 
 * @param {Object} component - The component to add
 * @returns {Promise<Object>} - The saved component
 */
export async function addComponentToDb(component) {
  try {
    // Validate component
    if (!component.type) {
      throw new Error('Component must have a type')
    }
    
    if (!component.name) {
      throw new Error('Component must have a name')
    }
    
    // Check if component already exists
    const existingComponent = await componentLibraryService.getComponentByType(component.type)
    if (existingComponent) {
      throw new Error(`Component with type '${component.type}' already exists`)
    }
    
    // Sanitize the component before saving
    const sanitizedComponent = sanitizeForIndexedDB(component)
    
    // Save the component to the database
    const savedComponent = await componentLibraryService.saveComponent(sanitizedComponent)
    
    ElMessage.success(`Component '${component.name}' added successfully`)
    return savedComponent
  } catch (error) {
    console.error('Failed to add component to database:', error)
    ElMessage.error(error.message || 'Failed to add component to database')
    throw error
  }
}

/**
 * Updates an existing component in the database
 * 
 * @param {Object} component - The component to update
 * @returns {Promise<Object>} - The updated component
 */
export async function updateComponentInDb(component) {
  try {
    // Validate component
    if (!component.type) {
      throw new Error('Component must have a type')
    }
    
    if (!component.name) {
      throw new Error('Component must have a name')
    }
    
    // Check if component exists
    const existingComponent = await componentLibraryService.getComponentByType(component.type)
    if (!existingComponent) {
      throw new Error(`Component with type '${component.type}' does not exist`)
    }
    
    // Sanitize the component before saving
    const sanitizedComponent = sanitizeForIndexedDB(component)
    
    // Save the component to the database
    const updatedComponent = await componentLibraryService.saveComponent(sanitizedComponent)
    
    ElMessage.success(`Component '${component.name}' updated successfully`)
    return updatedComponent
  } catch (error) {
    console.error('Failed to update component in database:', error)
    ElMessage.error(error.message || 'Failed to update component in database')
    throw error
  }
}

/**
 * Deletes a component from the database
 * 
 * @param {string} type - The type of the component to delete
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function deleteComponentFromDb(type) {
  try {
    // Check if component exists
    const existingComponent = await componentLibraryService.getComponentByType(type)
    if (!existingComponent) {
      throw new Error(`Component with type '${type}' does not exist`)
    }
    
    // Delete the component from the database
    await componentLibraryService.deleteComponent(type)
    
    ElMessage.success(`Component '${existingComponent.name}' deleted successfully`)
    return true
  } catch (error) {
    console.error('Failed to delete component from database:', error)
    ElMessage.error(error.message || 'Failed to delete component from database')
    throw error
  }
}

export default {
  ensureDefaultComponentsInDb,
  addComponentToDb,
  updateComponentInDb,
  deleteComponentFromDb
}
