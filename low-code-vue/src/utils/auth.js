/**
 * Authentication utility functions
 */

// Get token from local storage
export function getToken() {
  return localStorage.getItem('token')
}

// Set token in local storage
export function setToken(token) {
  localStorage.setItem('token', token)
}

// Remove token from local storage
export function removeToken() {
  localStorage.removeItem('token')
}

// Check if user is logged in
export function isLoggedIn() {
  return !!getToken()
}

// Parse JWT token
export function parseToken(token) {
  if (!token) return null
  
  try {
    // JWT token consists of three parts separated by dots
    const parts = token.split('.')
    if (parts.length !== 3) return null
    
    // Parse the payload (second part)
    const payload = JSON.parse(atob(parts[1]))
    
    // Check if token is expired
    const exp = payload.exp * 1000 // Convert to milliseconds
    if (Date.now() >= exp) return null
    
    return payload
  } catch (e) {
    console.error('Failed to parse token:', e)
    return null
  }
}

// Get current user information
export function getCurrentUser() {
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : null
}

// Set current user information
export function setCurrentUser(user) {
  localStorage.setItem('user', JSON.stringify(user))
}

// Remove current user information
export function removeCurrentUser() {
  localStorage.removeItem('user')
}

// Logout - clear all auth data
export function logout() {
  removeToken()
  removeCurrentUser()
}
