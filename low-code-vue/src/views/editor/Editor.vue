<template>
  <div class="editor-container">
    <div class="editor-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <el-button icon="HomeFilled" @click="goHome" type="primary" plain>首页</el-button>
        <h2>{{ pageConfig.name || 'Untitled Page' }}</h2>
      </div>

      <div class="header-right">
        <div class="save-status" v-if="lastSavedTime">
          <el-icon v-if="isSaving"><Loading class="is-loading" /></el-icon>
          <span v-else><el-icon><Check /></el-icon></span>
          {{ isSaving ? '正在保存...' : `上次保存于 ${formatSaveTime(lastSavedTime)}` }}
        </div>
        <el-button-group>
          <el-button type="primary" @click="savePage" icon="Check" :loading="isSaving">保存</el-button>
          <el-button type="success" @click="previewPage" icon="View">预览</el-button>
          <el-button type="warning" @click="publishPage" icon="Upload">发布</el-button>
        </el-button-group>
      </div>
    </div>

    <div class="editor-content">
      <div class="editor-sidebar">
        <div class="sidebar-header">
          <h3>组件库</h3>
        </div>
        <div class="sidebar-content">
          <draggable-component-list />
        </div>
      </div>

      <div class="editor-canvas-container">
        <div class="editor-toolbar">
          <el-button-group>
            <el-button :icon="ZoomOut" @click="decreaseScale" :disabled="canvasScale <= 0.5" size="small"></el-button>
            <el-button size="small">{{ Math.round(canvasScale * 100) }}%</el-button>
            <el-button :icon="ZoomIn" @click="increaseScale" :disabled="canvasScale >= 2" size="small"></el-button>
          </el-button-group>

          <el-radio-group v-model="deviceType" size="small" style="margin-left: 15px">
            <el-radio-button label="mobile">手机</el-radio-button>
            <el-radio-button label="tablet">平板</el-radio-button>
            <el-radio-button label="desktop">桌面</el-radio-button>
          </el-radio-group>
        </div>

        <canvas-component
          v-model="pageConfig.components"
          :device="deviceType"
          :scale="canvasScale"
          :selectedId="selectedComponent?.id"
          :pageStyles="pageConfig.styles || {}"
          @select="selectComponent"
          @remove="removeComponent"
          @duplicate="duplicateComponent"
        />
      </div>

      <div class="editor-properties">
        <div class="properties-header">
          <h3>
            {{ selectedComponent ? `组件属性: ${getComponentName(selectedComponent)}` : '页面属性' }}
          </h3>
          <el-button v-if="selectedComponent" type="danger" size="small" icon="Delete" circle @click="removeSelectedComponent"></el-button>
        </div>

        <div class="properties-content">
          <template v-if="selectedComponent">
            <el-tabs type="border-card">
              <el-tab-pane label="属性" lazy>
                <component-props-editor
                  :component="selectedComponent"
                  @update="updateSelectedComponent"
                />
              </el-tab-pane>
              <el-tab-pane label="样式" lazy>
                <component-styles-editor
                  :component="selectedComponent"
                  @update="updateSelectedComponent"
                />
              </el-tab-pane>
              <el-tab-pane label="事件" lazy>
                <component-events-editor
                  :component="selectedComponent"
                  @update="updateSelectedComponent"
                />
              </el-tab-pane>
            </el-tabs>
          </template>

          <template v-else>
            <page-properties-editor
              :pageConfig="pageConfig"
              @update="updatePageConfig"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ZoomIn, ZoomOut, Bell, Check, View, Upload, Delete, Loading, HomeFilled } from '@element-plus/icons-vue'
import { v4 as uuidv4 } from 'uuid'
import { useEditorStore } from '../../store'
import { pageApi } from '../../api'
import { projectService, pageService, componentService } from '../../services/db'
import { sanitizeComponent } from '../../utils/sanitize'
import ComponentRenderer from '../../components/editor/ComponentRenderer.vue'
import ComponentPropsEditor from '../../components/editor/ComponentPropsEditor.vue'
import ComponentStylesEditor from '../../components/editor/ComponentStylesEditor.vue'
import ComponentEventsEditor from '../../components/editor/ComponentEventsEditor.vue'
import CanvasComponent from '../../components/editor/Canvas.vue'
import DraggableComponentList from '../../components/editor/DraggableComponentList.vue'
import PagePropertiesEditor from '../../components/editor/PagePropertiesEditor.vue'

const route = useRoute()
const router = useRouter()
const editorStore = useEditorStore()

// Canvas configuration
const canvasWidth = computed(() => editorStore.canvasWidth)
const canvasHeight = computed(() => editorStore.canvasHeight)
const canvasScale = computed(() => editorStore.canvasScale)

// Device type
const deviceType = ref('mobile')

// Component library
const componentLibrary = computed(() => editorStore.componentLibrary)

// Page configuration
const pageConfig = reactive({
  id: null,
  projectId: null,
  name: '',
  title: '',
  path: '',
  components: []
})

// Saving status
const isSaving = ref(false)
const lastSavedTime = ref(null)

// Selected component
const selectedComponent = computed(() => editorStore.selectedComponent)

// Load page data
const loadPageData = async () => {
  const projectId = route.params.projectId
  const pageId = route.params.pageId

  if (!projectId || !pageId) {
    ElMessage.error('Invalid project or page ID')
    router.push('/project')
    return
  }

  try {
    // Set project and page IDs in store
    editorStore.setProject(projectId)
    editorStore.setPage(pageId)

    // Load page data
    console.log('Loading page data for pageId:', pageId)
    const pageData = await pageApi.getPageDetail(pageId)
    console.log('Page data received:', pageData)

    if (pageData) {
      // Update page config
      Object.assign(pageConfig, pageData)
      console.log('Page config after Object.assign:', JSON.stringify(pageConfig))

      // Parse components if stored as string
      if (typeof pageData.config === 'string' && pageData.config) {
        try {
          console.log('Parsing page config string:', pageData.config)
          const config = JSON.parse(pageData.config)
          console.log('Parsed config:', config)
          pageConfig.components = config.components || []
          pageConfig.styles = config.styles || {}
          console.log('Components after parsing:', pageConfig.components)
          console.log('Styles after parsing:', pageConfig.styles)

          // 检查按钮组件
          const buttonComponents = pageConfig.components.filter(c => c.type === 'button')
          if (buttonComponents.length > 0) {
            console.log('Button components after loading:', buttonComponents)
            buttonComponents.forEach((btn, index) => {
              console.log(`Button ${index} events:`, btn.events)
            })
          }
        } catch (e) {
          console.error('Failed to parse page config:', e)
          pageConfig.components = []
        }
      } else if (pageData.config && pageData.config.components) {
        console.log('Using components from pageData.config:', pageData.config.components)
        pageConfig.components = pageData.config.components

        // 检查按钮组件
        const buttonComponents = pageConfig.components.filter(c => c.type === 'button')
        if (buttonComponents.length > 0) {
          console.log('Button components after loading:', buttonComponents)
          buttonComponents.forEach((btn, index) => {
            console.log(`Button ${index} events:`, btn.events)
          })
        }
      } else {
        console.log('No components found in page data')
        pageConfig.components = []
      }

      // Update store
      editorStore.setPageConfig({
        name: pageConfig.name,
        title: pageConfig.title,
        path: pageConfig.path,
        components: pageConfig.components
      })
    } else {
      ElMessage.error('Page not found')
      router.push(`/project/${projectId}`)
    }
  } catch (error) {
    console.error('Failed to load page data:', error)
    ElMessage.error('Failed to load page data')
  }
}

// Format save time
const formatSaveTime = (timestamp) => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${hours}:${minutes}:${seconds}`
}

// Save components to database
const saveComponentsToDb = async () => {
  if (!pageConfig.id) return

  try {
    isSaving.value = true

    // 检查组件事件配置
    console.log('Saving components to database:', pageConfig.components)

    // 检查按钮组件
    const buttonComponents = pageConfig.components.filter(c => c.type === 'button')
    if (buttonComponents.length > 0) {
      console.log('Button components before sanitizing:', buttonComponents)
      buttonComponents.forEach((btn, index) => {
        console.log(`Button ${index} events:`, btn.events)
      })
    }

    // Create a sanitized copy of the components to ensure they can be stored in IndexedDB
    const sanitizedComponents = pageConfig.components.map(component => sanitizeComponent(component))

    // 检查按钮组件在序列化后
    const sanitizedButtonComponents = sanitizedComponents.filter(c => c.type === 'button')
    if (sanitizedButtonComponents.length > 0) {
      console.log('Button components after sanitizing:', sanitizedButtonComponents)
      sanitizedButtonComponents.forEach((btn, index) => {
        console.log(`Sanitized button ${index} events:`, btn.events)
      })
    }

    // Save the sanitized components to the database
    await componentService.savePageComponents(pageConfig.id, sanitizedComponents)

    lastSavedTime.value = new Date()
    isSaving.value = false
  } catch (error) {
    console.error('Failed to save components to database:', error)
    isSaving.value = false
  }
}

// Auto-save when components change
const autoSaveDebounceTimeout = ref(null)
const autoSaveComponents = () => {
  // Clear any existing timeout
  if (autoSaveDebounceTimeout.value) {
    clearTimeout(autoSaveDebounceTimeout.value)
  }

  // Set a new timeout to debounce the save operation
  autoSaveDebounceTimeout.value = setTimeout(() => {
    saveComponentsToDb()
  }, 1000) // 1 second debounce
}

// Watch for changes in components and save automatically
watch(() => pageConfig.components, () => {
  if (pageConfig.id) {
    autoSaveComponents()
  }
}, { deep: true })

// Update page config
const updatePageConfig = (updatedConfig) => {
  // 更新页面配置
  Object.assign(pageConfig, updatedConfig)

  // 自动保存页面
  savePage()
}

// Save page
const savePage = async (callback) => {
  // Check if project ID exists
  if (!editorStore.projectId) {
    ElMessage.warning('Please select a project first')
    return
  }

  try {
    isSaving.value = true

    // 检查组件事件配置
    console.log('Saving page with components:', pageConfig.components)

    // 检查第一个组件的事件配置（如果有）
    if (pageConfig.components.length > 0) {
      console.log('First component:', pageConfig.components[0])
      if (pageConfig.components[0].events) {
        console.log('First component events:', pageConfig.components[0].events)
      }
    }

    // 检查所有按钮组件
    const buttonComponents = pageConfig.components.filter(c => c.type === 'button')
    if (buttonComponents.length > 0) {
      console.log('Button components:', buttonComponents)
      buttonComponents.forEach((btn, index) => {
        console.log(`Button ${index} events:`, btn.events)
      })
    }

    // 准备保存数据
    console.log('Preparing to save components:', pageConfig.components)

    // 检查按钮组件的事件配置
    // 使用上面已经定义的 buttonComponents 变量
    console.log('Button components before saving:', buttonComponents)
    if (buttonComponents.length > 0) {
      buttonComponents.forEach((btn, index) => {
        console.log(`Button ${index} events before saving:`, JSON.stringify(btn.events))

        // 确保按钮组件有事件属性
        if (!btn.events) {
          console.warn(`Button ${index} has no events, initializing empty array`)
          btn.events = []
        }
      })
    }

    // 创建组件的深拷贝
    const componentsCopy = JSON.parse(JSON.stringify(pageConfig.components))
    console.log('Components deep copy:', componentsCopy)

    // 再次检查按钮组件的事件配置
    const buttonComponentsCopy = componentsCopy.filter(c => c.type === 'button')
    if (buttonComponentsCopy.length > 0) {
      console.log('Button components copy:', buttonComponentsCopy)
      buttonComponentsCopy.forEach((btn, index) => {
        console.log(`Button ${index} events in copy:`, JSON.stringify(btn.events))
      })
    }

    const componentsJson = JSON.stringify(componentsCopy)
    console.log('Components JSON:', componentsJson)

    const saveData = {
      name: pageConfig.name,
      title: pageConfig.title,
      path: pageConfig.path,
      config: JSON.stringify({
        components: componentsCopy,
        styles: pageConfig.styles || {}
      })
    }

    console.log('Save data config:', saveData.config)

    if (pageConfig.id) {
      // Update existing page
      await pageApi.updatePage(pageConfig.id, saveData)

      // Also save to local database
      await pageService.updatePage({
        id: pageConfig.id,
        projectId: editorStore.projectId,
        name: pageConfig.name,
        title: pageConfig.title,
        path: pageConfig.path,
        config: JSON.stringify({
          components: componentsCopy
        })
      })

      // Save components
      await saveComponentsToDb()

      ElMessage.success('Page saved successfully')
    } else {
      // Create new page
      saveData.projectId = editorStore.projectId
      const res = await pageApi.createPage(saveData)

      // Update page ID
      pageConfig.id = res.id

      // Also save to local database
      await pageService.createPage({
        id: pageConfig.id,
        projectId: editorStore.projectId,
        name: pageConfig.name,
        title: pageConfig.title,
        path: pageConfig.path,
        config: JSON.stringify({
          components: componentsCopy
        })
      })

      // Save components
      await saveComponentsToDb()

      ElMessage.success('Page saved successfully')
    }

    lastSavedTime.value = new Date()
    isSaving.value = false

    // Call callback if provided
    if (typeof callback === 'function') {
      callback()
    }
  } catch (error) {
    console.error('Failed to save page:', error)
    ElMessage.error('Failed to save page')
    isSaving.value = false
  }
}

// Preview page
const previewPage = () => {
  // Save current page config to local storage
  localStorage.setItem('previewPageConfig', JSON.stringify({
    name: pageConfig.name,
    title: pageConfig.title,
    path: pageConfig.path,
    components: pageConfig.components,
    styles: pageConfig.styles || {}
  }))

  // Navigate to preview page
  router.push(`/preview/${editorStore.projectId}/${pageConfig.id || 'new'}`)
}

// Publish page
const publishPage = () => {
  // Save page first, then publish
  savePage(() => {
    ElMessage.success('Page published successfully')
  })
}

// Go back
const goBack = () => {
  ElMessageBox.confirm('Do you want to save the current page?', 'Confirmation', {
    confirmButtonText: 'Save',
    cancelButtonText: 'Don\'t Save',
    type: 'warning'
  }).then(() => {
    // Save page
    savePage(() => {
      router.back()
    })
  }).catch(() => {
    // Don't save, just go back
    router.back()
  })
}

// Go to home page
const goHome = () => {
  ElMessageBox.confirm('Do you want to save the current page before returning to the home page?', 'Confirmation', {
    confirmButtonText: 'Save',
    cancelButtonText: 'Don\'t Save',
    type: 'warning'
  }).then(() => {
    // Save page
    savePage(() => {
      router.push('/dashboard')
    })
  }).catch(() => {
    // Don't save, just go to home page
    router.push('/dashboard')
  })
}

// Duplicate component
const duplicateComponent = (index) => {
  // Create a copy of the component
  const component = pageConfig.components[index]
  const newComponent = JSON.parse(JSON.stringify(component))
  newComponent.id = uuidv4()

  // Add to page components
  pageConfig.components.splice(index + 1, 0, newComponent)

  // Select the new component
  editorStore.selectComponent(newComponent)
}

// Component actions
const selectComponent = (component) => {
  console.log('Editor selecting component:', component ? component.id : 'none', component ? component.type : '')

  // Create a deep copy of the component to ensure it's not affected by reference issues
  const componentCopy = component ? JSON.parse(JSON.stringify(component)) : null

  // Update the selected component in the store
  editorStore.selectComponent(componentCopy)
}

const updateSelectedComponent = (updatedComponent) => {
  console.log('Editor updating component:', updatedComponent.id, updatedComponent.type)

  // Log specific component types for debugging
  if (updatedComponent.type === 'checkbox') {
    console.log('Checkbox options in Editor:', JSON.stringify(updatedComponent.props.options))
  }

  try {
    // First, check if this is a top-level component
    const index = pageConfig.components.findIndex(c => c.id === updatedComponent.id)

    if (index !== -1) {
      // This is a top-level component, update it directly
      const newComponents = [...pageConfig.components]

      // Create a completely new component object
      const newComponent = {
        id: updatedComponent.id,
        type: updatedComponent.type,
        props: JSON.parse(JSON.stringify(updatedComponent.props || {})),
        styles: JSON.parse(JSON.stringify(updatedComponent.styles || {})),
        // 保留 events 属性
        events: updatedComponent.events ? JSON.parse(JSON.stringify(updatedComponent.events)) : undefined
      }

      newComponents[index] = newComponent
      pageConfig.components = newComponents

      // Update selected component in store
      editorStore.selectComponent({
        ...newComponent
      })

      console.log('Editor updated top-level component successfully')

      // Trigger auto-save
      autoSaveComponents()
      return
    }

    // If not found at top level, search in container components
    for (let i = 0; i < pageConfig.components.length; i++) {
      const container = pageConfig.components[i]

      // Skip non-container components or containers without children
      if (!container.props.children || !Array.isArray(container.props.children)) {
        continue
      }

      // Look for the component in this container's children
      const childIndex = container.props.children.findIndex(child => child.id === updatedComponent.id)

      if (childIndex !== -1) {
        // Found the component in this container, update it
        const newContainer = JSON.parse(JSON.stringify(container))

        // Create a completely new component object
        const newComponent = {
          id: updatedComponent.id,
          type: updatedComponent.type,
          props: JSON.parse(JSON.stringify(updatedComponent.props || {})),
          styles: JSON.parse(JSON.stringify(updatedComponent.styles || {})),
          // 保留 events 属性
          events: updatedComponent.events ? JSON.parse(JSON.stringify(updatedComponent.events)) : undefined
        }

        newContainer.props.children[childIndex] = newComponent

        // Update the container in the page components
        const newComponents = [...pageConfig.components]
        newComponents[i] = newContainer
        pageConfig.components = newComponents

        // Update selected component in store
        editorStore.selectComponent({
          ...newComponent
        })

        console.log('Editor updated nested component successfully')

        // Trigger auto-save
        autoSaveComponents()
        return
      }
    }

    // If we get here, the component wasn't found anywhere
    console.warn('Component not found for update:', updatedComponent.id)
  } catch (error) {
    console.error('Error updating component in Editor:', error)
  }
}

const removeComponent = (index) => {
  // Remove component
  pageConfig.components.splice(index, 1)

  // Clear selection
  editorStore.clearSelection()
}

// Remove selected component
const removeSelectedComponent = () => {
  if (selectedComponent.value) {
    const index = pageConfig.components.findIndex(c => c.id === selectedComponent.value.id)
    if (index !== -1) {
      removeComponent(index)
    }
  }
}

// Get component name
const getComponentName = (component) => {
  if (!component) return ''

  const componentDef = componentLibrary.value.find(c => c.type === component.type)
  return componentDef ? componentDef.name : component.type
}

// Canvas scale actions
const increaseScale = () => {
  editorStore.setCanvasScale(Math.min(canvasScale.value + 0.1, 2))
}

const decreaseScale = () => {
  editorStore.setCanvasScale(Math.max(canvasScale.value - 0.1, 0.5))
}

// Clear selection when clicking outside components
const handleClickOutside = (event) => {
  const isCanvas = event.target.classList.contains('editor-canvas')

  if (isCanvas) {
    editorStore.clearSelection()
  }
}

// Load page data on mount
onMounted(() => {
  loadPageData()

  // Add click event listener to clear selection
  document.addEventListener('click', handleClickOutside)
})

// Remove event listener on unmount
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.editor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 0 15px;
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.save-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #909399;
}

.save-status .el-icon {
  font-size: 14px;
  color: #67c23a;
}

.save-status .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-sidebar {
  width: 250px;
  border-right: 1px solid #ebeef5;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}



.component-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 4px;
  background-color: #fff;
  cursor: move;
  user-select: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.component-item:hover {
  background-color: #ecf5ff;
}

.component-item .el-icon {
  font-size: 18px;
  color: #409eff;
}

.editor-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f0f2f5;
}

.editor-toolbar {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.editor-canvas-wrapper {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
}

.editor-canvas {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform-origin: top center;
  overflow-y: auto;
  position: relative;
  transition: transform 0.3s;
}

.empty-canvas {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}

.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #909399;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 20px;
}

.coming-soon .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
  color: #409eff;
  color: #909399;
}

.page-properties-form {
  padding: 20px 0;
}

.page-properties-form :deep(.el-form-item__label) {
  padding-bottom: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.page-properties-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.page-properties-form :deep(.el-input) {
  width: 100%;
}

.canvas-component {
  position: relative;
  margin-bottom: 10px;
}

.canvas-component.selected {
  outline: 2px solid #409eff;
}

.component-actions {
  position: absolute;
  top: -15px;
  right: -15px;
  z-index: 10;
}

.editor-properties {
  width: 350px;
  border-left: 1px solid #ebeef5;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.properties-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.properties-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #fff;
}

:deep(.el-tabs__content) {
  padding: 15px 5px;
}

:deep(.el-tabs__item) {
  font-size: 15px;
  height: 45px;
  line-height: 45px;
}
</style>
