<template>
  <div class="project-detail-container">
    <div class="project-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">Back</el-button>
        <h2>{{ project.name }}</h2>
      </div>

      <div class="header-right">
        <el-input
          v-model="searchKeyword"
          placeholder="Search pages"
          prefix-icon="Search"
          clearable
          @clear="loadProjectPages"
          @keyup.enter="searchPages"
          class="search-input"
        />

        <el-button type="primary" @click="createPage">Create Page</el-button>

        <el-dropdown @command="handleCommand">
          <el-button>
            More <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">Export Document</el-dropdown-item>
              <el-dropdown-item command="exportCode">Export Code</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <div class="project-description">
      <p>{{ project.description || 'No description' }}</p>
    </div>

    <div class="page-list" v-loading="loading">
      <el-empty v-if="pageTree.length === 0" description="No pages found" />

      <el-card v-for="page in pageTree" :key="page.id" class="page-card" shadow="hover">
        <div class="page-card-content">
          <div class="page-thumbnail">
            <img v-if="page.thumbnail" :src="page.thumbnail" alt="Page thumbnail" />
            <div v-else class="thumbnail-placeholder">
              <el-icon><Document /></el-icon>
            </div>
          </div>

          <div class="page-info">
            <h3 class="page-name">{{ page.name }}</h3>
            <p class="page-path">{{ page.path }}</p>
            <p class="page-time">Updated: {{ formatDate(page.updateTime) }}</p>
          </div>
        </div>

        <div class="page-card-actions">
          <el-button type="primary" text @click="openEditor(page)">Edit</el-button>
          <el-button type="primary" text @click="editPage(page)">Settings</el-button>
          <el-popconfirm
            title="Are you sure to delete this page?"
            @confirm="deletePage(page)"
          >
            <template #reference>
              <el-button type="danger" text>Delete</el-button>
            </template>
          </el-popconfirm>
        </div>
      </el-card>
    </div>

    <!-- Create Page Dialog -->
    <el-dialog v-model="createDialogVisible" title="Create Page" width="500px">
      <el-form :model="pageForm" :rules="rules" ref="pageFormRef" label-width="100px">
        <el-form-item label="Name" prop="name">
          <el-input v-model="pageForm.name" placeholder="Page name" />
        </el-form-item>

        <el-form-item label="Title" prop="title">
          <el-input v-model="pageForm.title" placeholder="Page title" />
        </el-form-item>

        <el-form-item label="Path" prop="path">
          <el-input v-model="pageForm.path" placeholder="Page path (e.g. /home)" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="createDialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitCreatePage">Create</el-button>
      </template>
    </el-dialog>

    <!-- Edit Page Dialog -->
    <el-dialog v-model="editDialogVisible" title="Edit Page" width="500px">
      <el-form :model="pageForm" :rules="rules" ref="pageFormRef" label-width="100px">
        <el-form-item label="Name" prop="name">
          <el-input v-model="pageForm.name" placeholder="Page name" />
        </el-form-item>

        <el-form-item label="Title" prop="title">
          <el-input v-model="pageForm.title" placeholder="Page title" />
        </el-form-item>

        <el-form-item label="Path" prop="path">
          <el-input v-model="pageForm.path" placeholder="Page path (e.g. /home)" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitEditPage">Save</el-button>
      </template>
    </el-dialog>

    <!-- Export Document Dialog -->
    <el-dialog v-model="exportDialogVisible" title="Export Document" width="400px">
      <el-form label-width="100px">
        <el-form-item label="Format">
          <el-radio-group v-model="exportFormat">
            <el-radio value="markdown">Markdown</el-radio>
            <el-radio value="html">HTML</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="exportDialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="exportLoading" @click="exportDocument">Export</el-button>
      </template>
    </el-dialog>

    <!-- Export Code Dialog -->
    <el-dialog v-model="exportCodeDialogVisible" title="Export Code" width="400px">
      <el-form label-width="100px">
        <el-form-item label="Framework">
          <el-radio-group v-model="exportCodeFormat">
            <el-radio value="vue">Vue</el-radio>
            <el-radio value="uniapp">UniApp</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="exportCodeDialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="exportCodeLoading" @click="exportCode">Export</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowDown, Document } from '@element-plus/icons-vue'
import { projectApi, pageApi, documentApi } from '../../api'
import { codeApi } from '../../api/code'

const route = useRoute()
const router = useRouter()

// Project data
const project = reactive({
  id: null,
  name: '',
  description: '',
  createTime: null,
  updateTime: null
})

// Pages data
const pageTree = ref([])
const loading = ref(false)
const searchKeyword = ref('')

// Dialog visibility
const createDialogVisible = ref(false)
const editDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const submitLoading = ref(false)
const exportLoading = ref(false)
const exportFormat = ref('markdown')

// Page form
const pageFormRef = ref(null)
const pageForm = reactive({
  id: null,
  name: '',
  title: '',
  path: ''
})

// Form validation rules
const rules = {
  name: [
    { required: true, message: 'Please enter page name', trigger: 'blur' },
    { min: 2, max: 50, message: 'Length should be 2 to 50 characters', trigger: 'blur' }
  ],
  title: [
    { required: true, message: 'Please enter page title', trigger: 'blur' }
  ],
  path: [
    { required: true, message: 'Please enter page path', trigger: 'blur' },
    { pattern: /^\/[a-zA-Z0-9\-_\/]*$/, message: 'Path must start with / and contain only letters, numbers, hyphens, underscores, and slashes', trigger: 'blur' }
  ]
}

// Load project info
const loadProjectInfo = () => {
  // Get project ID
  const projectId = route.params.id

  // Ensure projectId is valid
  if (projectId && !isNaN(Number(projectId)) && Number(projectId) > 0) {
    // Convert to number
    const numericProjectId = Number(projectId)

    // Call API to get project info
    projectApi.getProjectDetail(numericProjectId).then(res => {
      if (res) {
        Object.assign(project, res)
        // Load project pages
        loadProjectPages(numericProjectId)
      } else {
        ElMessage.error('Project does not exist')
        router.push('/project')
      }
    }).catch(err => {
      console.error('Failed to get project info:', err)
      ElMessage.error('Failed to get project info')
      router.push('/project')
    })
  } else {
    // Handle invalid projectId
    ElMessage.error('Invalid project ID')
    router.push('/project')
  }
}

// Load project pages
const loadProjectPages = (projectId) => {
  // Ensure projectId is valid
  if (!projectId || isNaN(Number(projectId)) || Number(projectId) <= 0) {
    ElMessage.error('Invalid project ID')
    return
  }

  loading.value = true

  // Call API to get project pages
  pageApi.getPageList(projectId).then(res => {
    pageTree.value = res || []
  }).catch(err => {
    console.error('Failed to get page list:', err)
    ElMessage.error('Failed to get page list')
  }).finally(() => {
    loading.value = false
  })
}

// Search pages
const searchPages = () => {
  // Filter pages locally
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    pageTree.value = pageTree.value.filter(page =>
      page.name.toLowerCase().includes(keyword) ||
      page.title.toLowerCase().includes(keyword) ||
      page.path.toLowerCase().includes(keyword)
    )
  } else {
    loadProjectPages(project.id)
  }
}

// Format date
const formatDate = (dateString) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// Go back
const goBack = () => {
  router.back()
}

// Open editor
const openEditor = (page) => {
  router.push(`/editor/${project.id}/${page.id}`)
}

// Create page
const createPage = () => {
  pageForm.id = null
  pageForm.name = ''
  pageForm.title = ''
  pageForm.path = ''

  createDialogVisible.value = true
}

// Submit create page
const submitCreatePage = async () => {
  if (!pageFormRef.value) return

  await pageFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true

      try {
        await pageApi.createPage({
          projectId: project.id,
          name: pageForm.name,
          title: pageForm.title,
          path: pageForm.path
        })

        ElMessage.success('Page created successfully')
        createDialogVisible.value = false
        loadProjectPages(project.id)
      } catch (error) {
        console.error('Failed to create page:', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// Edit page
const editPage = (page) => {
  pageForm.id = page.id
  pageForm.name = page.name
  pageForm.title = page.title
  pageForm.path = page.path

  editDialogVisible.value = true
}

// Submit edit page
const submitEditPage = async () => {
  if (!pageFormRef.value) return

  await pageFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true

      try {
        await pageApi.updatePage(pageForm.id, {
          name: pageForm.name,
          title: pageForm.title,
          path: pageForm.path
        })

        ElMessage.success('Page updated successfully')
        editDialogVisible.value = false
        loadProjectPages(project.id)
      } catch (error) {
        console.error('Failed to update page:', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// Delete page
const deletePage = async (page) => {
  try {
    await pageApi.deletePage(page.id)
    ElMessage.success('Page deleted successfully')
    loadProjectPages(project.id)
  } catch (error) {
    console.error('Failed to delete page:', error)
    ElMessage.error('Failed to delete page')
  }
}

// Handle dropdown commands
const handleCommand = (command) => {
  if (command === 'export') {
    exportDialogVisible.value = true
  } else if (command === 'exportCode') {
    exportCodeDialogVisible.value = true
  }
}

// Export document
const exportDocument = async () => {
  exportLoading.value = true

  try {
    const response = await documentApi.exportProjectDocument(project.id, exportFormat.value)

    // Create a blob from the response
    const blob = new Blob([response], {
      type: exportFormat.value === 'markdown' ? 'text/markdown' : 'text/html'
    })

    // Create a link element and trigger download
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `${project.name}.${exportFormat.value === 'markdown' ? 'md' : 'html'}`
    link.click()

    ElMessage.success('Document exported successfully')
    exportDialogVisible.value = false
  } catch (error) {
    console.error('Failed to export document:', error)
    ElMessage.error('Failed to export document')
  } finally {
    exportLoading.value = false
  }
}

// Export code
const exportCodeDialogVisible = ref(false)
const exportCodeLoading = ref(false)
const exportCodeFormat = ref('vue')

const exportCode = async () => {
  exportCodeLoading.value = true
  console.log('Exporting code for project:', project.id, 'with format:', exportCodeFormat.value)

  try {
    console.log('Calling exportProjectCode with:', project.id, exportCodeFormat.value)
    const response = await codeApi.exportProjectCode(project.id, exportCodeFormat.value)
    console.log('Response received:', typeof response, response instanceof Blob ? 'Blob' : 'Not a Blob')

    // Create a blob from the response
    console.log('Creating blob from response')
    let blob
    try {
      blob = new Blob([response], {
        type: 'application/zip'
      })
      console.log('Blob created successfully:', blob.size, 'bytes')
    } catch (blobError) {
      console.error('Error creating blob:', blobError)
      throw new Error('Failed to create blob: ' + blobError.message)
    }

    // Create a link element and trigger download
    console.log('Creating download link')
    const link = document.createElement('a')
    try {
      const url = URL.createObjectURL(blob)
      console.log('URL created successfully')
      link.href = url
      link.download = `${project.name}_${exportCodeFormat.value}_code.zip`
      console.log('Triggering download with filename:', link.download)
      link.click()
      // Clean up the URL object
      setTimeout(() => {
        URL.revokeObjectURL(url)
        console.log('URL object revoked')
      }, 100)
    } catch (urlError) {
      console.error('Error creating URL or triggering download:', urlError)
      throw new Error('Failed to create download link: ' + urlError.message)
    }

    ElMessage.success('Code exported successfully')
    exportCodeDialogVisible.value = false
  } catch (error) {
    console.error('Failed to export code:', error)
    console.error('Error details:', error.response ? error.response.data : error.message)
    ElMessage.error(`Failed to export code: ${error.message}`)
  } finally {
    exportCodeLoading.value = false
  }
}

// Load project on mount
onMounted(() => {
  loadProjectInfo()
})
</script>

<style scoped>
.project-detail-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 0 15px;
  font-size: 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input {
  width: 250px;
}

.project-description {
  margin-bottom: 30px;
  color: #606266;
}

.page-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  min-height: 300px;
}

.page-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-card-content {
  flex: 1;
  display: flex;
  gap: 15px;
}

.page-thumbnail {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.page-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.thumbnail-placeholder .el-icon {
  font-size: 30px;
  color: #909399;
}

.page-info {
  flex: 1;
}

.page-name {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 16px;
  color: #303133;
}

.page-path {
  margin-bottom: 5px;
  color: #606266;
  font-size: 14px;
}

.page-time {
  margin-bottom: 0;
  font-size: 12px;
  color: #909399;
}

.page-card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
}
</style>
