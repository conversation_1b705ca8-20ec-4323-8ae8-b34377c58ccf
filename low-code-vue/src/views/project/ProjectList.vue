<template>
  <div class="project-list-container">
    <div class="project-header">
      <h2>My Projects</h2>
      <el-button type="primary" @click="showCreateDialog">Create Project</el-button>
    </div>
    
    <div class="project-search">
      <el-input
        v-model="searchKeyword"
        placeholder="Search projects"
        prefix-icon="Search"
        clearable
        @clear="loadProjects"
        @keyup.enter="searchProjects"
      />
    </div>
    
    <div class="project-grid" v-loading="loading">
      <el-empty v-if="projects.length === 0" description="No projects found" />
      
      <el-card v-for="project in projects" :key="project.id" class="project-card" shadow="hover">
        <div class="project-card-content">
          <h3 class="project-name">{{ project.name }}</h3>
          <p class="project-description">{{ project.description || 'No description' }}</p>
          <p class="project-time">Created: {{ formatDate(project.createTime) }}</p>
        </div>
        
        <div class="project-card-actions">
          <el-button type="primary" text @click="openProject(project)">Open</el-button>
          <el-button type="primary" text @click="editProject(project)">Edit</el-button>
          <el-popconfirm
            title="Are you sure to delete this project?"
            @confirm="deleteProject(project.id)"
          >
            <template #reference>
              <el-button type="danger" text>Delete</el-button>
            </template>
          </el-popconfirm>
        </div>
      </el-card>
    </div>
    
    <el-pagination
      v-if="total > 0"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[8, 16, 24, 32]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="pagination"
    />
    
    <!-- Create Project Dialog -->
    <el-dialog v-model="createDialogVisible" title="Create Project" width="500px">
      <el-form :model="projectForm" :rules="rules" ref="projectFormRef" label-width="100px">
        <el-form-item label="Name" prop="name">
          <el-input v-model="projectForm.name" placeholder="Project name" />
        </el-form-item>
        
        <el-form-item label="Description" prop="description">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            placeholder="Project description"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitCreateProject">Create</el-button>
      </template>
    </el-dialog>
    
    <!-- Edit Project Dialog -->
    <el-dialog v-model="editDialogVisible" title="Edit Project" width="500px">
      <el-form :model="projectForm" :rules="rules" ref="projectFormRef" label-width="100px">
        <el-form-item label="Name" prop="name">
          <el-input v-model="projectForm.name" placeholder="Project name" />
        </el-form-item>
        
        <el-form-item label="Description" prop="description">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            placeholder="Project description"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editDialogVisible = false">Cancel</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitEditProject">Save</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { projectApi } from '../../api'

const router = useRouter()

// Projects data
const projects = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(8)
const loading = ref(false)
const searchKeyword = ref('')

// Dialog visibility
const createDialogVisible = ref(false)
const editDialogVisible = ref(false)
const submitLoading = ref(false)

// Project form
const projectFormRef = ref(null)
const projectForm = reactive({
  id: null,
  name: '',
  description: ''
})

// Form validation rules
const rules = {
  name: [
    { required: true, message: 'Please enter project name', trigger: 'blur' },
    { min: 2, max: 50, message: 'Length should be 2 to 50 characters', trigger: 'blur' }
  ]
}

// Load projects
const loadProjects = async () => {
  loading.value = true
  
  try {
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      name: searchKeyword.value || undefined
    }
    
    const result = await projectApi.getProjectList(params)
    projects.value = result.records || []
    total.value = result.total || 0
  } catch (error) {
    console.error('Failed to load projects:', error)
    ElMessage.error('Failed to load projects')
  } finally {
    loading.value = false
  }
}

// Search projects
const searchProjects = () => {
  currentPage.value = 1
  loadProjects()
}

// Pagination handlers
const handleSizeChange = (size) => {
  pageSize.value = size
  loadProjects()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadProjects()
}

// Format date
const formatDate = (dateString) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// Open project
const openProject = (project) => {
  router.push(`/project/${project.id}`)
}

// Show create dialog
const showCreateDialog = () => {
  projectForm.id = null
  projectForm.name = ''
  projectForm.description = ''
  createDialogVisible.value = true
}

// Submit create project
const submitCreateProject = async () => {
  if (!projectFormRef.value) return
  
  await projectFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      
      try {
        await projectApi.createProject({
          name: projectForm.name,
          description: projectForm.description
        })
        
        ElMessage.success('Project created successfully')
        createDialogVisible.value = false
        loadProjects()
      } catch (error) {
        console.error('Failed to create project:', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// Edit project
const editProject = (project) => {
  projectForm.id = project.id
  projectForm.name = project.name
  projectForm.description = project.description
  editDialogVisible.value = true
}

// Submit edit project
const submitEditProject = async () => {
  if (!projectFormRef.value) return
  
  await projectFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      
      try {
        await projectApi.updateProject(projectForm.id, {
          name: projectForm.name,
          description: projectForm.description
        })
        
        ElMessage.success('Project updated successfully')
        editDialogVisible.value = false
        loadProjects()
      } catch (error) {
        console.error('Failed to update project:', error)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// Delete project
const deleteProject = async (id) => {
  try {
    await projectApi.deleteProject(id)
    ElMessage.success('Project deleted successfully')
    loadProjects()
  } catch (error) {
    console.error('Failed to delete project:', error)
    ElMessage.error('Failed to delete project')
  }
}

// Load projects on mount
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.project-list-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.project-header h2 {
  margin: 0;
  font-size: 24px;
}

.project-search {
  margin-bottom: 20px;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  min-height: 300px;
}

.project-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-card-content {
  flex: 1;
}

.project-name {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #303133;
}

.project-description {
  margin-bottom: 10px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.project-time {
  margin-bottom: 0;
  font-size: 12px;
  color: #909399;
}

.project-card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
