<template>
  <div class="component-designer">
    <div class="designer-header">
      <div class="header-left">
        <h2>自定义组件设计器</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="saveComponent">保存组件</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>

    <div class="designer-content">
      <div class="designer-sidebar">
        <div class="sidebar-section">
          <h3>基础形状</h3>
          <div class="shape-list">
            <div
              v-for="shape in basicShapes"
              :key="shape.type"
              class="shape-item"
              @mousedown="startDrag(shape)"
              draggable="false"
            >
              <div :class="['shape-preview', `shape-${shape.type}`]"></div>
              <span>{{ shape.name }}</span>
            </div>
          </div>
        </div>

        <div class="sidebar-section">
          <h3>组件属性</h3>
          <el-form label-position="top" v-if="selectedElement">
            <el-form-item label="组件名称">
              <el-input v-model="componentInfo.name" placeholder="输入组件名称" />
            </el-form-item>

            <el-form-item label="组件类型">
              <el-input v-model="componentInfo.type" placeholder="输入组件类型标识" />
            </el-form-item>

            <el-form-item label="组件图标">
              <el-select v-model="componentInfo.icon" filterable placeholder="选择图标">
                <el-option
                  v-for="icon in availableIcons"
                  :key="icon"
                  :label="icon"
                  :value="icon"
                >
                  <div class="icon-option">
                    <el-icon>
                      <component :is="icon"></component>
                    </el-icon>
                    <span>{{ icon }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="组件分类">
              <el-select v-model="componentInfo.category" placeholder="选择分类">
                <el-option label="基础组件" value="basic" />
                <el-option label="表单组件" value="form" />
                <el-option label="数据组件" value="data" />
                <el-option label="容器组件" value="container" />
                <el-option label="导航组件" value="navigation" />
                <el-option label="反馈组件" value="feedback" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="designer-main">
        <div class="design-canvas-container">
          <div
            ref="designCanvas"
            class="design-canvas"
            @mousedown="handleCanvasMouseDown"
            @mousemove="handleCanvasMouseMove"
            @mouseup="handleCanvasMouseUp"
            @mouseleave="handleCanvasMouseLeave"
          >
            <div
              v-for="element in canvasElements"
              :key="element.id"
              :class="['canvas-element', { 'selected': selectedElement && selectedElement.id === element.id }]"
              :style="getElementStyle(element)"
              @mousedown.stop="selectElement(element, $event)"
            >
              <div class="element-content" :class="`element-${element.type}`"></div>

              <!-- 调整大小的手柄 -->
              <div
                v-if="selectedElement && selectedElement.id === element.id"
                v-for="handle in resizeHandles"
                :key="handle.position"
                :class="['resize-handle', `handle-${handle.position}`]"
                @mousedown.stop="startResize(element, handle.position, $event)"
              ></div>
            </div>
          </div>
        </div>

        <div class="element-properties" v-if="selectedElement">
          <el-tabs>
            <el-tab-pane label="样式">
              <styles-editor v-model="selectedElement.styles" @update:modelValue="updateElementStyles" />
            </el-tab-pane>
            <el-tab-pane label="属性">
              <el-form label-position="top">
                <el-form-item label="元素类型">
                  <el-input v-model="selectedElement.type" disabled />
                </el-form-item>

                <el-form-item label="元素ID">
                  <el-input v-model="selectedElement.id" disabled />
                </el-form-item>

                <el-form-item label="元素名称">
                  <el-input v-model="selectedElement.name" @change="updateElement" />
                </el-form-item>

                <el-form-item label="HTML标签">
                  <el-select v-model="selectedElement.tag" @change="updateElement">
                    <el-option label="div" value="div" />
                    <el-option label="span" value="span" />
                    <el-option label="button" value="button" />
                    <el-option label="input" value="input" />
                    <el-option label="textarea" value="textarea" />
                    <el-option label="select" value="select" />
                    <el-option label="img" value="img" />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="内容">
              <el-form label-position="top">
                <el-form-item label="文本内容">
                  <el-input
                    v-model="selectedElement.content"
                    type="textarea"
                    :rows="3"
                    @change="updateElement"
                  />
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'
import StylesEditor from '../../components/editor/props/StylesEditor.vue'
import { useEditorStore } from '@/store'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const router = useRouter()
const editorStore = useEditorStore()

// 可用图标列表
const availableIcons = computed(() => {
  return Object.keys(ElementPlusIconsVue)
})

// 基础形状
const basicShapes = [
  { type: 'rectangle', name: '矩形' },
  { type: 'circle', name: '圆形' },
  { type: 'triangle', name: '三角形' },
  { type: 'text', name: '文本' },
  { type: 'button', name: '按钮' },
  { type: 'input', name: '输入框' }
]

// 画布元素
const canvasElements = ref([])

// 当前选中的元素
const selectedElement = ref(null)

// 设计画布引用
const designCanvas = ref(null)

// 组件信息
const componentInfo = reactive({
  name: '',
  type: '',
  icon: 'Edit',
  category: 'basic',
  isCustom: true
})

// 调整大小的手柄
const resizeHandles = [
  { position: 'top-left' },
  { position: 'top-right' },
  { position: 'bottom-left' },
  { position: 'bottom-right' },
  { position: 'top' },
  { position: 'right' },
  { position: 'bottom' },
  { position: 'left' }
]

// 拖拽状态
const dragState = reactive({
  isDragging: false,
  currentElement: null,
  startX: 0,
  startY: 0,
  elementStartX: 0,
  elementStartY: 0
})

// 调整大小状态
const resizeState = reactive({
  isResizing: false,
  currentElement: null,
  handle: '',
  startX: 0,
  startY: 0,
  elementStartWidth: 0,
  elementStartHeight: 0,
  elementStartX: 0,
  elementStartY: 0
})

// 创建新元素
const createNewElement = (shape, x, y) => {
  const id = uuidv4()
  const element = {
    id,
    type: shape.type,
    name: shape.name,
    tag: 'div',
    content: shape.type === 'text' ? '文本内容' : (shape.type === 'button' ? '按钮' : ''),
    x,
    y,
    width: 100,
    height: 100,
    styles: {
      backgroundColor: shape.type === 'button' ? '#409EFF' : (shape.type === 'input' ? '#FFFFFF' : '#E6F7FF'),
      border: shape.type === 'input' ? '1px solid #DCDFE6' : 'none',
      borderRadius: shape.type === 'circle' ? '50%' : (shape.type === 'button' ? '4px' : '0'),
      color: shape.type === 'button' ? '#FFFFFF' : '#333333',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      fontSize: '14px',
      padding: shape.type === 'button' ? '8px 16px' : '0'
    }
  }

  canvasElements.value.push(element)
  return element
}

// 开始拖拽
const startDrag = (shape) => {
  const handleMouseMove = (e) => {
    const canvasRect = designCanvas.value.getBoundingClientRect()
    const x = e.clientX - canvasRect.left
    const y = e.clientY - canvasRect.top

    // 创建预览元素
    const previewElement = document.createElement('div')
    previewElement.className = `drag-preview shape-${shape.type}`
    previewElement.style.left = `${x}px`
    previewElement.style.top = `${y}px`
    designCanvas.value.appendChild(previewElement)

    const handleMouseUp = (e) => {
      // 移除预览元素
      if (previewElement.parentNode) {
        previewElement.parentNode.removeChild(previewElement)
      }

      // 创建新元素
      const canvasRect = designCanvas.value.getBoundingClientRect()
      const x = e.clientX - canvasRect.left
      const y = e.clientY - canvasRect.top

      if (x >= 0 && y >= 0 && x <= canvasRect.width && y <= canvasRect.height) {
        const newElement = createNewElement(shape, x, y)
        selectElement(newElement)
      }

      // 移除事件监听器
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mouseup', handleMouseUp, { once: true })
  }

  document.addEventListener('mousemove', handleMouseMove, { once: true })
}

// 选择元素
const selectElement = (element, event) => {
  if (event) {
    event.stopPropagation()

    // 开始拖拽
    dragState.isDragging = true
    dragState.currentElement = element
    dragState.startX = event.clientX
    dragState.startY = event.clientY
    dragState.elementStartX = element.x
    dragState.elementStartY = element.y
  }

  selectedElement.value = element
}

// 更新元素
const updateElement = () => {
  if (!selectedElement.value) return

  const index = canvasElements.value.findIndex(el => el.id === selectedElement.value.id)
  if (index !== -1) {
    canvasElements.value[index] = { ...selectedElement.value }
  }
}

// 更新元素样式
const updateElementStyles = (styles) => {
  if (!selectedElement.value) return

  selectedElement.value.styles = styles
  updateElement()
}

// 获取元素样式
const getElementStyle = (element) => {
  return {
    position: 'absolute',
    left: `${element.x}px`,
    top: `${element.y}px`,
    width: `${element.width}px`,
    height: `${element.height}px`,
    ...element.styles
  }
}

// 处理画布鼠标按下事件
const handleCanvasMouseDown = (event) => {
  // 如果点击的是画布而不是元素，取消选择
  if (event.target === designCanvas.value) {
    selectedElement.value = null
  }
}

// 处理画布鼠标移动事件
const handleCanvasMouseMove = (event) => {
  // 处理拖拽
  if (dragState.isDragging && dragState.currentElement) {
    const dx = event.clientX - dragState.startX
    const dy = event.clientY - dragState.startY

    dragState.currentElement.x = dragState.elementStartX + dx
    dragState.currentElement.y = dragState.elementStartY + dy

    updateElement()
  }

  // 处理调整大小
  if (resizeState.isResizing && resizeState.currentElement) {
    const dx = event.clientX - resizeState.startX
    const dy = event.clientY - resizeState.startY

    const { handle, elementStartWidth, elementStartHeight, elementStartX, elementStartY } = resizeState

    // 根据不同的调整手柄更新元素的尺寸和位置
    if (handle.includes('left')) {
      resizeState.currentElement.x = elementStartX + dx
      resizeState.currentElement.width = elementStartWidth - dx
    }

    if (handle.includes('right')) {
      resizeState.currentElement.width = elementStartWidth + dx
    }

    if (handle.includes('top')) {
      resizeState.currentElement.y = elementStartY + dy
      resizeState.currentElement.height = elementStartHeight - dy
    }

    if (handle.includes('bottom')) {
      resizeState.currentElement.height = elementStartHeight + dy
    }

    // 确保宽度和高度不小于最小值
    resizeState.currentElement.width = Math.max(20, resizeState.currentElement.width)
    resizeState.currentElement.height = Math.max(20, resizeState.currentElement.height)

    updateElement()
  }
}

// 处理画布鼠标抬起事件
const handleCanvasMouseUp = () => {
  dragState.isDragging = false
  dragState.currentElement = null

  resizeState.isResizing = false
  resizeState.currentElement = null
}

// 处理画布鼠标离开事件
const handleCanvasMouseLeave = () => {
  dragState.isDragging = false
  dragState.currentElement = null

  resizeState.isResizing = false
  resizeState.currentElement = null
}

// 开始调整大小
const startResize = (element, handle, event) => {
  event.stopPropagation()

  resizeState.isResizing = true
  resizeState.currentElement = element
  resizeState.handle = handle
  resizeState.startX = event.clientX
  resizeState.startY = event.clientY
  resizeState.elementStartWidth = element.width
  resizeState.elementStartHeight = element.height
  resizeState.elementStartX = element.x
  resizeState.elementStartY = element.y
}

// 保存组件
const saveComponent = async () => {
  if (!componentInfo.name) {
    ElMessage.warning('请输入组件名称')
    return
  }

  if (!componentInfo.type) {
    ElMessage.warning('请输入组件类型标识')
    return
  }

  // 验证组件类型格式
  const typePattern = /^[a-zA-Z0-9_\-]+$/
  if (!typePattern.test(componentInfo.type)) {
    ElMessage.warning('组件类型只能包含字母、数字、下划线和连字符')
    return
  }

  if (canvasElements.value.length === 0) {
    ElMessage.warning('请至少添加一个元素')
    return
  }

  // 构建组件数据
  const componentData = {
    name: componentInfo.name,
    type: componentInfo.type,
    icon: componentInfo.icon,
    category: componentInfo.category,
    isCustom: true,
    defaultProps: {},
    defaultStyles: {},
    elements: canvasElements.value
  }

  try {
    // 保存组件到组件库
    await editorStore.saveComponentToLibrary(componentData)
    ElMessage.success('组件保存成功')
    goBack()
  } catch (error) {
    console.error('保存组件失败:', error)
    ElMessage.error('保存组件失败')
  }
}

// 返回组件库页面
const goBack = () => {
  router.push('/component-library')
}
</script>

<style scoped>
.component-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.designer-sidebar {
  width: 250px;
  border-right: 1px solid #ebeef5;
  background-color: #f5f7fa;
  overflow-y: auto;
  padding: 15px;
}

.sidebar-section {
  margin-bottom: 20px;
}

.sidebar-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #606266;
}

.shape-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.shape-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.shape-item:hover {
  background-color: #e6f7ff;
}

.shape-preview {
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #333;
}

.shape-rectangle {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.shape-circle {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 50%;
}

.shape-triangle {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 40px solid #e6f7ff;
}

.shape-text {
  background-color: transparent;
  border: 1px dashed #91d5ff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.shape-text::after {
  content: 'Text';
  font-size: 12px;
}

.shape-button {
  background-color: #409eff;
  color: white;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.shape-button::after {
  content: 'Button';
  font-size: 12px;
  color: white;
}

.shape-input {
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.shape-input::after {
  content: 'Input';
  font-size: 12px;
  color: #909399;
}

.shape-item span {
  font-size: 12px;
  color: #606266;
}

.designer-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.design-canvas-container {
  flex: 1;
  padding: 20px;
  overflow: auto;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.design-canvas {
  width: 800px;
  height: 600px;
  background-color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.canvas-element {
  position: absolute;
  cursor: move;
  user-select: none;
}

.canvas-element.selected {
  outline: 2px solid #409eff;
  z-index: 10;
}

.element-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.element-rectangle {
  background-color: #e6f7ff;
}

.element-circle {
  border-radius: 50%;
  background-color: #e6f7ff;
}

.element-triangle {
  width: 0;
  height: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-bottom: 100px solid #e6f7ff;
  background-color: transparent !important;
}

.element-text {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.element-button {
  background-color: #409eff;
  color: white;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.element-input {
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: white;
  border: 1px solid #409eff;
  z-index: 20;
}

.handle-top-left {
  top: -4px;
  left: -4px;
  cursor: nwse-resize;
}

.handle-top-right {
  top: -4px;
  right: -4px;
  cursor: nesw-resize;
}

.handle-bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: nesw-resize;
}

.handle-bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: nwse-resize;
}

.handle-top {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: ns-resize;
}

.handle-right {
  top: 50%;
  right: -4px;
  transform: translateY(-50%);
  cursor: ew-resize;
}

.handle-bottom {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: ns-resize;
}

.handle-left {
  top: 50%;
  left: -4px;
  transform: translateY(-50%);
  cursor: ew-resize;
}

.element-properties {
  width: 300px;
  border-left: 1px solid #ebeef5;
  background-color: #fff;
  overflow-y: auto;
  padding: 15px;
}

.drag-preview {
  position: absolute;
  width: 100px;
  height: 100px;
  opacity: 0.5;
  pointer-events: none;
  z-index: 1000;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
