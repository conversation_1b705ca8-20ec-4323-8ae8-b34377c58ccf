<template>
  <div class="component-library-container">
    <div class="library-header">
      <h1>组件库管理</h1>
      <div class="header-actions">
        <el-button type="success" @click="goToInitContainers">初始化容器组件</el-button>
        <el-button type="warning" @click="goToAddContainers">添加容器组件</el-button>
        <el-button type="info" @click="goToUpdateSpace">更新间距组件</el-button>
        <el-button type="success" @click="goToAddInputComponents">添加输入框组件</el-button>
        <el-button type="warning" @click="goToComponentDesigner">设计自定义组件</el-button>
        <el-button type="primary" @click="showAddComponentDialog">添加组件</el-button>
        <el-button type="danger" @click="refreshComponents">刷新组件库</el-button>
      </div>
    </div>

    <div class="library-content">
      <el-tabs type="border-card">
        <el-tab-pane v-for="category in categories" :key="category.code" :label="category.name">
          <div class="component-list">
            <el-table :data="getComponentsByCategory(category.code)" style="width: 100%">
              <el-table-column prop="name" label="组件名称" width="180">
                <template #default="{ row }">
                  <div class="component-name">
                    <template v-if="isBase64Icon(row.icon)">
                      <img :src="row.icon" class="component-icon" alt="component icon" />
                    </template>
                    <template v-else>
                      <el-icon>
                        <component :is="row.icon || 'Document'" />
                      </el-icon>
                    </template>
                    <span>{{ row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="组件类型" width="180" />
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.enabled === 1 || row.enabled === '1' ? 'success' : 'danger'">
                    {{ row.enabled === 1 || row.enabled === '1' ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="updatedAt" label="更新时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.updatedAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="280">
                <template #default="{ row }">
                  <el-button type="primary" size="small" @click="editComponent(row)">编辑</el-button>
                  <el-button
                    :type="row.enabled === 1 || row.enabled === '1' ? 'warning' : 'success'"
                    size="small"
                    @click="toggleComponentStatus(row)"
                  >
                    {{ row.enabled === 1 || row.enabled === '1' ? '禁用' : '启用' }}
                  </el-button>
                  <el-button type="danger" size="small" @click="confirmDeleteComponent(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- Add/Edit Component Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑组件' : '添加组件'"
      width="80%"
      :close-on-click-modal="false"
      class="component-edit-dialog"
    >
      <div class="dialog-content">
        <div class="form-container">
          <el-form :model="componentForm" label-width="120px" :rules="formRules" ref="componentFormRef">
            <el-form-item label="组件类型" prop="type">
              <el-input v-model="componentForm.type" :disabled="isEditing" />
            </el-form-item>

            <el-form-item label="组件名称" prop="name">
              <el-input v-model="componentForm.name" />
            </el-form-item>

            <el-form-item label="组件图标" prop="icon">
              <el-select v-model="componentForm.icon" filterable placeholder="选择图标">
                <el-option
                  v-for="icon in availableIcons"
                  :key="icon"
                  :label="icon"
                  :value="icon"
                >
                  <div class="icon-option">
                    <el-icon><component :is="icon" /></el-icon>
                    <span>{{ icon }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="组件分类" prop="category">
              <el-select v-model="componentForm.category" placeholder="选择分类">
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="默认属性" prop="defaultProps">
              <el-input
                type="textarea"
                v-model="componentForm.defaultPropsJson"
                :rows="10"
                placeholder="输入JSON格式的默认属性"
              />
            </el-form-item>

            <el-form-item label="默认样式" prop="defaultStyles">
              <el-input
                type="textarea"
                v-model="componentForm.defaultStylesJson"
                :rows="10"
                placeholder="输入JSON格式的默认样式"
              />
            </el-form-item>

            <el-form-item label="组件状态">
              <el-switch
                v-model="componentForm.enabled"
                active-text="启用"
                inactive-text="禁用"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
          </el-form>
        </div>

        <div class="guide-container">
          <el-tabs>
            <el-tab-pane label="属性格式说明">
              <format-guide v-bind="propsFormatGuide" />
            </el-tab-pane>
            <el-tab-pane label="样式格式说明">
              <format-guide v-bind="stylesFormatGuide" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveComponent" :loading="isSaving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useEditorStore } from '../../store'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { componentApi } from '../../api/component'
import { componentCategoryApi } from '../../api/componentCategory'
import FormatGuide from '../../components/common/FormatGuide.vue'
import { propsFormatGuide, stylesFormatGuide } from '../../data/formatGuides'

const router = useRouter()
const editorStore = useEditorStore()

// Component categories
const categories = ref([
  { code: 'basic', name: '基础组件' },
  { code: 'form', name: '表单组件' },
  { code: 'layout', name: '布局组件' },
  { code: 'advanced', name: '高级组件' },
  { code: 'data', name: '数据组件' },
  { code: 'flow', name: '流程组件' },
  { code: 'icon', name: '图标组件' },
  { code: 'custom', name: '自定义组件' }
])

// Load categories from API
const loadCategories = async () => {
  try {
    const apiCategories = await componentCategoryApi.getAllCategories()
    if (apiCategories && apiCategories.length > 0) {
      // Filter out disabled categories
      const enabledCategories = apiCategories.filter(category => {
        return category.enabled === undefined || category.enabled === 1 || category.enabled === '1'
      })
      categories.value = enabledCategories
      console.log('Loaded categories from API:', enabledCategories.length)
    }
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}

// Available icons from Element Plus
const availableIcons = computed(() => Object.keys(ElementPlusIconsVue))

// Component list
const components = computed(() => editorStore.componentLibrary)

// Get components by category (including disabled ones)
const getComponentsByCategory = (categoryCode) => {
  return components.value.filter(component => {
    // Match by category (show all components, including disabled ones)
    return component.category === categoryCode
  })
}

// Check if icon is a base64 string
const isBase64Icon = (icon) => {
  return icon && typeof icon === 'string' && icon.startsWith('data:')
}

// Format date
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// Dialog visibility
const dialogVisible = ref(false)
const isEditing = ref(false)
const isSaving = ref(false)
const componentFormRef = ref(null)

// Current component being edited
const currentComponent = ref(null)

// Component form
const componentForm = ref({
  type: '',
  name: '',
  icon: '',
  category: 'custom',
  defaultPropsJson: '',
  defaultStylesJson: '',
  enabled: 1 // Default to enabled
})

// Form validation rules
const formRules = {
  type: [
    { required: true, message: '请输入组件类型', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_\-]+$/, message: '组件类型只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入组件名称', trigger: 'blur' }
  ],
  icon: [
    { required: true, message: '请选择组件图标', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择组件分类', trigger: 'change' }
  ],
  defaultPropsJson: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        try {
          JSON.parse(value)
          callback()
        } catch (error) {
          callback(new Error('请输入有效的JSON格式'))
        }
      },
      trigger: 'blur'
    }
  ],
  defaultStylesJson: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        try {
          JSON.parse(value)
          callback()
        } catch (error) {
          callback(new Error('请输入有效的JSON格式'))
        }
      },
      trigger: 'blur'
    }
  ]
}

// Show add component dialog
const showAddComponentDialog = () => {
  isEditing.value = false
  componentForm.value = {
    type: '',
    name: '',
    icon: 'Document',
    category: 'custom',
    defaultPropsJson: JSON.stringify({ content: '默认内容' }, null, 2),
    defaultStylesJson: JSON.stringify({ width: '100%', margin: '10px 0' }, null, 2)
  }
  dialogVisible.value = true
}

// Edit component
const editComponent = (component) => {
  isEditing.value = true
  componentForm.value = {
    type: component.type,
    name: component.name,
    icon: component.icon,
    category: component.category || 'custom',
    defaultPropsJson: JSON.stringify(component.defaultProps || {}, null, 2),
    defaultStylesJson: JSON.stringify(component.defaultStyles || {}, null, 2),
    enabled: component.enabled !== undefined ? component.enabled : 1 // Use component's enabled status or default to enabled
  }

  // Store the current component for reference
  currentComponent.value = component
  dialogVisible.value = true
}

// Save component
const saveComponent = async () => {
  // Validate form
  await componentFormRef.value.validate(async (valid) => {
    if (!valid) {
      return
    }

    try {
      isSaving.value = true

      // Parse JSON strings
      let defaultProps = {}
      let defaultStyles = {}

      try {
        defaultProps = JSON.parse(componentForm.value.defaultPropsJson || '{}')
        defaultStyles = JSON.parse(componentForm.value.defaultStylesJson || '{}')
      } catch (error) {
        ElMessage.error('JSON格式错误，请检查输入')
        isSaving.value = false
        return
      }

      // Create component object
      const component = {
        type: componentForm.value.type,
        name: componentForm.value.name,
        icon: componentForm.value.icon,
        category: componentForm.value.category,
        defaultProps,
        defaultStyles,
        enabled: componentForm.value.enabled // Use form's enabled status
      }

      // Show loading indicator
      const loadingInstance = ElLoading.service({
        lock: true,
        text: isEditing.value ? '更新组件中...' : '添加组件中...',
        background: 'rgba(255, 255, 255, 0.7)'
      })

      try {
        // Save component to database via API
        if (isEditing.value) {
          // Find the existing component to get its ID
          const existingComponent = await componentApi.getComponentByType(component.type)
          if (existingComponent) {
            await componentApi.updateComponent({
              ...component,
              id: existingComponent.id,
              enabled: existingComponent.enabled // Preserve enabled status
            })
            ElMessage.success('组件更新成功')
          } else {
            ElMessage.error(`找不到类型为 '${component.type}' 的组件`)
          }
        } else {
          await componentApi.createComponent(component)
          ElMessage.success('组件添加成功')
        }

        // Reload components
        await loadComponents()

        dialogVisible.value = false
      } finally {
        loadingInstance.close()
      }
    } catch (error) {
      console.error('Failed to save component:', error)
      // Error message is already shown by the componentManager functions
    } finally {
      isSaving.value = false
    }
  })
}

// Toggle component status (enable/disable)
const toggleComponentStatus = async (component) => {
  try {
    // Show confirmation dialog
    await ElMessageBox.confirm(
      `确定要${component.enabled === 1 || component.enabled === '1' ? '禁用' : '启用'}该组件吗？`,
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // Toggle component status
    const updatedComponent = {
      ...component,
      enabled: component.enabled === 1 || component.enabled === '1' ? 0 : 1
    }

    // Update component
    await componentApi.updateComponent(updatedComponent)

    // Show success message
    ElMessage.success(`组件已${updatedComponent.enabled === 1 ? '启用' : '禁用'}`)

    // Refresh components
    await loadComponents()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to toggle component status:', error)
      ElMessage.error('操作失败')
    }
  }
}

// Confirm delete component
const confirmDeleteComponent = (component) => {
  ElMessageBox.confirm(
    `确定要删除组件 "${component.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // Show loading indicator
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '删除组件中...',
        background: 'rgba(255, 255, 255, 0.7)'
      })

      try {
        // Delete component from database via API
        if (component.id) {
          await componentApi.deleteComponent(component.id)
          ElMessage.success('组件删除成功')
        } else {
          // If component doesn't have an ID, try to find it by type
          const existingComponent = await componentApi.getComponentByType(component.type)
          if (existingComponent && existingComponent.id) {
            await componentApi.deleteComponent(existingComponent.id)
            ElMessage.success('组件删除成功')
          } else {
            ElMessage.error(`找不到类型为 '${component.type}' 的组件`)
          }
        }

        // Reload components
        await loadComponents()
      } finally {
        loadingInstance.close()
      }
    } catch (error) {
      console.error('Failed to delete component:', error)
      // Error message is already shown by the componentManager functions
    }
  }).catch(() => {
    // User cancelled
  })
}

// Load components from API
const loadComponents = async () => {
  try {
    // Show loading indicator
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '加载组件库...',
      background: 'rgba(255, 255, 255, 0.7)'
    })

    try {
      // Load components from API
      await editorStore.loadComponentsFromDb()

      // Update local components list
      components.value = editorStore.componentLibrary
    } finally {
      loadingInstance.close()
    }
  } catch (error) {
    console.error('Failed to load components:', error)
    ElMessage.error('加载组件库失败')
  }
}

// 跳转到初始化容器组件页面
const goToInitContainers = () => {
  router.push('/init-containers')
}

// 跳转到添加容器组件页面
const goToAddContainers = () => {
  router.push('/add-containers')
}

// 跳转到更新间距组件页面
const goToUpdateSpace = () => {
  router.push('/update-space')
}

// 跳转到添加输入框组件页面
const goToAddInputComponents = () => {
  router.push('/add-input-components')
}

// 跳转到组件设计器页面
const goToComponentDesigner = () => {
  router.push('/component-designer')
}

// 刷新组件库
const refreshComponents = async () => {
  try {
    // 显示加载指示器
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '刷新组件库...',
      background: 'rgba(255, 255, 255, 0.7)'
    })

    try {
      // 刷新组件库
      await editorStore.loadComponentsFromDb(true) // 强制刷新

      // 更新本地组件列表
      components.value = editorStore.componentLibrary

      ElMessage.success('组件库刷新成功')
    } finally {
      loadingInstance.close()
    }
  } catch (error) {
    console.error('Failed to refresh components:', error)
    ElMessage.error('组件库刷新失败')
  }
}

// Load components on mount
onMounted(async () => {
  // Load categories from API
  await loadCategories()

  // Load components from API
  await loadComponents()
})
</script>

<style scoped>
.component-library-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.library-header h1 {
  margin: 0;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.library-content {
  flex: 1;
  overflow: auto;
}

.component-list {
  margin-top: 10px;
}

.component-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.component-name .el-icon {
  font-size: 18px;
}

.component-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-option .el-icon {
  font-size: 16px;
}

/* 组件编辑对话框样式 */
.component-edit-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.dialog-content {
  display: flex;
  height: 70vh;
  overflow: hidden;
}

.form-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid #ebeef5;
}

.guide-container {
  width: 40%;
  overflow-y: auto;
}

.guide-container :deep(.el-tabs__header) {
  padding: 0 15px;
  margin: 0;
  background-color: #f5f7fa;
}

.guide-container :deep(.el-tabs__content) {
  padding: 0;
  height: calc(100% - 40px);
  overflow: hidden;
}
</style>
