<template>
  <div class="init-containers-container">
    <div class="header">
      <h1>初始化容器组件</h1>
      <el-button type="primary" @click="goBack">返回</el-button>
    </div>

    <div class="content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>容器组件初始化</span>
          </div>
        </template>
        <div class="card-content">
          <p>点击下面的按钮初始化新的容器组件：</p>
          <ul>
            <li>卡片容器 (card-container)</li>
            <li>标签页容器 (tab-container)</li>
            <li>折叠面板容器 (collapse-container)</li>
            <li>网格容器 (grid-container)</li>
          </ul>
          <el-button type="primary" @click="initContainers" :loading="loading">初始化容器组件</el-button>
          
          <div v-if="result" class="result-container">
            <h3>初始化结果：</h3>
            <pre>{{ JSON.stringify(result, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '../../api/index'

const router = useRouter()
const loading = ref(false)
const result = ref(null)

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化容器组件
const initContainers = async () => {
  loading.value = true
  result.value = null
  
  try {
    const response = await request({
      url: '/api/component-init/containers',
      method: 'post'
    })
    
    result.value = response
    
    if (response && response.code === 200) {
      ElMessage.success('容器组件初始化成功')
    } else {
      ElMessage.warning('容器组件初始化返回未知状态')
    }
  } catch (error) {
    console.error('Failed to initialize container components:', error)
    ElMessage.error('容器组件初始化失败: ' + (error.message || '未知错误'))
    result.value = { error: error.message || '未知错误' }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.init-containers-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-container {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
