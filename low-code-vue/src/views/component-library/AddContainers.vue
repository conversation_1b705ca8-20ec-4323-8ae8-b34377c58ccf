<template>
  <div class="add-containers-container">
    <div class="header">
      <h1>添加容器组件</h1>
      <el-button type="primary" @click="goBack">返回</el-button>
    </div>

    <div class="content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>容器组件添加</span>
          </div>
        </template>
        <div class="card-content">
          <p>点击下面的按钮将新的容器组件直接添加到组件库：</p>
          <ul>
            <li>卡片容器 (card-container)</li>
            <li>标签页容器 (tab-container)</li>
            <li>折叠面板容器 (collapse-container)</li>
            <li>网格容器 (grid-container)</li>
          </ul>
          <el-button type="primary" @click="addContainers" :loading="loading">添加容器组件</el-button>
          
          <div v-if="result" class="result-container">
            <h3>添加结果：</h3>
            <pre>{{ JSON.stringify(result, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useEditorStore } from '../../store'

const router = useRouter()
const editorStore = useEditorStore()
const loading = ref(false)
const result = ref(null)

// 返回上一页
const goBack = () => {
  router.back()
}

// 添加容器组件
const addContainers = async () => {
  loading.value = true
  result.value = null
  
  try {
    // 创建新的容器组件
    const containerComponents = [
      {
        type: 'card-container',
        name: '卡片容器',
        icon: 'Document',
        category: 'layout',
        enabled: 1,
        defaultProps: {
          title: '卡片标题',
          shadow: 'always',
          children: []
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0',
          padding: '0'
        }
      },
      {
        type: 'tab-container',
        name: '标签页容器',
        icon: 'Menu',
        category: 'layout',
        enabled: 1,
        defaultProps: {
          tabs: [
            { title: '标签页1', name: 'tab1', children: [] },
            { title: '标签页2', name: 'tab2', children: [] }
          ],
          activeName: 'tab1'
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'collapse-container',
        name: '折叠面板容器',
        icon: 'ArrowDown',
        category: 'layout',
        enabled: 1,
        defaultProps: {
          items: [
            { title: '面板1', name: 'panel1', children: [] },
            { title: '面板2', name: 'panel2', children: [] }
          ],
          activeNames: ['panel1']
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      },
      {
        type: 'grid-container',
        name: '网格容器',
        icon: 'Grid',
        category: 'layout',
        enabled: 1,
        defaultProps: {
          cols: 3,
          gutter: 20,
          children: []
        },
        defaultStyles: {
          width: '100%',
          margin: '10px 0'
        }
      }
    ]
    
    // 检查组件库中是否已经存在这些组件
    const existingComponents = editorStore.componentLibrary.map(c => c.type)
    const componentsToAdd = containerComponents.filter(c => !existingComponents.includes(c.type))
    
    if (componentsToAdd.length === 0) {
      ElMessage.warning('所有容器组件已经存在于组件库中')
      result.value = { message: '所有容器组件已经存在于组件库中', added: 0 }
      loading.value = false
      return
    }
    
    // 将新组件添加到组件库
    const addedComponents = []
    for (const component of componentsToAdd) {
      try {
        // 使用EditorStore的saveComponentToLibrary方法保存组件
        const savedComponent = await editorStore.saveComponentToLibrary(component)
        addedComponents.push(savedComponent)
      } catch (error) {
        console.error(`Failed to add component ${component.type}:`, error)
      }
    }
    
    result.value = {
      message: `成功添加 ${addedComponents.length} 个容器组件`,
      added: addedComponents.length,
      components: addedComponents.map(c => c.type)
    }
    
    if (addedComponents.length > 0) {
      ElMessage.success(`成功添加 ${addedComponents.length} 个容器组件`)
    } else {
      ElMessage.warning('没有添加任何容器组件')
    }
  } catch (error) {
    console.error('Failed to add container components:', error)
    ElMessage.error('添加容器组件失败: ' + (error.message || '未知错误'))
    result.value = { error: error.message || '未知错误' }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.add-containers-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-container {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
