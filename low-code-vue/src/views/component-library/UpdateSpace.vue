<template>
  <div class="update-space-container">
    <div class="header">
      <h1>更新间距组件</h1>
      <el-button type="primary" @click="goBack">返回</el-button>
    </div>

    <div class="content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>间距组件更新</span>
          </div>
        </template>
        <div class="card-content">
          <p>点击下面的按钮更新间距组件的属性：</p>
          <ul>
            <li>添加方向属性（水平/垂直）</li>
            <li>添加间距大小属性（小/默认/大/自定义）</li>
            <li>添加是否换行属性</li>
            <li>添加对齐方式属性</li>
            <li>添加子项数量属性</li>
            <li>添加子项类型属性（卡片/按钮/文本）</li>
          </ul>
          <el-button type="primary" @click="updateSpace" :loading="loading">更新间距组件</el-button>
          
          <div v-if="result" class="result-container">
            <h3>更新结果：</h3>
            <pre>{{ JSON.stringify(result, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '../../api/index'

const router = useRouter()
const loading = ref(false)
const result = ref(null)

// 返回上一页
const goBack = () => {
  router.back()
}

// 更新间距组件
const updateSpace = async () => {
  loading.value = true
  result.value = null
  
  try {
    const response = await request({
      url: '/api/component-init/update-space',
      method: 'post'
    })
    
    result.value = response
    
    if (response && response.code === 200) {
      ElMessage.success('间距组件更新成功')
    } else {
      ElMessage.warning('间距组件更新返回未知状态')
    }
  } catch (error) {
    console.error('Failed to update space component:', error)
    ElMessage.error('间距组件更新失败: ' + (error.message || '未知错误'))
    result.value = { error: error.message || '未知错误' }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.update-space-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-container {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
