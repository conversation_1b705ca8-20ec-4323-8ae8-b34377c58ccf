<template>
  <div class="add-input-components">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>添加输入框组件</h3>
        </div>
      </template>
      
      <div class="card-content">
        <p>点击下方按钮将新的输入框组件添加到组件库中。</p>
        
        <el-button type="primary" @click="addComponents" :loading="loading">
          添加输入框组件
        </el-button>
        
        <div v-if="result" class="result-message">
          <el-alert
            :title="result.message"
            :type="result.added > 0 ? 'success' : 'info'"
            :closable="false"
            show-icon
          >
            <div v-if="result.added > 0">
              成功添加了 {{ result.added }} 个组件
            </div>
          </el-alert>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useEditorStore } from '@/store'
import { ElMessage, ElLoading } from 'element-plus'

const editorStore = useEditorStore()
const loading = ref(false)
const result = ref(null)

// 输入框组件定义
const inputComponents = [
  {
    type: 'number-input',
    name: '数字输入框',
    icon: 'Odometer',
    category: 'form',
    defaultProps: {
      defaultValue: 0,
      min: -Infinity,
      max: Infinity,
      step: 1,
      precision: 0,
      stepStrictly: false,
      controls: true,
      disabled: false,
      placeholder: '请输入数字'
    },
    defaultStyles: {
      width: '100%',
      margin: '0 0 15px 0'
    }
  },
  {
    type: 'password-input',
    name: '密码输入框',
    icon: 'Lock',
    category: 'form',
    defaultProps: {
      defaultValue: '',
      placeholder: '请输入密码',
      showPasswordToggle: true,
      clearable: true,
      disabled: false
    },
    defaultStyles: {
      width: '100%',
      margin: '0 0 15px 0'
    }
  },
  {
    type: 'search-input',
    name: '搜索输入框',
    icon: 'Search',
    category: 'form',
    defaultProps: {
      defaultValue: '',
      placeholder: '请输入搜索内容',
      showPrefix: true,
      showButton: true,
      buttonText: '搜索',
      buttonType: 'primary',
      clearable: true,
      disabled: false
    },
    defaultStyles: {
      width: '100%',
      margin: '0 0 15px 0'
    }
  }
]

// 添加组件到组件库
const addComponents = async () => {
  loading.value = true
  result.value = null
  
  try {
    // 检查组件库中是否已经存在这些组件
    const existingComponents = editorStore.componentLibrary.map(c => c.type)
    const componentsToAdd = inputComponents.filter(c => !existingComponents.includes(c.type))
    
    if (componentsToAdd.length === 0) {
      ElMessage.warning('所有输入框组件已经存在于组件库中')
      result.value = { message: '所有输入框组件已经存在于组件库中', added: 0 }
      loading.value = false
      return
    }
    
    // 将新组件添加到组件库
    const addedComponents = []
    for (const component of componentsToAdd) {
      try {
        // 使用EditorStore的saveComponentToLibrary方法保存组件
        const savedComponent = await editorStore.saveComponentToLibrary(component)
        addedComponents.push(savedComponent)
      } catch (error) {
        console.error(`Failed to add component ${component.type}:`, error)
      }
    }
    
    // 更新结果
    result.value = {
      message: addedComponents.length > 0 ? '输入框组件添加成功' : '添加组件失败',
      added: addedComponents.length
    }
    
    if (addedComponents.length > 0) {
      ElMessage.success(`成功添加了 ${addedComponents.length} 个输入框组件`)
    }
  } catch (error) {
    console.error('Failed to add input components:', error)
    ElMessage.error('添加输入框组件失败')
    result.value = { message: '添加输入框组件失败', added: 0 }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.add-input-components {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  padding: 10px 0;
}

.result-message {
  margin-top: 20px;
}
</style>
