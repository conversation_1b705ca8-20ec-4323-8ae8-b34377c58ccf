<template>
  <div class="image-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>图片管理</h2>
      <p>管理系统中的所有图片资源，支持上传、删除、分类管理等功能</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showUploadDialog = true">
          <el-icon><Plus /></el-icon>
          上传图片
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedImages.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索图片名称或备注"
          style="width: 200px; margin-right: 10px;"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="图片分类:">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable>
            <el-option 
              v-for="(name, value) in CATEGORY_NAMES" 
              :key="value" 
              :label="name" 
              :value="parseInt(value)"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态:">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="正常" :value="0" />
            <el-option label="已删除" :value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="上传时间:">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 图片列表 -->
    <div class="image-list">
      <el-table 
        :data="imageList" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="预览" width="100">
          <template #default="{ row }">
            <el-image
              :src="row.url"
              :preview-src-list="[row.url]"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-teleported="true"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="originalName" label="文件名" min-width="150" />
        
        <el-table-column label="分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">
              {{ CATEGORY_NAMES[row.category] || '未知' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.size) }}
          </template>
        </el-table-column>
        
        <el-table-column label="尺寸" width="100">
          <template #default="{ row }">
            {{ row.width }}×{{ row.height }}
          </template>
        </el-table-column>
        
        <el-table-column prop="uploaderName" label="上传者" width="100" />
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 0 ? 'success' : 'danger'">
              {{ row.status === 0 ? '正常' : '已删除' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="上传时间" width="160" />
        
        <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewImageDetail(row)">详情</el-button>
            <el-button size="small" @click="downloadImage(row)">下载</el-button>
            <el-button 
              v-if="row.status === 0"
              size="small" 
              type="danger" 
              @click="deleteImage(row)"
            >
              删除
            </el-button>
            <el-button 
              v-else
              size="small" 
              type="success" 
              @click="restoreImage(row)"
            >
              恢复
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传图片" width="600px">
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :multiple="true"
          :file-list="uploadFileList"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleRemove"
          list-type="picture-card"
          accept="image/*"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        
        <div class="upload-form">
          <el-form :model="uploadForm" label-width="80px">
            <el-form-item label="分类:">
              <el-select v-model="uploadForm.category" placeholder="选择分类">
                <el-option 
                  v-for="(name, value) in CATEGORY_NAMES" 
                  :key="value" 
                  :label="name" 
                  :value="parseInt(value)"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="备注:">
              <el-input 
                v-model="uploadForm.remark" 
                type="textarea" 
                placeholder="请输入备注信息"
                :rows="3"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpload">开始上传</el-button>
      </template>
    </el-dialog>

    <!-- 图片详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="图片详情" width="800px">
      <div v-if="currentImage" class="image-detail">
        <div class="detail-image">
          <el-image :src="currentImage.url" fit="contain" style="max-height: 400px;" />
        </div>
        
        <div class="detail-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">{{ currentImage.originalName }}</el-descriptions-item>
            <el-descriptions-item label="分类">{{ CATEGORY_NAMES[currentImage.category] }}</el-descriptions-item>
            <el-descriptions-item label="大小">{{ formatFileSize(currentImage.size) }}</el-descriptions-item>
            <el-descriptions-item label="尺寸">{{ currentImage.width }}×{{ currentImage.height }}</el-descriptions-item>
            <el-descriptions-item label="格式">{{ currentImage.contentType }}</el-descriptions-item>
            <el-descriptions-item label="MD5">{{ currentImage.md5 }}</el-descriptions-item>
            <el-descriptions-item label="上传者">{{ currentImage.uploaderName }}</el-descriptions-item>
            <el-descriptions-item label="上传时间">{{ currentImage.createTime }}</el-descriptions-item>
            <el-descriptions-item label="访问URL" span="2">
              <el-input :value="currentImage.url" readonly>
                <template #append>
                  <el-button @click="copyUrl(currentImage.url)">复制</el-button>
                </template>
              </el-input>
            </el-descriptions-item>
            <el-descriptions-item label="备注" span="2">{{ currentImage.remark || '无' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Search } from '@element-plus/icons-vue'
import { 
  getImagePage, 
  deleteImage as deleteImageApi,
  deleteImages as deleteImagesApi,
  restoreImage as restoreImageApi,
  downloadImage as downloadImageApi,
  uploadImages,
  CATEGORY_NAMES,
  IMAGE_CATEGORIES
} from '../api/image'

// 响应式数据
const loading = ref(false)
const imageList = ref([])
const selectedImages = ref([])
const showUploadDialog = ref(false)
const showDetailDialog = ref(false)
const currentImage = ref(null)
const uploadFileList = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: null,
  status: null,
  dateRange: null
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 上传表单
const uploadForm = reactive({
  category: IMAGE_CATEGORIES.USER_UPLOAD,
  remark: ''
})

// 上传配置
const uploadAction = computed(() => '/api/images/upload/batch')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}))
const uploadData = computed(() => ({
  category: uploadForm.category,
  remark: uploadForm.remark
}))

// 页面加载时获取数据
onMounted(() => {
  loadImageList()
})

// 加载图片列表
const loadImageList = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword || undefined,
      category: searchForm.category || undefined,
      status: searchForm.status !== null ? searchForm.status : undefined,
      startTime: searchForm.dateRange?.[0] || undefined,
      endTime: searchForm.dateRange?.[1] || undefined
    }
    
    const response = await getImagePage(params)
    if (response.code === 200) {
      imageList.value = response.data.records
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取图片列表失败')
    }
  } catch (error) {
    console.error('获取图片列表失败:', error)
    ElMessage.error('获取图片列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadImageList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    category: null,
    status: null,
    dateRange: null
  })
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedImages.value = selection
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadImageList()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  loadImageList()
}

// 获取分类标签类型
const getCategoryTagType = (category) => {
  const types = {
    1: 'primary',  // 组件图标
    2: 'success',  // 用户上传
    3: 'warning',  // 系统图片
    4: 'info'      // 模板缩略图
  }
  return types[category] || 'default'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 查看图片详情
const viewImageDetail = (image) => {
  currentImage.value = image
  showDetailDialog.value = true
}

// 复制URL
const copyUrl = async (url) => {
  try {
    await navigator.clipboard.writeText(url)
    ElMessage.success('URL已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下载图片
const downloadImage = async (image) => {
  try {
    const response = await downloadImageApi(image.id)
    const blob = new Blob([response])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = image.originalName
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 删除图片
const deleteImage = async (image) => {
  try {
    await ElMessageBox.confirm('确定要删除这张图片吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await deleteImageApi(image.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadImageList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedImages.value.length} 张图片吗？`, '确认批量删除', {
      type: 'warning'
    })
    
    const ids = selectedImages.value.map(item => item.id)
    const response = await deleteImagesApi(ids)
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      selectedImages.value = []
      loadImageList()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 恢复图片
const restoreImage = async (image) => {
  try {
    await ElMessageBox.confirm('确定要恢复这张图片吗？', '确认恢复', {
      type: 'info'
    })
    
    const response = await restoreImageApi(image.id)
    if (response.code === 200) {
      ElMessage.success('恢复成功')
      loadImageList()
    } else {
      ElMessage.error(response.message || '恢复失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复失败:', error)
      ElMessage.error('恢复失败')
    }
  }
}

// 上传前检查
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过10MB!')
    return false
  }
  
  return true
}

// 开始上传
const handleUpload = () => {
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的图片')
    return
  }
  
  // 触发上传
  uploadRef.value.submit()
}

// 上传成功
const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    ElMessage.success(`${file.name} 上传成功`)
  } else {
    ElMessage.error(`${file.name} 上传失败: ${response.message}`)
  }
}

// 上传失败
const handleUploadError = (error, file) => {
  console.error('上传失败:', error)
  ElMessage.error(`${file.name} 上传失败`)
}

// 移除文件
const handleRemove = (file) => {
  const index = uploadFileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    uploadFileList.value.splice(index, 1)
  }
}

// 关闭上传对话框时重置
const closeUploadDialog = () => {
  uploadFileList.value = []
  uploadForm.category = IMAGE_CATEGORIES.USER_UPLOAD
  uploadForm.remark = ''
  loadImageList() // 重新加载列表
}
</script>

<style scoped>
.image-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.filter-bar {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.image-list {
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
}

.upload-area {
  display: flex;
  gap: 20px;
}

.upload-form {
  flex: 1;
}

.image-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-image {
  text-align: center;
}

.detail-info {
  flex: 1;
}
</style>
