<template>
  <div class="component-enable-view">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h2>组件启用管理</h2>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索组件或分类"
              prefix-icon="Search"
              clearable
              style="width: 250px; margin-right: 10px"
            />
            <el-button type="primary" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="组件管理" name="components">
          <div class="table-actions">
            <el-button type="success" @click="batchEnableComponents" :disabled="!hasComponentSelection">批量启用</el-button>
            <el-button type="danger" @click="batchDisableComponents" :disabled="!hasComponentSelection">批量禁用</el-button>
          </div>
          
          <el-table
            :data="filteredComponents"
            border
            style="width: 100%"
            @selection-change="handleComponentSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="组件名称" />
            <el-table-column prop="type" label="组件类型" />
            <el-table-column prop="category" label="所属分类" />
            <el-table-column label="图标" width="80">
              <template #default="scope">
                <div class="icon-preview">
                  <template v-if="isBase64Icon(scope.row.icon)">
                    <img :src="scope.row.icon" class="component-icon" alt="icon" />
                  </template>
                  <template v-else>
                    <el-icon><component :is="scope.row.icon || 'Document'" /></el-icon>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.enabled === 1 ? 'success' : 'danger'">
                  {{ scope.row.enabled === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template #default="scope">
                <el-button
                  v-if="scope.row.enabled === 0"
                  type="success"
                  size="small"
                  @click="enableComponent(scope.row)"
                >
                  启用
                </el-button>
                <el-button
                  v-else
                  type="danger"
                  size="small"
                  @click="disableComponent(scope.row)"
                >
                  禁用
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="分类管理" name="categories">
          <div class="table-actions">
            <el-button type="success" @click="batchEnableCategories" :disabled="!hasCategorySelection">批量启用</el-button>
            <el-button type="danger" @click="batchDisableCategories" :disabled="!hasCategorySelection">批量禁用</el-button>
          </div>
          
          <el-table
            :data="filteredCategories"
            border
            style="width: 100%"
            @selection-change="handleCategorySelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="分类名称" />
            <el-table-column prop="code" label="分类代码" />
            <el-table-column label="图标" width="80">
              <template #default="scope">
                <div class="icon-preview">
                  <template v-if="isBase64Icon(scope.row.icon)">
                    <img :src="scope.row.icon" class="component-icon" alt="icon" />
                  </template>
                  <template v-else>
                    <el-icon><component :is="scope.row.icon || 'Folder'" /></el-icon>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.enabled === 1 ? 'success' : 'danger'">
                  {{ scope.row.enabled === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template #default="scope">
                <el-button
                  v-if="scope.row.enabled === 0"
                  type="success"
                  size="small"
                  @click="enableCategory(scope.row)"
                >
                  启用
                </el-button>
                <el-button
                  v-else
                  type="danger"
                  size="small"
                  @click="disableCategory(scope.row)"
                >
                  禁用
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { componentApi } from '../api/component'
import { componentCategoryApi } from '../api/componentCategory'
import { componentEnableApi } from '../api/componentEnable'
import { ElMessageBox } from 'element-plus'

// Active tab
const activeTab = ref('components')

// Search text
const searchText = ref('')

// Components and categories data
const components = ref([])
const categories = ref([])

// Selected items
const selectedComponents = ref([])
const selectedCategories = ref([])

// Computed properties for selection state
const hasComponentSelection = computed(() => selectedComponents.value.length > 0)
const hasCategorySelection = computed(() => selectedCategories.value.length > 0)

// Filtered components based on search text
const filteredComponents = computed(() => {
  if (!searchText.value) {
    return components.value
  }
  
  const searchLower = searchText.value.toLowerCase()
  return components.value.filter(component => {
    return (
      component.name.toLowerCase().includes(searchLower) ||
      component.type.toLowerCase().includes(searchLower) ||
      (component.category && component.category.toLowerCase().includes(searchLower))
    )
  })
})

// Filtered categories based on search text
const filteredCategories = computed(() => {
  if (!searchText.value) {
    return categories.value
  }
  
  const searchLower = searchText.value.toLowerCase()
  return categories.value.filter(category => {
    return (
      category.name.toLowerCase().includes(searchLower) ||
      category.code.toLowerCase().includes(searchLower)
    )
  })
})

// Check if icon is a base64 string
const isBase64Icon = (icon) => {
  return icon && typeof icon === 'string' && icon.startsWith('data:')
}

// Load data
const loadData = async () => {
  try {
    // Load components
    const componentsResponse = await componentApi.getAllComponents()
    components.value = componentsResponse
    
    // Load categories
    const categoriesResponse = await componentCategoryApi.getAllCategories()
    categories.value = categoriesResponse
  } catch (error) {
    console.error('Failed to load data:', error)
  }
}

// Refresh data
const refreshData = () => {
  loadData()
}

// Handle selection change
const handleComponentSelectionChange = (selection) => {
  selectedComponents.value = selection
}

const handleCategorySelectionChange = (selection) => {
  selectedCategories.value = selection
}

// Enable/disable component
const enableComponent = async (component) => {
  try {
    await componentEnableApi.enableComponent(component.id)
    // Refresh data after enabling
    refreshData()
  } catch (error) {
    console.error('Failed to enable component:', error)
  }
}

const disableComponent = async (component) => {
  try {
    await ElMessageBox.confirm(
      '禁用组件后，前端将不再显示该组件。确定要禁用吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await componentEnableApi.disableComponent(component.id)
    // Refresh data after disabling
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to disable component:', error)
    }
  }
}

// Batch enable/disable components
const batchEnableComponents = async () => {
  try {
    const ids = selectedComponents.value.map(component => component.id)
    await componentEnableApi.batchEnableComponents(ids)
    // Refresh data after batch enabling
    refreshData()
  } catch (error) {
    console.error('Failed to batch enable components:', error)
  }
}

const batchDisableComponents = async () => {
  try {
    await ElMessageBox.confirm(
      '批量禁用组件后，前端将不再显示这些组件。确定要禁用吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedComponents.value.map(component => component.id)
    await componentEnableApi.batchDisableComponents(ids)
    // Refresh data after batch disabling
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to batch disable components:', error)
    }
  }
}

// Enable/disable category
const enableCategory = async (category) => {
  try {
    await componentEnableApi.enableCategory(category.id)
    // Refresh data after enabling
    refreshData()
  } catch (error) {
    console.error('Failed to enable category:', error)
  }
}

const disableCategory = async (category) => {
  try {
    await ElMessageBox.confirm(
      '禁用分类后，前端将不再显示该分类及其下的所有组件。确定要禁用吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await componentEnableApi.disableCategory(category.id)
    // Refresh data after disabling
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to disable category:', error)
    }
  }
}

// Batch enable/disable categories
const batchEnableCategories = async () => {
  try {
    const ids = selectedCategories.value.map(category => category.id)
    await componentEnableApi.batchEnableCategories(ids)
    // Refresh data after batch enabling
    refreshData()
  } catch (error) {
    console.error('Failed to batch enable categories:', error)
  }
}

const batchDisableCategories = async () => {
  try {
    await ElMessageBox.confirm(
      '批量禁用分类后，前端将不再显示这些分类及其下的所有组件。确定要禁用吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedCategories.value.map(category => category.id)
    await componentEnableApi.batchDisableCategories(ids)
    // Refresh data after batch disabling
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to batch disable categories:', error)
    }
  }
}

// Load data on mount
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.component-enable-view {
  padding: 20px;
}

.page-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.table-actions {
  margin-bottom: 15px;
}

.icon-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.component-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}
</style>
