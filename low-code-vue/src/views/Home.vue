<template>
  <div class="home-container">
    <div class="hero-section">
      <div class="hero-content">
        <h1>Build Web Applications Without Coding</h1>
        <p>Create, design, and deploy web applications with our intuitive low-code platform</p>
        <div class="hero-buttons">
          <el-button type="primary" size="large" @click="goToRegister">Get Started</el-button>
          <el-button size="large" @click="goToLogin">Sign In</el-button>
        </div>
      </div>
      <div class="hero-image">
        <img src="../assets/hero-image.svg" alt="Low-Code Platform" />
      </div>
    </div>
    
    <div class="features-section">
      <h2>Key Features</h2>
      <div class="features-grid">
        <div class="feature-card">
          <el-icon size="40"><Pointer /></el-icon>
          <h3>Drag & Drop Interface</h3>
          <p>Build interfaces by dragging and dropping components onto the canvas</p>
        </div>
        <div class="feature-card">
          <el-icon size="40"><Connection /></el-icon>
          <h3>Visual Workflow Builder</h3>
          <p>Create complex workflows without writing code</p>
        </div>
        <div class="feature-card">
          <el-icon size="40"><DataAnalysis /></el-icon>
          <h3>Data Integration</h3>
          <p>Connect to various data sources with ease</p>
        </div>
        <div class="feature-card">
          <el-icon size="40"><Monitor /></el-icon>
          <h3>Responsive Design</h3>
          <p>Create applications that work on any device</p>
        </div>
      </div>
    </div>
    
    <div class="cta-section">
      <h2>Ready to Get Started?</h2>
      <p>Join thousands of users building applications with our platform</p>
      <el-button type="primary" size="large" @click="goToRegister">Create Free Account</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Pointer, Connection, DataAnalysis, Monitor } from '@element-plus/icons-vue'

const router = useRouter()

const goToLogin = () => {
  router.push('/login')
}

const goToRegister = () => {
  router.push('/register')
}
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.hero-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 40px 0;
  gap: 40px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #303133;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: #606266;
}

.hero-buttons {
  display: flex;
  gap: 15px;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
}

.features-section {
  margin: 60px 0;
  text-align: center;
}

.features-section h2 {
  font-size: 2rem;
  margin-bottom: 40px;
  color: #303133;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  padding: 30px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.feature-card .el-icon {
  color: #409eff;
  margin-bottom: 15px;
}

.feature-card h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #303133;
}

.feature-card p {
  color: #606266;
}

.cta-section {
  margin: 60px 0;
  text-align: center;
  padding: 60px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.cta-section h2 {
  font-size: 2rem;
  margin-bottom: 15px;
  color: #303133;
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: #606266;
}

@media (max-width: 768px) {
  .hero-section {
    flex-direction: column;
  }
  
  .hero-content h1 {
    font-size: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
