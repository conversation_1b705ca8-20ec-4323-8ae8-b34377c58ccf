<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>Low-Code Platform</h2>
        <p>Login to your account</p>
      </div>

      <el-form :model="loginForm" :rules="rules" ref="loginFormRef" class="login-form">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" placeholder="Username" prefix-icon="User" />
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" placeholder="Password" prefix-icon="Lock" show-password />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" class="login-button" @click="handleLogin">Login</el-button>
        </el-form-item>

        <div class="login-options">
          <el-button link @click="goToRegister">Register</el-button>
          <el-button link @click="forgetPassword">Forgot Password?</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { userApi } from '../api'
import { useUserStore } from '../store'

const router = useRouter()
const userStore = useUserStore()

// Login form
const loginFormRef = ref(null)
const loginForm = reactive({
  username: '',
  password: ''
})

// Form validation rules
const rules = {
  username: [
    { required: true, message: 'Please enter username', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter password', trigger: 'blur' }
  ]
}

// Loading state
const loading = ref(false)

// Handle login
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true

      try {
        const result = await userApi.login(loginForm)

        // Save user info and token
        userStore.setUserInfo(result.user)
        userStore.setToken(result.token)

        ElMessage.success('Login successful')

        // Check if there's a redirect path
        const redirect = localStorage.getItem('redirect')
        if (redirect) {
          localStorage.removeItem('redirect')
          router.push(redirect)
        } else {
          router.push('/dashboard')
        }
      } catch (error) {
        console.error('Login failed:', error)
      } finally {
        loading.value = false
      }
    }
  })
}

// Go to register page
const goToRegister = () => {
  router.push('/register')
}

// Forgot password
const forgetPassword = () => {
  ElMessage.info('Password reset functionality is under development')
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.login-box {
  width: 400px;
  padding: 40px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  margin: 0 0 10px;
  font-size: 24px;
  color: #409eff;
}

.login-header p {
  margin: 0;
  color: #606266;
}

.login-form {
  margin-top: 20px;
}

.login-button {
  width: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}
</style>
