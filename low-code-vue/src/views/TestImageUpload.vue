<template>
  <div class="test-image-upload">
    <el-card header="图片上传测试页面">
      <div class="upload-section">
        <h3>基础图片上传测试</h3>
        <ImageUploader 
          v-model="imageUrl"
          :category="IMAGE_CATEGORIES.USER_UPLOAD"
          business-type="test"
          :business-id="1"
          remark="测试上传"
          @upload-success="handleUploadSuccess"
        />
        
        <div v-if="imageUrl" class="result-section">
          <h4>上传结果:</h4>
          <p><strong>图片URL:</strong> {{ imageUrl }}</p>
          <img :src="imageUrl" alt="上传的图片" style="max-width: 300px; max-height: 200px;" />
        </div>
      </div>

      <div class="debug-section">
        <h3>存储服务测试</h3>
        <el-button @click="testStorageConnection">测试存储连接</el-button>
        <el-button @click="testApiConnection">测试API连接</el-button>

        <div v-if="debugInfo" class="debug-info">
          <h4>存储服务状态:</h4>
          <pre>{{ debugInfo }}</pre>
        </div>
      </div>

      <div class="logs-section">
        <h3>上传日志</h3>
        <div class="logs">
          <div v-for="(log, index) in uploadLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-level" :class="log.level">{{ log.level }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import ImageUploader from '@/components/editor/ImageUploader.vue'
import { IMAGE_CATEGORIES } from '@/api/image'
import { request } from '@/api/api'

// 响应式数据
const imageUrl = ref('')
const debugInfo = ref('')
const uploadLogs = reactive([])

// 添加日志
const addLog = (level, message) => {
  uploadLogs.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
  // 只保留最近20条日志
  if (uploadLogs.length > 20) {
    uploadLogs.splice(20)
  }
}

// 上传成功回调
const handleUploadSuccess = (imageData) => {
  addLog('SUCCESS', `图片上传成功: ${imageData.originalName}`)
  addLog('INFO', `图片ID: ${imageData.id}`)
  addLog('INFO', `图片URL: ${imageData.url}`)
  addLog('INFO', `文件大小: ${imageData.size} bytes`)
  
  console.log('上传成功回调:', imageData)
}

// 测试存储连接
const testStorageConnection = async () => {
  try {
    addLog('INFO', '开始测试存储服务连接...')

    const response = await request({
      url: '/images/test/minio',
      method: 'get'
    })

    if (response.code === 200) {
      const data = response.data
      addLog('SUCCESS', `存储服务测试成功`)
      addLog('INFO', `当前存储类型: ${data.currentStorageType}`)
      addLog('INFO', `MinIO状态: ${data.minioStatus || data.minioAvailable ? 'AVAILABLE' : 'UNAVAILABLE'}`)
      addLog('INFO', `本地存储状态: ${data.localStatus || data.localAvailable ? 'AVAILABLE' : 'UNAVAILABLE'}`)
      addLog('INFO', `回退到本地存储: ${data.fallbackToLocal ? '启用' : '禁用'}`)

      debugInfo.value = JSON.stringify(data, null, 2)
    } else {
      addLog('ERROR', `存储服务测试失败: ${response.message}`)
    }
  } catch (error) {
    addLog('ERROR', `存储服务测试异常: ${error.message}`)
    console.error('存储服务测试失败:', error)
  }
}

// 测试API连接
const testApiConnection = async () => {
  try {
    addLog('INFO', '开始测试API连接...')
    
    const response = await request({
      url: '/images/page',
      method: 'get',
      params: {
        current: 1,
        size: 1
      }
    })
    
    if (response.code === 200) {
      addLog('SUCCESS', 'API连接测试成功')
      debugInfo.value = JSON.stringify(response.data, null, 2)
    } else {
      addLog('ERROR', `API连接测试失败: ${response.message}`)
    }
  } catch (error) {
    addLog('ERROR', `API连接测试异常: ${error.message}`)
    console.error('API连接测试失败:', error)
  }
}

// 页面加载时添加初始日志
addLog('INFO', '图片上传测试页面已加载')
</script>

<style scoped>
.test-image-upload {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-section {
  margin-bottom: 30px;
}

.result-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.debug-section {
  margin-bottom: 30px;
}

.debug-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.debug-info pre {
  margin: 0;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.logs-section {
  margin-top: 30px;
}

.logs {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
  min-width: 80px;
}

.log-level {
  margin-right: 10px;
  min-width: 60px;
  font-weight: bold;
}

.log-level.INFO {
  color: #409eff;
}

.log-level.SUCCESS {
  color: #67c23a;
}

.log-level.ERROR {
  color: #f56c6c;
}

.log-message {
  flex: 1;
}

h3 {
  color: #303133;
  margin-bottom: 15px;
}

h4 {
  color: #606266;
  margin-bottom: 10px;
}
</style>
