<template>
  <div class="component-events-view">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h2>组件事件管理</h2>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索组件"
              prefix-icon="Search"
              clearable
              style="width: 250px; margin-right: 10px"
            />
            <el-button type="primary" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="组件事件管理" name="components">
          <div class="table-actions">
            <el-button type="danger" @click="batchClearEvents" :disabled="!hasComponentSelection">批量清空事件</el-button>
          </div>

          <el-table
            :data="filteredComponents"
            border
            style="width: 100%"
            @selection-change="handleComponentSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="组件名称" width="180">
              <template #default="{ row }">
                <div class="component-name">
                  <template v-if="isBase64Icon(row.icon)">
                    <img :src="row.icon" class="component-icon" alt="component icon" />
                  </template>
                  <template v-else>
                    <el-icon>
                      <component :is="row.icon || 'Document'" />
                    </el-icon>
                  </template>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="组件类型" width="120" />
            <el-table-column prop="category" label="组件分类" width="120" />
            <el-table-column label="事件数量" width="100">
              <template #default="scope">
                <el-tag :type="getEventCountTagType(scope.row)">
                  {{ getEventCount(scope.row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="editComponentEvents(scope.row)"
                >
                  编辑事件
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="clearComponentEvents(scope.row)"
                  :disabled="!hasEvents(scope.row)"
                >
                  清空事件
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 编辑事件对话框 -->
    <el-dialog
      v-model="eventDialogVisible"
      title="编辑组件事件"
      width="70%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div v-if="currentComponent" class="event-dialog-content">
        <div class="component-info">
          <h3>{{ currentComponent.name }} ({{ currentComponent.type }})</h3>
        </div>

        <div class="events-editor-container">
          <component-events-editor
            :component="currentComponent"
            :available-components="components"
            :available-pages="[]"
            @update="updateComponentWithEvents"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="eventDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveComponentEvents">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { componentApi } from '../api/component'
import { componentEventApi } from '../api/componentEvent'
import { ElMessageBox } from 'element-plus'
import ComponentEventsEditor from '../components/editor/ComponentEventsEditor.vue'

// 活动标签页
const activeTab = ref('components')

// 搜索文本
const searchText = ref('')

// 组件数据
const components = ref([])

// 选中的组件
const selectedComponents = ref([])

// 当前编辑的组件
const currentComponent = ref(null)

// 事件对话框可见性
const eventDialogVisible = ref(false)

// 计算属性：是否有选中的组件
const hasComponentSelection = computed(() => selectedComponents.value.length > 0)

// 计算属性：过滤后的组件列表
const filteredComponents = computed(() => {
  if (!searchText.value) {
    return components.value
  }

  const search = searchText.value.toLowerCase()
  return components.value.filter(component =>
    component.name.toLowerCase().includes(search) ||
    component.type.toLowerCase().includes(search) ||
    (component.category && component.category.toLowerCase().includes(search))
  )
})

// 加载数据
const loadData = async () => {
  try {
    // 加载组件
    const componentsResponse = await componentApi.getAllComponents()
    components.value = componentsResponse
  } catch (error) {
    console.error('Failed to load data:', error)
  }
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 处理组件选择变化
const handleComponentSelectionChange = (selection) => {
  selectedComponents.value = selection
}

// 检查图标是否为Base64格式
const isBase64Icon = (icon) => {
  return icon && typeof icon === 'string' && icon.startsWith('data:')
}

// 获取组件事件数量
const getEventCount = (component) => {
  if (!component.events) {
    return 0
  }

  try {
    const events = JSON.parse(component.events)
    return Array.isArray(events) ? events.length : 0
  } catch (e) {
    return 0
  }
}

// 获取事件数量标签类型
const getEventCountTagType = (component) => {
  const count = getEventCount(component)
  if (count === 0) {
    return 'info'
  } else if (count <= 2) {
    return 'success'
  } else if (count <= 5) {
    return 'warning'
  } else {
    return 'danger'
  }
}

// 检查组件是否有事件
const hasEvents = (component) => {
  return getEventCount(component) > 0
}

// 编辑组件事件
const editComponentEvents = (component) => {
  currentComponent.value = JSON.parse(JSON.stringify(component))

  // 确保events是一个数组
  if (!currentComponent.value.events) {
    currentComponent.value.events = []
  } else if (typeof currentComponent.value.events === 'string') {
    try {
      currentComponent.value.events = JSON.parse(currentComponent.value.events)
    } catch (e) {
      currentComponent.value.events = []
    }
  }

  eventDialogVisible.value = true
}

// 更新组件事件
const updateComponentWithEvents = (updatedComponent) => {
  console.log('Received updated component:', updatedComponent)
  currentComponent.value = updatedComponent
  console.log('Current component after update:', currentComponent.value)
}

// 保存组件事件
const saveComponentEvents = async () => {
  try {
    if (!currentComponent.value) {
      return
    }

    // 将事件数据转换为字符串
    const events = currentComponent.value.events

    // 调用API保存事件
    await componentEventApi.updateComponentEvents(currentComponent.value.id, events)

    // 关闭对话框
    eventDialogVisible.value = false

    // 刷新数据
    refreshData()
  } catch (error) {
    console.error('Failed to save component events:', error)
  }
}

// 清空组件事件
const clearComponentEvents = async (component) => {
  try {
    await ElMessageBox.confirm(
      `确定要清空 "${component.name}" 的所有事件配置吗？此操作不可恢复。`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await componentEventApi.clearComponentEvents(component.id)

    // 刷新数据
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to clear component events:', error)
    }
  }
}

// 批量清空事件
const batchClearEvents = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要清空选中的 ${selectedComponents.value.length} 个组件的所有事件配置吗？此操作不可恢复。`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量清空事件
    const promises = selectedComponents.value.map(component =>
      componentEventApi.clearComponentEvents(component.id)
    )

    await Promise.all(promises)

    // 刷新数据
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to batch clear component events:', error)
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.component-events-view {
  padding: 20px;
}

.page-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.table-actions {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
}

.component-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.component-icon {
  width: 20px;
  height: 20px;
}

.event-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.component-info {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.component-info h3 {
  margin: 0;
  font-size: 16px;
}

.events-editor-container {
  min-height: 400px;
}
</style>
