<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h2>Dashboard</h2>
    </div>

    <div class="dashboard-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="dashboard-card">
            <template #header>
              <div class="card-header">
                <h3>My Projects</h3>
                <el-button type="primary" @click="goToProjects">View All</el-button>
              </div>
            </template>
            <div v-loading="projectsLoading">
              <div v-if="recentProjects.length > 0">
                <div v-for="project in recentProjects" :key="project.id" class="project-item">
                  <div class="project-info">
                    <h4>{{ project.name }}</h4>
                    <p>{{ formatDate(project.updateTime || project.createTime) }}</p>
                  </div>
                  <el-button type="primary" text @click="openProject(project)">Open</el-button>
                </div>
              </div>
              <el-empty v-else description="No projects found" />
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="dashboard-card">
            <template #header>
              <div class="card-header">
                <h3>Recent Activity</h3>
              </div>
            </template>
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in recentActivities"
                :key="index"
                :timestamp="activity.time"
                :type="activity.type"
              >
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="dashboard-card">
            <template #header>
              <div class="card-header">
                <h3>Quick Actions</h3>
              </div>
            </template>
            <div class="quick-actions">
              <el-button type="primary" @click="createProject">Create New Project</el-button>
              <el-button @click="goToProjects">Browse Projects</el-button>
              <el-button type="success" @click="goToComponentLibrary">Manage Component Library</el-button>
              <el-button @click="goToProfile">Edit Profile</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { projectApi } from '../api'

const router = useRouter()

// Data
const recentProjects = ref([])
const projectsLoading = ref(false)
const recentActivities = ref([
  {
    content: 'Created project "E-commerce Homepage"',
    time: '2023-06-01 10:00',
    type: 'primary'
  },
  {
    content: 'Updated page "Product List"',
    time: '2023-06-02 15:30',
    type: 'success'
  },
  {
    content: 'Published project "Company Website"',
    time: '2023-06-03 09:15',
    type: 'warning'
  }
])

// Load recent projects
const loadRecentProjects = async () => {
  projectsLoading.value = true

  try {
    const params = {
      current: 1,
      size: 5
    }

    const result = await projectApi.getProjectList(params)
    recentProjects.value = result.records || []
  } catch (error) {
    console.error('Failed to load recent projects:', error)
  } finally {
    projectsLoading.value = false
  }
}

// Format date
const formatDate = (dateString) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// Navigation
const goToProjects = () => {
  router.push('/project')
}

const openProject = (project) => {
  router.push(`/project/${project.id}`)
}

const createProject = () => {
  router.push('/project')
  // You might want to trigger the create project dialog here
}

const goToProfile = () => {
  // Implement profile page navigation if needed
  console.log('Navigate to profile page')
}

const goToComponentLibrary = () => {
  router.push('/component-library')
}

// Load data on mount
onMounted(() => {
  loadRecentProjects()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-header h2 {
  margin: 0;
  font-size: 24px;
}

.dashboard-content {
  margin-bottom: 20px;
}

.dashboard-card {
  height: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.project-item:last-child {
  border-bottom: none;
}

.project-info h4 {
  margin: 0 0 5px;
  font-size: 16px;
}

.project-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
