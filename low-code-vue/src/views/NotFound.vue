<template>
  <div class="not-found">
    <h1>404</h1>
    <h2>Page Not Found</h2>
    <p>The page you are looking for does not exist or has been moved</p>
    <el-button type="primary" @click="goHome">Return to Home</el-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
}

h1 {
  font-size: 6rem;
  margin-bottom: 0;
  color: #409eff;
}

h2 {
  font-size: 2rem;
  margin-top: 0;
}

p {
  margin-bottom: 2rem;
  color: #666;
}
</style>
