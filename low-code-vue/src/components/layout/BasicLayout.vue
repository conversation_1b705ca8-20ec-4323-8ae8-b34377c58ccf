<template>
  <div class="basic-layout">
    <app-header />
    <div class="main-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import AppHeader from './AppHeader.vue'
</script>

<style scoped>
.basic-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow: auto;
  background-color: #f5f7fa;
}
</style>
