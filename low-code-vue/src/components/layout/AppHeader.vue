<template>
  <div class="app-header">
    <div class="logo">
      <router-link to="/">
        <h1>Low-Code Platform</h1>
      </router-link>
    </div>

    <div class="nav-menu">
      <el-menu mode="horizontal" :router="true" :ellipsis="false" class="menu">
        <el-menu-item index="/dashboard">
          <el-icon><HomeFilled /></el-icon>
          <span>首页</span>
        </el-menu-item>
        <el-menu-item index="/project">
          <el-icon><Folder /></el-icon>
          <span>Projects</span>
        </el-menu-item>
        <el-sub-menu index="components">
          <template #title>
            <el-icon><Menu /></el-icon>
            <span>Components</span>
          </template>
          <el-menu-item index="/component-library">
            <el-icon><Grid /></el-icon>
            <span>Component Library</span>
          </el-menu-item>
          <el-menu-item index="/component-enable">
            <el-icon><Switch /></el-icon>
            <span>Component Enable/Disable</span>
          </el-menu-item>
          <el-menu-item index="/component-events">
            <el-icon><Connection /></el-icon>
            <span>Component Events</span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>

    <div class="user-actions">
      <template v-if="userStore.isLoggedIn">
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="user-dropdown">
            {{ userStore.nickname || userStore.username }}
            <el-icon><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">Profile</el-dropdown-item>
              <el-dropdown-item command="settings">Settings</el-dropdown-item>
              <el-dropdown-item divided command="logout">Logout</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template v-else>
        <el-button type="primary" @click="goToLogin">Login</el-button>
        <el-button @click="goToRegister">Register</el-button>
      </template>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '../../store'
import { ArrowDown, HomeFilled, Folder, Menu, Grid, Switch, Connection } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// Handle dropdown commands
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      // Navigate to profile page
      // router.push('/profile')
      break
    case 'settings':
      // Navigate to settings page
      // router.push('/settings')
      break
    case 'logout':
      // Confirm logout
      ElMessageBox.confirm('Are you sure you want to logout?', 'Confirm', {
        confirmButtonText: 'Logout',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        userStore.logout()
      }).catch(() => {
        // User cancelled logout
      })
      break
  }
}

// Navigation
const goToLogin = () => {
  router.push('/login')
}

const goToRegister = () => {
  router.push('/register')
}
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo {
  display: flex;
  align-items: center;
}

.logo a {
  text-decoration: none;
  color: inherit;
}

.logo h1 {
  margin: 0;
  font-size: 20px;
  color: #409eff;
}

.nav-menu {
  flex: 1;
  margin: 0 20px;
}

.menu {
  border-bottom: none;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f7fa;
}

.user-dropdown .el-icon {
  margin-left: 5px;
}
</style>
