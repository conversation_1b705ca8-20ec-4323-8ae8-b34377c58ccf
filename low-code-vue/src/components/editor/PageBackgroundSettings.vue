<template>
  <div class="page-background-settings">
    <el-form label-position="top">
      <el-form-item label="背景类型">
        <el-radio-group v-model="backgroundType" @change="updateBackground">
          <el-radio label="color">颜色</el-radio>
          <el-radio label="image">图片</el-radio>
          <el-radio label="gradient">渐变</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <!-- 颜色背景 -->
      <template v-if="backgroundType === 'color'">
        <el-form-item label="背景颜色">
          <div class="color-picker-container">
            <el-color-picker v-model="backgroundColor" show-alpha @change="updateBackground" />
            <el-input v-model="backgroundColor" @change="updateBackground" class="color-input" />
          </div>
        </el-form-item>
      </template>
      
      <!-- 图片背景 -->
      <template v-if="backgroundType === 'image'">
        <el-form-item label="背景图片">
          <image-uploader v-model="backgroundImage" @update:modelValue="updateBackground" />
        </el-form-item>
        
        <el-form-item label="图片URL">
          <el-input v-model="backgroundImage" placeholder="https://example.com/image.jpg" @change="updateBackground" />
        </el-form-item>
        
        <el-form-item label="背景尺寸">
          <el-select v-model="backgroundSize" @change="updateBackground" style="width: 100%">
            <el-option label="自动" value="auto" />
            <el-option label="包含 (contain)" value="contain" />
            <el-option label="覆盖 (cover)" value="cover" />
            <el-option label="100%" value="100%" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="backgroundSize === 'custom'" label="自定义尺寸">
          <el-input v-model="customBackgroundSize" placeholder="例如: 100% 100%" @change="updateBackground" />
        </el-form-item>
        
        <el-form-item label="背景位置">
          <el-select v-model="backgroundPosition" @change="updateBackground" style="width: 100%">
            <el-option label="居中" value="center" />
            <el-option label="左上" value="left top" />
            <el-option label="右上" value="right top" />
            <el-option label="左下" value="left bottom" />
            <el-option label="右下" value="right bottom" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="backgroundPosition === 'custom'" label="自定义位置">
          <el-input v-model="customBackgroundPosition" placeholder="例如: 50% 50%" @change="updateBackground" />
        </el-form-item>
        
        <el-form-item label="背景重复">
          <el-select v-model="backgroundRepeat" @change="updateBackground" style="width: 100%">
            <el-option label="不重复" value="no-repeat" />
            <el-option label="重复" value="repeat" />
            <el-option label="水平重复" value="repeat-x" />
            <el-option label="垂直重复" value="repeat-y" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="背景附着">
          <el-select v-model="backgroundAttachment" @change="updateBackground" style="width: 100%">
            <el-option label="滚动" value="scroll" />
            <el-option label="固定" value="fixed" />
            <el-option label="局部" value="local" />
          </el-select>
        </el-form-item>
      </template>
      
      <!-- 渐变背景 -->
      <template v-if="backgroundType === 'gradient'">
        <el-form-item label="渐变类型">
          <el-select v-model="gradientType" @change="updateBackground" style="width: 100%">
            <el-option label="线性渐变" value="linear" />
            <el-option label="径向渐变" value="radial" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="gradientType === 'linear'" label="渐变方向">
          <el-select v-model="gradientDirection" @change="updateBackground" style="width: 100%">
            <el-option label="从上到下" value="to bottom" />
            <el-option label="从左到右" value="to right" />
            <el-option label="从左上到右下" value="to bottom right" />
            <el-option label="从左下到右上" value="to top right" />
            <el-option label="自定义角度" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="gradientType === 'linear' && gradientDirection === 'custom'" label="自定义角度">
          <el-input-number v-model="gradientAngle" :min="0" :max="360" @change="updateBackground" />
          <span class="unit">度</span>
        </el-form-item>
        
        <el-form-item label="渐变颜色">
          <div class="gradient-colors">
            <div v-for="(color, index) in gradientColors" :key="index" class="gradient-color-item">
              <div class="color-picker-container">
                <el-color-picker v-model="color.value" show-alpha @change="updateBackground" />
                <el-input v-model="color.value" @change="updateBackground" class="color-input" />
              </div>
              <el-input-number 
                v-model="color.position" 
                :min="0" 
                :max="100" 
                @change="updateBackground" 
                class="position-input"
              />
              <span class="unit">%</span>
              <el-button 
                type="danger" 
                icon="Delete" 
                circle 
                @click="removeGradientColor(index)" 
                :disabled="gradientColors.length <= 2"
              />
            </div>
            <el-button type="primary" @click="addGradientColor" :disabled="gradientColors.length >= 5">
              添加颜色
            </el-button>
          </div>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import ImageUploader from './ImageUploader.vue'

const props = defineProps({
  pageConfig: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 背景类型
const backgroundType = ref('color')

// 颜色背景
const backgroundColor = ref('rgba(255, 255, 255, 1)')

// 图片背景
const backgroundImage = ref('')
const backgroundSize = ref('cover')
const customBackgroundSize = ref('')
const backgroundPosition = ref('center')
const customBackgroundPosition = ref('')
const backgroundRepeat = ref('no-repeat')
const backgroundAttachment = ref('scroll')

// 渐变背景
const gradientType = ref('linear')
const gradientDirection = ref('to bottom')
const gradientAngle = ref(180)
const gradientColors = ref([
  { value: 'rgba(255, 255, 255, 1)', position: 0 },
  { value: 'rgba(0, 0, 0, 1)', position: 100 }
])

// 初始化背景设置
onMounted(() => {
  if (props.pageConfig && props.pageConfig.styles) {
    const styles = props.pageConfig.styles
    
    // 检测背景类型
    if (styles.backgroundImage) {
      if (styles.backgroundImage.includes('url(')) {
        // 图片背景
        backgroundType.value = 'image'
        backgroundImage.value = styles.backgroundImage.replace(/url\(['"]?(.*?)['"]?\)/g, '$1')
        backgroundSize.value = styles.backgroundSize || 'cover'
        backgroundPosition.value = styles.backgroundPosition || 'center'
        backgroundRepeat.value = styles.backgroundRepeat || 'no-repeat'
        backgroundAttachment.value = styles.backgroundAttachment || 'scroll'
        
        if (!['auto', 'contain', 'cover', '100%'].includes(backgroundSize.value)) {
          customBackgroundSize.value = backgroundSize.value
          backgroundSize.value = 'custom'
        }
        
        if (!['center', 'left top', 'right top', 'left bottom', 'right bottom'].includes(backgroundPosition.value)) {
          customBackgroundPosition.value = backgroundPosition.value
          backgroundPosition.value = 'custom'
        }
      } else if (styles.backgroundImage.includes('gradient')) {
        // 渐变背景
        backgroundType.value = 'gradient'
        
        if (styles.backgroundImage.includes('linear-gradient')) {
          gradientType.value = 'linear'
          
          // 解析渐变方向
          const directionMatch = styles.backgroundImage.match(/linear-gradient\((.*?),/)
          if (directionMatch && directionMatch[1]) {
            const direction = directionMatch[1].trim()
            
            if (direction.includes('deg')) {
              gradientDirection.value = 'custom'
              gradientAngle.value = parseInt(direction)
            } else {
              gradientDirection.value = direction
            }
          }
          
          // 解析渐变颜色
          const colorsMatch = styles.backgroundImage.match(/linear-gradient\(.*?,\s*(.*)\)/)
          if (colorsMatch && colorsMatch[1]) {
            const colors = colorsMatch[1].split(',').map(c => c.trim())
            gradientColors.value = colors.map(color => {
              const parts = color.split(' ')
              if (parts.length > 1) {
                return {
                  value: parts[0],
                  position: parseInt(parts[1])
                }
              } else {
                return {
                  value: color,
                  position: 0
                }
              }
            })
          }
        } else if (styles.backgroundImage.includes('radial-gradient')) {
          gradientType.value = 'radial'
          
          // 解析渐变颜色
          const colorsMatch = styles.backgroundImage.match(/radial-gradient\(.*?,\s*(.*)\)/)
          if (colorsMatch && colorsMatch[1]) {
            const colors = colorsMatch[1].split(',').map(c => c.trim())
            gradientColors.value = colors.map(color => {
              const parts = color.split(' ')
              if (parts.length > 1) {
                return {
                  value: parts[0],
                  position: parseInt(parts[1])
                }
              } else {
                return {
                  value: color,
                  position: 0
                }
              }
            })
          }
        }
      }
    } else if (styles.backgroundColor) {
      // 颜色背景
      backgroundType.value = 'color'
      backgroundColor.value = styles.backgroundColor
    }
  }
})

// 添加渐变颜色
const addGradientColor = () => {
  gradientColors.value.push({
    value: 'rgba(128, 128, 128, 1)',
    position: 50
  })
  updateBackground()
}

// 移除渐变颜色
const removeGradientColor = (index) => {
  if (gradientColors.value.length > 2) {
    gradientColors.value.splice(index, 1)
    updateBackground()
  }
}

// 更新背景
const updateBackground = () => {
  const styles = {}
  
  // 清除所有背景相关样式
  styles.backgroundColor = ''
  styles.backgroundImage = ''
  styles.backgroundSize = ''
  styles.backgroundPosition = ''
  styles.backgroundRepeat = ''
  styles.backgroundAttachment = ''
  
  if (backgroundType.value === 'color') {
    // 颜色背景
    styles.backgroundColor = backgroundColor.value
  } else if (backgroundType.value === 'image') {
    // 图片背景
    if (backgroundImage.value) {
      styles.backgroundImage = `url(${backgroundImage.value})`
      
      if (backgroundSize.value === 'custom') {
        styles.backgroundSize = customBackgroundSize.value
      } else {
        styles.backgroundSize = backgroundSize.value
      }
      
      if (backgroundPosition.value === 'custom') {
        styles.backgroundPosition = customBackgroundPosition.value
      } else {
        styles.backgroundPosition = backgroundPosition.value
      }
      
      styles.backgroundRepeat = backgroundRepeat.value
      styles.backgroundAttachment = backgroundAttachment.value
    }
  } else if (backgroundType.value === 'gradient') {
    // 渐变背景
    if (gradientType.value === 'linear') {
      let direction = gradientDirection.value
      
      if (direction === 'custom') {
        direction = `${gradientAngle.value}deg`
      }
      
      // 排序渐变颜色
      const sortedColors = [...gradientColors.value].sort((a, b) => a.position - b.position)
      
      // 构建渐变字符串
      const gradientString = sortedColors.map(color => {
        return `${color.value} ${color.position}%`
      }).join(', ')
      
      styles.backgroundImage = `linear-gradient(${direction}, ${gradientString})`
    } else if (gradientType.value === 'radial') {
      // 排序渐变颜色
      const sortedColors = [...gradientColors.value].sort((a, b) => a.position - b.position)
      
      // 构建渐变字符串
      const gradientString = sortedColors.map(color => {
        return `${color.value} ${color.position}%`
      }).join(', ')
      
      styles.backgroundImage = `radial-gradient(circle, ${gradientString})`
    }
  }
  
  // 更新页面样式
  emit('update', {
    ...props.pageConfig,
    styles: {
      ...props.pageConfig.styles,
      ...styles
    }
  })
}
</script>

<style scoped>
.page-background-settings {
  padding: 10px 0;
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-input {
  flex: 1;
}

.gradient-colors {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.gradient-color-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.position-input {
  width: 100px;
}

.unit {
  margin: 0 5px;
}
</style>
