<template>
  <div class="events-editor">
    <el-empty v-if="!component" description="请选择一个组件" />
    <template v-else>
      <div class="events-header">
        <h3>事件配置</h3>
        <el-button type="primary" size="small" @click="addEvent">添加事件</el-button>
      </div>

      <el-empty v-if="events.length === 0" description="暂无事件配置">
        <el-button type="primary" @click="addEvent">添加事件</el-button>
      </el-empty>

      <div v-else class="events-list">
        <el-collapse accordion>
          <el-collapse-item v-for="(event, index) in events" :key="index" :name="index">
            <template #title>
              <div class="event-title">
                <span>{{ getEventTypeLabel(event.type) }}</span>
                <el-tag size="small" :type="getActionTagType(event.action.type)">
                  {{ getActionTypeLabel(event.action.type) }}
                </el-tag>
              </div>
            </template>

            <div class="event-content">
              <el-form label-position="top" label-width="100px">
                <el-form-item label="事件类型">
                  <el-select v-model="event.type" placeholder="选择事件类型" @change="updateEvents">
                    <el-option
                      v-for="option in availableEventTypes"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="动作类型">
                  <el-select v-model="event.action.type" placeholder="选择动作类型" @change="updateActionType(index)">
                    <el-option
                      v-for="option in actionTypes"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>

                <!-- 导航动作配置 -->
                <template v-if="event.action.type === 'navigate'">
                  <el-form-item label="导航类型">
                    <el-radio-group v-model="event.action.navigationType" @change="updateEvents">
                      <el-radio label="page">页面</el-radio>
                      <el-radio label="url">外部链接</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item v-if="event.action.navigationType === 'page'" label="选择页面">
                    <el-select v-model="event.action.pageId" placeholder="选择页面" @change="updateEvents">
                      <el-option
                        v-for="page in pages"
                        :key="page.id"
                        :label="page.title || page.name"
                        :value="page.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item v-else-if="event.action.navigationType === 'url'" label="URL">
                    <el-input v-model="event.action.url" placeholder="https://example.com" @change="updateEvents" />
                  </el-form-item>
                </template>

                <!-- 消息动作配置 -->
                <template v-else-if="event.action.type === 'message'">
                  <el-form-item label="消息类型">
                    <el-select v-model="event.action.messageType" placeholder="选择消息类型" @change="updateEvents">
                      <el-option label="成功" value="success" />
                      <el-option label="警告" value="warning" />
                      <el-option label="错误" value="error" />
                      <el-option label="信息" value="info" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="消息内容">
                    <el-input v-model="event.action.message" placeholder="输入消息内容" @change="updateEvents" />
                  </el-form-item>
                </template>

                <!-- 切换组件状态动作配置 -->
                <template v-else-if="event.action.type === 'toggleComponent'">
                  <el-form-item label="目标组件">
                    <el-select v-model="event.action.targetComponentId" placeholder="选择目标组件" @change="updateEvents">
                      <el-option
                        v-for="comp in availableComponents"
                        :key="comp.id"
                        :label="getComponentLabel(comp)"
                        :value="comp.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="操作">
                    <el-select v-model="event.action.operation" placeholder="选择操作" @change="updateEvents">
                      <el-option label="显示" value="show" />
                      <el-option label="隐藏" value="hide" />
                      <el-option label="切换显示/隐藏" value="toggle" />
                    </el-select>
                  </el-form-item>
                </template>

                <!-- 调用API动作配置 -->
                <template v-else-if="event.action.type === 'api'">
                  <el-form-item label="API路径">
                    <el-input v-model="event.action.apiUrl" placeholder="/api/example" @change="updateEvents" />
                  </el-form-item>

                  <el-form-item label="请求方法">
                    <el-select v-model="event.action.apiMethod" placeholder="选择请求方法" @change="updateEvents">
                      <el-option label="GET" value="get" />
                      <el-option label="POST" value="post" />
                      <el-option label="PUT" value="put" />
                      <el-option label="DELETE" value="delete" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="请求参数">
                    <el-input
                      v-model="event.action.apiParams"
                      type="textarea"
                      placeholder='{"key": "value"}'
                      rows="4"
                      @change="updateEvents"
                    />
                  </el-form-item>

                  <el-form-item label="成功后显示消息">
                    <el-switch v-model="event.action.showSuccessMessage" @change="updateEvents" />
                  </el-form-item>
                </template>

                <!-- 设置变量动作配置 -->
                <template v-else-if="event.action.type === 'setVariable'">
                  <el-form-item label="变量名称">
                    <el-input v-model="event.action.variableName" placeholder="variableName" @change="updateEvents" />
                  </el-form-item>

                  <el-form-item label="变量值">
                    <el-input v-model="event.action.variableValue" placeholder="value" @change="updateEvents" />
                  </el-form-item>
                </template>

                <!-- 执行JavaScript动作配置 -->
                <template v-else-if="event.action.type === 'javascript'">
                  <el-form-item label="JavaScript代码">
                    <el-input
                      v-model="event.action.jsCode"
                      type="textarea"
                      placeholder="// 在这里编写JavaScript代码
// 例如: console.log('Hello world');"
                      rows="6"
                      @change="updateEvents"
                    />
                  </el-form-item>
                </template>

                <div class="event-actions">
                  <el-button type="danger" size="small" @click="removeEvent(index)">删除事件</el-button>
                </div>
              </el-form>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useEditorStore } from '../../store'
import { pageApi } from '../../api'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])
const editorStore = useEditorStore()

// 事件列表
const events = ref([])

// 页面列表
const pages = ref([])

// 可用的事件类型
const availableEventTypes = computed(() => {
  const baseTypes = [
    { label: '点击', value: 'click' },
    { label: '双击', value: 'dblclick' },
    { label: '鼠标进入', value: 'mouseenter' },
    { label: '鼠标离开', value: 'mouseleave' },
    { label: '按键按下', value: 'keydown' },
    { label: '按键释放', value: 'keyup' }
  ]

  // 根据组件类型添加特定事件
  if (props.component) {
    switch (props.component.type) {
      case 'input':
      case 'textarea':
        return [
          ...baseTypes,
          { label: '输入变化', value: 'input' },
          { label: '失去焦点', value: 'blur' },
          { label: '获得焦点', value: 'focus' },
          { label: '按下回车', value: 'enter' }
        ]
      case 'select':
      case 'radio':
      case 'checkbox':
      case 'switch':
      case 'slider':
      case 'rate':
      case 'date-picker':
      case 'time-picker':
      case 'color-picker':
        return [
          ...baseTypes,
          { label: '值变化', value: 'change' }
        ]
      case 'button':
        return [
          { label: '点击', value: 'click' },
          { label: '双击', value: 'dblclick' },
          { label: '鼠标进入', value: 'mouseenter' },
          { label: '鼠标离开', value: 'mouseleave' }
        ]
      case 'form':
        return [
          { label: '提交', value: 'submit' },
          { label: '重置', value: 'reset' }
        ]
      case 'image':
      case 'avatar':
        return [
          ...baseTypes,
          { label: '加载完成', value: 'load' },
          { label: '加载失败', value: 'error' }
        ]
      case 'carousel':
        return [
          ...baseTypes,
          { label: '切换', value: 'change' }
        ]
      default:
        return baseTypes
    }
  }

  return baseTypes
})

// 动作类型
const actionTypes = [
  { label: '导航', value: 'navigate' },
  { label: '显示消息', value: 'message' },
  { label: '切换组件状态', value: 'toggleComponent' },
  { label: '调用API', value: 'api' },
  { label: '设置变量', value: 'setVariable' },
  { label: '执行JavaScript', value: 'javascript' }
]

// 可用的组件列表（当前页面上的其他组件）
const availableComponents = computed(() => {
  if (!editorStore.pageConfig || !editorStore.pageConfig.components) {
    return []
  }

  return editorStore.pageConfig.components.filter(comp => comp.id !== props.component.id)
})

// 初始化事件列表
const initEvents = () => {
  if (props.component && props.component.events) {
    events.value = JSON.parse(JSON.stringify(props.component.events))
  } else {
    events.value = []
  }
}

// 加载页面列表
const loadPages = async () => {
  try {
    if (editorStore.projectId) {
      const response = await pageApi.getPageList(editorStore.projectId)
      if (response && response.list && Array.isArray(response.list)) {
        pages.value = response.list
      } else if (response && Array.isArray(response)) {
        // 处理直接返回数组的情况
        pages.value = response
      }
    }
  } catch (error) {
    console.error('Failed to load pages:', error)
    // 设置一个空数组，避免页面出错
    pages.value = []
  }
}

// 添加事件
const addEvent = () => {
  events.value.push({
    type: availableEventTypes.value[0].value,
    action: {
      type: 'navigate',
      navigationType: 'page',
      pageId: '',
      url: '',
      messageType: 'info',
      message: '',
      targetComponentId: '',
      operation: 'show',
      apiUrl: '',
      apiMethod: 'get',
      apiParams: '{}',
      showSuccessMessage: true,
      variableName: '',
      variableValue: '',
      jsCode: '// 在这里编写JavaScript代码'
    }
  })

  updateEvents()
}

// 移除事件
const removeEvent = (index) => {
  events.value.splice(index, 1)
  updateEvents()
}

// 更新动作类型
const updateActionType = (index) => {
  const event = events.value[index]

  // 重置动作配置
  if (event.action.type === 'navigate') {
    event.action.navigationType = 'page'
    event.action.pageId = ''
    event.action.url = ''
  } else if (event.action.type === 'message') {
    event.action.messageType = 'info'
    event.action.message = ''
  } else if (event.action.type === 'toggleComponent') {
    event.action.targetComponentId = ''
    event.action.operation = 'show'
  } else if (event.action.type === 'api') {
    event.action.apiUrl = ''
    event.action.apiMethod = 'get'
    event.action.apiParams = '{}'
    event.action.showSuccessMessage = true
  } else if (event.action.type === 'setVariable') {
    event.action.variableName = ''
    event.action.variableValue = ''
  } else if (event.action.type === 'javascript') {
    event.action.jsCode = '// 在这里编写JavaScript代码'
  }

  updateEvents()
}

// 更新事件列表
const updateEvents = () => {
  const updatedComponent = {
    ...props.component,
    events: events.value
  }

  emit('update', updatedComponent)
}

// 获取事件类型标签
const getEventTypeLabel = (type) => {
  const eventType = availableEventTypes.value.find(et => et.value === type)
  return eventType ? eventType.label : type
}

// 获取动作类型标签
const getActionTypeLabel = (type) => {
  const actionType = actionTypes.find(at => at.value === type)
  return actionType ? actionType.label : type
}

// 获取动作标签类型
const getActionTagType = (type) => {
  switch (type) {
    case 'navigate':
      return 'primary'
    case 'message':
      return 'success'
    case 'toggleComponent':
      return 'warning'
    case 'api':
      return 'danger'
    case 'setVariable':
      return 'info'
    case 'javascript':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取组件标签
const getComponentLabel = (component) => {
  return `${component.type} (${component.id.substring(0, 8)})`
}

// 监听组件变化
watch(() => props.component, () => {
  initEvents()
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  initEvents()
  loadPages()
})
</script>

<style scoped>
.events-editor {
  padding: 10px;
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.events-header h3 {
  margin: 0;
  font-size: 16px;
}

.events-list {
  margin-top: 15px;
}

.event-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.event-content {
  padding: 10px;
}

.event-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
