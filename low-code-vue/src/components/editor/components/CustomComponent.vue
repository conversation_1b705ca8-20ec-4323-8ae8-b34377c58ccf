<template>
  <div class="custom-component" :style="containerStyles" :data-component-id="component.id">
    <div 
      v-for="element in elements" 
      :key="element.id"
      class="custom-element"
      :style="getElementStyle(element)"
    >
      <component 
        v-if="element.tag && ['div', 'span', 'button', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(element.tag)"
        :is="element.tag"
        :style="element.styles"
      >
        {{ element.content }}
      </component>
      
      <input 
        v-else-if="element.tag === 'input'"
        :type="element.inputType || 'text'"
        :placeholder="element.content || ''"
        :style="element.styles"
      />
      
      <textarea 
        v-else-if="element.tag === 'textarea'"
        :placeholder="element.content || ''"
        :style="element.styles"
      ></textarea>
      
      <img 
        v-else-if="element.tag === 'img'"
        :src="element.content || ''"
        :alt="element.alt || ''"
        :style="element.styles"
      />
      
      <div v-else class="element-content" :class="`element-${element.type}`" :style="element.styles">
        {{ element.content }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// 元素列表
const elements = ref([])

// 初始化元素
onMounted(() => {
  if (props.component.elements) {
    elements.value = props.component.elements
  }
})

// 容器样式
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// 获取元素样式
const getElementStyle = (element) => {
  return {
    position: 'absolute',
    left: `${element.x}px`,
    top: `${element.y}px`,
    width: `${element.width}px`,
    height: `${element.height}px`,
    ...element.styles
  }
}
</script>

<style scoped>
.custom-component {
  position: relative;
  width: 100%;
  min-height: 100px;
}

.custom-element {
  position: absolute;
}

.element-rectangle {
  background-color: #e6f7ff;
}

.element-circle {
  border-radius: 50%;
  background-color: #e6f7ff;
}

.element-triangle {
  width: 0;
  height: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-bottom: 100px solid #e6f7ff;
  background-color: transparent !important;
}

.element-text {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.element-button {
  background-color: #409eff;
  color: white;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.element-input {
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
