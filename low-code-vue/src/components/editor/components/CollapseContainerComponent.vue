<template>
  <div
    class="collapse-container-component"
    :style="containerStyles"
    @dragover.prevent
  >
    <el-collapse v-model="activeNames" class="collapse-wrapper">
      <el-collapse-item
        v-for="item in component.props.items"
        :key="item.name"
        :title="item.title"
        :name="item.name"
      >
        <div 
          class="collapse-content"
          @dragover.prevent
          @drop.stop="handleDrop(item.name, $event)"
        >
          <div class="container-children">
            <template v-if="hasChildren(item.name)">
              <div
                v-for="(child, index) in getItemChildren(item.name)"
                :key="child.id"
                class="container-child"
              >
                <div
                  class="component-wrapper"
                  :class="{ 'is-selected': selectedId === child.id }"
                  @click.stop="selectChildComponent(child, item.name, index)"
                >
                  <component-renderer :component="child" @update="updateChild(item.name, index, $event)" />
                  <div class="component-actions" v-if="selectedId === child.id">
                    <el-button size="small" circle icon="Delete" @click.stop="removeChildComponent(item.name, index)"></el-button>
                    <el-button size="small" circle icon="CopyDocument" @click.stop="duplicateChildComponent(item.name, index)"></el-button>
                    <el-button size="small" circle icon="Top" @click.stop="moveChildComponent(item.name, index, index - 1)" :disabled="index === 0"></el-button>
                    <el-button size="small" circle icon="Bottom" @click.stop="moveChildComponent(item.name, index, index + 1)" :disabled="index === getItemChildren(item.name).length - 1"></el-button>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div v-if="!hasChildren(item.name)" class="container-placeholder">
            <el-icon><Plus /></el-icon>
            <span>折叠面板容器（拖拽组件到这里）</span>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { Plus, Delete, CopyDocument, Top, Bottom } from '@element-plus/icons-vue'
import ComponentRenderer from '../ComponentRenderer.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update', 'select'])

// Selected component ID
const selectedId = ref(null)

// Active collapse items
const activeNames = ref(props.component.props.activeNames || [])

// Watch for changes in activeNames
watch(activeNames, (newVal) => {
  // Update active names in component props
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      activeNames: newVal
    }
  }
  
  emit('update', updatedComponent)
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Check if item has children
const hasChildren = (itemName) => {
  const item = props.component.props.items.find(i => i.name === itemName)
  return item && item.children && item.children.length > 0
}

// Get item children
const getItemChildren = (itemName) => {
  const item = props.component.props.items.find(i => i.name === itemName)
  return item ? (item.children || []) : []
}

// Update child component
const updateChild = (itemName, index, updatedChild) => {
  // Create a deep copy of items
  const updatedItems = JSON.parse(JSON.stringify(props.component.props.items))
  
  // Find the item
  const itemIndex = updatedItems.findIndex(i => i.name === itemName)
  if (itemIndex === -1) return
  
  // Ensure children array exists
  if (!updatedItems[itemIndex].children) {
    updatedItems[itemIndex].children = []
  }
  
  // Update the child
  updatedItems[itemIndex].children[index] = updatedChild
  
  // Create a new component object with the updated items
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      items: updatedItems
    }
  }
  
  emit('update', updatedComponent)
}

// Handle component drop
const handleDrop = (itemName, event) => {
  event.preventDefault()
  event.stopPropagation()

  const componentData = event.dataTransfer.getData('application/json')

  if (componentData) {
    try {
      const newComponent = JSON.parse(componentData)
      
      // Create a deep copy of items
      const updatedItems = JSON.parse(JSON.stringify(props.component.props.items))
      
      // Find the item
      const itemIndex = updatedItems.findIndex(i => i.name === itemName)
      if (itemIndex === -1) return
      
      // Ensure children array exists
      if (!updatedItems[itemIndex].children) {
        updatedItems[itemIndex].children = []
      }
      
      // Add component to children
      updatedItems[itemIndex].children.push(newComponent)
      
      // Create a new component object with the updated items
      const updatedComponent = {
        ...props.component,
        props: {
          ...props.component.props,
          items: updatedItems
        }
      }
      
      emit('update', updatedComponent)
    } catch (error) {
      console.error('Failed to parse component data:', error)
    }
  }
}

// Select child component
const selectChildComponent = (child, itemName, index) => {
  selectedId.value = child.id
  
  // Create a deep copy of the child to ensure it's not affected by reference issues
  const childCopy = JSON.parse(JSON.stringify(child))
  
  // Emit the select event with the child component
  emit('select', childCopy)
}

// Remove child component
const removeChildComponent = (itemName, index) => {
  // Create a deep copy of items
  const updatedItems = JSON.parse(JSON.stringify(props.component.props.items))
  
  // Find the item
  const itemIndex = updatedItems.findIndex(i => i.name === itemName)
  if (itemIndex === -1) return
  
  // Ensure children array exists
  if (!updatedItems[itemIndex].children) {
    updatedItems[itemIndex].children = []
  }
  
  // Remove the child
  updatedItems[itemIndex].children.splice(index, 1)
  
  // Create a new component object with the updated items
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      items: updatedItems
    }
  }
  
  emit('update', updatedComponent)
  selectedId.value = null
}

// Duplicate child component
const duplicateChildComponent = (itemName, index) => {
  // Create a deep copy of items
  const updatedItems = JSON.parse(JSON.stringify(props.component.props.items))
  
  // Find the item
  const itemIndex = updatedItems.findIndex(i => i.name === itemName)
  if (itemIndex === -1) return
  
  // Ensure children array exists
  if (!updatedItems[itemIndex].children) {
    updatedItems[itemIndex].children = []
  }
  
  // Get the child to duplicate
  const childToDuplicate = updatedItems[itemIndex].children[index]
  
  // Create a duplicate with a new ID
  const duplicatedChild = JSON.parse(JSON.stringify(childToDuplicate))
  duplicatedChild.id = 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
  
  // Insert the duplicate after the original
  updatedItems[itemIndex].children.splice(index + 1, 0, duplicatedChild)
  
  // Create a new component object with the updated items
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      items: updatedItems
    }
  }
  
  emit('update', updatedComponent)
}

// Move child component
const moveChildComponent = (itemName, fromIndex, toIndex) => {
  // Create a deep copy of items
  const updatedItems = JSON.parse(JSON.stringify(props.component.props.items))
  
  // Find the item
  const itemIndex = updatedItems.findIndex(i => i.name === itemName)
  if (itemIndex === -1) return
  
  // Ensure children array exists
  if (!updatedItems[itemIndex].children) {
    updatedItems[itemIndex].children = []
  }
  
  // Check if the target index is valid
  if (toIndex < 0 || toIndex >= updatedItems[itemIndex].children.length) {
    return
  }
  
  // Move the child
  const [movedChild] = updatedItems[itemIndex].children.splice(fromIndex, 1)
  updatedItems[itemIndex].children.splice(toIndex, 0, movedChild)
  
  // Create a new component object with the updated items
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      items: updatedItems
    }
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.collapse-container-component {
  width: 100%;
  position: relative;
}

.collapse-wrapper {
  width: 100%;
}

.collapse-content {
  position: relative;
  min-height: 100px;
  padding: 10px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.6);
}

.container-children {
  width: 100%;
  min-height: 80px;
}

.container-child {
  margin-bottom: 10px;
  position: relative;
}

.container-child:last-child {
  margin-bottom: 0;
}

.component-wrapper {
  position: relative;
  border: 1px dashed transparent;
  transition: all 0.3s;
}

.component-wrapper:hover {
  border-color: #409eff;
}

.component-wrapper.is-selected {
  border-color: #409eff;
}

.component-actions {
  position: absolute;
  top: -15px;
  right: 0;
  display: flex;
  gap: 5px;
  z-index: 100;
}

.container-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  pointer-events: none;
  font-size: 14px;
}

.container-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
