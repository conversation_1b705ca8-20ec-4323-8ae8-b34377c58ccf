<template>
  <div class="upload-component" :style="containerStyles">
    <el-upload
      :action="component.props.action || '#'"
      :multiple="component.props.multiple"
      :accept="component.props.accept"
      :list-type="component.props.listType"
      :auto-upload="component.props.autoUpload"
      :limit="component.props.limit"
      :file-list="fileList"
      :on-change="handleChange"
      :http-request="handleUpload"
    >
      <el-button type="primary">点击上传</el-button>
      <template #tip>
        <div class="el-upload__tip">
          仅在预览模式下模拟上传，不会真正上传文件
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// File list
const fileList = ref([])

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Handle file change
const handleChange = (file, files) => {
  console.log('File changed:', file)
  fileList.value = files
}

// Handle custom upload
const handleUpload = (options) => {
  const { file, onSuccess } = options
  
  // Simulate upload success after 1 second
  setTimeout(() => {
    ElMessage.success(`模拟上传成功: ${file.name}`)
    onSuccess()
  }, 1000)
  
  // Return a mock upload handler
  return {
    abort() {
      console.log('Upload aborted')
    }
  }
}
</script>

<style scoped>
.upload-component {
  width: 100%;
}
</style>
