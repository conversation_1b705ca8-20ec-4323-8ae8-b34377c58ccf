<template>
  <div class="steps-component" :style="containerStyles">
    <el-steps
      :active="component.props.active"
      :direction="component.props.direction"
      :simple="component.props.simple"
    >
      <el-step 
        v-for="(item, index) in component.props.items" 
        :key="index"
        :title="item.title"
        :description="item.description"
      />
    </el-steps>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.steps-component {
  width: 100%;
}
</style>
