<template>
  <div class="radio-component" :style="containerStyles">
    <el-radio-group v-model="radioValue" :disabled="component.props.disabled">
      <template v-for="option in component.props.options" :key="option.value">
        <el-radio :value="option.value">
          {{ option.label }}
        </el-radio>
      </template>
    </el-radio-group>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

// Radio value
const radioValue = ref('')

// Initialize value when component props change
watch(() => props.component.props.options, (newOptions) => {
  if (newOptions && newOptions.length > 0 && !radioValue.value) {
    radioValue.value = newOptions[0].value
  }
}, { immediate: true })

// Watch for value changes
watch(radioValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.radio-component {
  width: 100%;
}
</style>
