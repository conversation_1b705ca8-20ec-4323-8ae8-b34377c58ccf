<template>
  <div class="select-component" :style="containerStyles">
    <el-select
      v-model="selectValue"
      :placeholder="component.props.placeholder"
      :clearable="component.props.clearable"
      :disabled="component.props.disabled"
      style="width: 100%"
    >
      <el-option
        v-for="option in component.props.options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Select value
const selectValue = ref('')

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.select-component {
  width: 100%;
}
</style>
