<template>
  <div class="flow-process-component" :style="nodeStyles">
    <div class="flow-process-content">
      <div class="flow-process-icon" v-if="component.props.icon">
        <el-icon><component :is="component.props.icon" /></el-icon>
      </div>
      <div class="flow-process-title">{{ component.props.title }}</div>
      <div class="flow-process-description">{{ component.props.description }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles with defaults
const nodeStyles = computed(() => {
  const styles = { ...props.component.styles }
  
  // Set default styles if not provided
  if (!styles.backgroundColor) styles.backgroundColor = '#ffffff'
  if (!styles.borderColor) styles.borderColor = '#409eff'
  if (!styles.borderWidth) styles.borderWidth = '2px'
  if (!styles.borderStyle) styles.borderStyle = 'solid'
  if (!styles.borderRadius) styles.borderRadius = '4px'
  if (!styles.padding) styles.padding = '15px'
  if (!styles.width) styles.width = '180px'
  if (!styles.boxShadow) styles.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
  
  return styles
})
</script>

<style scoped>
.flow-process-component {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.flow-process-component:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2) !important;
}

.flow-process-content {
  width: 100%;
  text-align: center;
}

.flow-process-icon {
  font-size: 24px;
  margin-bottom: 10px;
  color: #409eff;
}

.flow-process-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.flow-process-description {
  font-size: 12px;
  color: #606266;
}
</style>
