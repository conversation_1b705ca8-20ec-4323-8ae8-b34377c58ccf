<template>
  <div class="simple-chart-component" :style="containerStyles" :class="{ 'is-disabled': isDisabled }">
    <div class="chart-title">{{ component.props.title || '图表' }}</div>
    <div class="chart-placeholder">
      <div class="chart-icon">
        <el-icon><component :is="chartIcon" /></el-icon>
      </div>
      <div class="chart-message">
        图表将在预览模式下显示
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { DataLine, TrendCharts, Histogram, PieChart } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Check if component is disabled
const isDisabled = computed(() => {
  return props.component.disabled === true
})

// Get chart icon based on component type
const chartIcon = computed(() => {
  const type = props.component.type
  switch (type) {
    case 'line-chart':
      return 'TrendCharts'
    case 'bar-chart':
      return 'Histogram'
    case 'pie-chart':
      return 'PieChart'
    default:
      return 'DataLine'
  }
})
</script>

<style scoped>
.simple-chart-component {
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #f5f7fa;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
  color: #303133;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.chart-icon {
  font-size: 48px;
  color: #909399;
  margin-bottom: 10px;
}

.chart-message {
  font-size: 14px;
  color: #909399;
}

.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
</style>
