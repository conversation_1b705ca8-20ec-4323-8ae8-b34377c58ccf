<template>
  <div class="input-component" :style="containerStyles" :data-component-id="component.id">
    <el-input
      :placeholder="component.props.placeholder"
      :type="component.props.type"
      :clearable="component.props.clearable"
      :disabled="component.props.disabled"
      :maxlength="component.props.maxlength"
      :show-word-limit="component.props.showWordLimit"
      v-model="inputValue"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Input value
const inputValue = ref('')

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Event handlers
const handleInput = (value) => {
  // This event is emitted when the input value changes
  emitEvent('input', { value })
}

const handleChange = (value) => {
  // This event is emitted when the input loses focus and the value has changed
  emitEvent('change', { value })
}

const handleBlur = (event) => {
  // This event is emitted when the input loses focus
  emitEvent('blur', { value: inputValue.value })
}

const handleFocus = (event) => {
  // This event is emitted when the input gets focus
  emitEvent('focus', { value: inputValue.value })
}

// Helper function to emit events to parent EventHandlerWrapper
const emitEvent = (eventType, data) => {
  // Create and dispatch a custom event that will bubble up to the EventHandlerWrapper
  const customEvent = new CustomEvent(`component:${eventType}`, {
    bubbles: true, // Allow event to bubble up the DOM tree
    detail: data    // Event data
  })

  // Get the current component's root element
  const rootElement = document.querySelector(`[data-component-id="${props.component.id}"]`)
  if (rootElement) {
    // Dispatch the event from the component's root element
    rootElement.dispatchEvent(customEvent)
  } else {
    console.warn(`Could not find element for component ${props.component.id}`)
  }
}
</script>

<style scoped>
.input-component {
  width: 100%;
}
</style>
