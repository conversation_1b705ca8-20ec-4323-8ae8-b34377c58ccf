<template>
  <div class="search-input-component" :style="containerStyles" :data-component-id="component.id">
    <el-input
      v-model="inputValue"
      :placeholder="component.props.placeholder"
      :disabled="component.props.disabled"
      :clearable="component.props.clearable"
      :style="inputStyles"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @clear="handleClear"
    >
      <template #prefix v-if="component.props.showPrefix">
        <el-icon><Search /></el-icon>
      </template>
      <template #append v-if="component.props.showButton">
        <el-button :type="component.props.buttonType" @click="handleSearch">
          {{ component.props.buttonText }}
        </el-button>
      </template>
    </el-input>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Input value
const inputValue = ref('')

// Initialize value from props
onMounted(() => {
  if (props.component.props && props.component.props.defaultValue) {
    inputValue.value = props.component.props.defaultValue
  }
})

// Computed styles for container
const containerStyles = computed(() => {
  const styles = { ...props.component.styles } || {}
  const containerStyles = {}

  // 处理宽度
  if (styles.width) {
    containerStyles.width = styles.width
  }

  // 处理外边距
  if (styles.margin) {
    containerStyles.margin = styles.margin
  }

  return containerStyles
})

// Input styles
const inputStyles = computed(() => {
  const styles = { ...props.component.styles } || {}
  const customStyles = {}

  // 处理宽度
  if (styles.width) {
    // 已经在外层容器中设置了宽度，这里不需要再设置
    delete styles.width
  }

  // 处理高度
  if (styles.height) {
    customStyles.height = styles.height
    delete styles.height
  }

  // 处理外边距
  if (styles.margin) {
    // 已经在外层容器中设置了外边距，这里不需要再设置
    delete styles.margin
  }

  // 处理边框半径
  if (styles.borderRadius) {
    // 设置自定义CSS变量
    customStyles['--el-input-border-radius'] = styles.borderRadius
    delete styles.borderRadius
  }

  // 处理边框颜色
  if (styles.borderColor) {
    customStyles['--el-input-border-color'] = styles.borderColor
    delete styles.borderColor
  }

  // 处理边框宽度
  if (styles.borderWidth) {
    customStyles['--el-input-border-width'] = styles.borderWidth
    delete styles.borderWidth
  }

  // 处理背景色
  if (styles.backgroundColor) {
    customStyles['--el-input-bg-color'] = styles.backgroundColor
    delete styles.backgroundColor
  }

  // 合并所有样式
  return { ...styles, ...customStyles }
})

// Event handlers
const handleInput = (value) => {
  // This event is emitted when the input value changes
  emitEvent('input', { value })
}

const handleChange = (value) => {
  // This event is emitted when the input value changes and the input loses focus
  emitEvent('change', { value })
}

const handleBlur = (event) => {
  // This event is emitted when the input loses focus
  emitEvent('blur', { value: inputValue.value })
}

const handleFocus = (event) => {
  // This event is emitted when the input gets focus
  emitEvent('focus', { value: inputValue.value })
}

const handleClear = () => {
  // This event is emitted when the clear button is clicked
  emitEvent('clear', { value: '' })
}

const handleSearch = () => {
  // This event is emitted when the search button is clicked
  emitEvent('search', { value: inputValue.value })
}

// Helper function to emit events to parent EventHandlerWrapper
const emitEvent = (eventType, data) => {
  // Create and dispatch a custom event that will bubble up to the EventHandlerWrapper
  const customEvent = new CustomEvent(`component:${eventType}`, {
    bubbles: true, // Allow event to bubble up the DOM tree
    detail: data    // Event data
  })

  // Get the current component's root element
  const rootElement = document.querySelector(`[data-component-id="${props.component.id}"]`)
  if (rootElement) {
    // Dispatch the event from the component's root element
    rootElement.dispatchEvent(customEvent)
  } else {
    console.warn(`Could not find element for component ${props.component.id}`)
  }
}
</script>

<style scoped>
.search-input-component {
  width: 100%;
  display: flex;
}

/* 覆盖 Element Plus 的默认样式 */
:deep(.el-input) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  /* 允许自定义边框半径 */
  border-radius: var(--el-input-border-radius, 4px) !important;
  /* 允许自定义边框颜色 */
  border-color: var(--el-input-border-color, var(--el-border-color)) !important;
  /* 允许自定义边框宽度 */
  border-width: var(--el-input-border-width, 1px) !important;
  /* 允许自定义背景色 */
  background-color: var(--el-input-bg-color, var(--el-fill-color-blank)) !important;
}
</style>
