<template>
  <div class="descriptions-component" :style="containerStyles">
    <el-descriptions
      :title="component.props.title"
      :column="component.props.column"
      :border="component.props.border"
      :direction="component.props.direction"
      :size="component.props.size"
    >
      <el-descriptions-item 
        v-for="(item, index) in component.props.items" 
        :key="index"
        :label="item.label"
      >
        {{ item.value }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.descriptions-component {
  width: 100%;
}
</style>
