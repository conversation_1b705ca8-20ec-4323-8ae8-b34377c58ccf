<template>
  <div class="alert-component" :style="containerStyles">
    <el-alert
      :title="component.props.title"
      :description="component.props.description"
      :type="component.props.type"
      :closable="component.props.closable"
      :show-icon="component.props.showIcon"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.alert-component {
  width: 100%;
}
</style>
