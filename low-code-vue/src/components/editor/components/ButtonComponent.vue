<template>
  <div class="button-component" :style="containerStyles">
    <el-button
      :type="component.props.type"
      :style="buttonStyles"
    >
      {{ component.props.text }}
    </el-button>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const containerStyles = computed(() => {
  const { margin, ...rest } = props.component.styles || {}
  return { margin }
})

const buttonStyles = computed(() => {
  const { margin, ...rest } = props.component.styles || {}
  return rest
})
</script>

<style scoped>
.button-component {
  width: 100%;
}
</style>
