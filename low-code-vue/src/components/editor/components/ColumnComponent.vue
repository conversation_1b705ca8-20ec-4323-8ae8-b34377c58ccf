<template>
  <div class="column-component" :style="containerStyles">
    <el-col :span="component.props.span" :offset="component.props.offset">
      <slot></slot>
    </el-col>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.column-component {
  height: 100%;
}
</style>
