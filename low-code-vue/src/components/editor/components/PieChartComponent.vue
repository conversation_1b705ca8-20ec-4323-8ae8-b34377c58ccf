<template>
  <div class="pie-chart-component" :style="containerStyles">
    <div class="chart-title" v-if="component.props.title">{{ component.props.title }}</div>
    <div class="chart-container" ref="chartContainer"></div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const chartContainer = ref(null)
let chart = null

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Initialize chart
const initChart = () => {
  if (!chartContainer.value) return

  // Create chart instance
  chart = echarts.init(chartContainer.value)

  // Set chart options
  const options = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: props.component.props.series[0].data.map(item => item.name)
    },
    series: [
      {
        name: props.component.props.series[0].name,
        type: 'pie',
        radius: '50%',
        center: ['50%', '50%'],
        data: props.component.props.series[0].data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  // Set options
  chart.setOption(options)
}

// Handle window resize
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// Watch for component changes
watch(() => props.component, () => {
  if (chart) {
    chart.dispose()
    initChart()
  }
}, { deep: true })

// Lifecycle hooks
onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.pie-chart-component {
  width: 100%;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.chart-container {
  width: 100%;
  height: 300px;
}
</style>
