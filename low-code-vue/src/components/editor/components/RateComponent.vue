<template>
  <div class="rate-component" :style="containerStyles">
    <el-rate
      v-model="rateValue"
      :max="component.props.max"
      :disabled="component.props.disabled"
      :allow-half="component.props.allowHalf"
      :show-text="component.props.showText"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Rate value
const rateValue = ref(0)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.rate-component {
  width: 100%;
}
</style>
