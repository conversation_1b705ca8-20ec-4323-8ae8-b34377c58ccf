<template>
  <div class="collapse-component" :style="containerStyles">
    <el-collapse v-model="activeNames" :accordion="component.props.accordion">
      <el-collapse-item 
        v-for="(item, index) in component.props.items" 
        :key="index" 
        :title="item.title" 
        :name="index"
      >
        <div v-html="item.content"></div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Active collapse items
const activeNames = ref([0]) // Default open first item

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.collapse-component {
  width: 100%;
  margin-bottom: 15px;
}
</style>
