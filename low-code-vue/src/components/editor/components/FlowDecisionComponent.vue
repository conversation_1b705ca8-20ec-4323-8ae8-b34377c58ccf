<template>
  <div class="flow-decision-component" :style="nodeStyles">
    <div class="flow-decision-content">
      <div class="flow-decision-title">{{ component.props.title }}</div>
      <div class="flow-decision-description">{{ component.props.description }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles with defaults
const nodeStyles = computed(() => {
  const styles = { ...props.component.styles }
  
  // Set default styles if not provided
  if (!styles.backgroundColor) styles.backgroundColor = '#ffffff'
  if (!styles.borderColor) styles.borderColor = '#e6a23c'
  if (!styles.borderWidth) styles.borderWidth = '2px'
  if (!styles.borderStyle) styles.borderStyle = 'solid'
  if (!styles.width) styles.width = '120px'
  if (!styles.height) styles.height = '120px'
  if (!styles.transform) styles.transform = 'rotate(45deg)'
  if (!styles.boxShadow) styles.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
  
  return styles
})
</script>

<style scoped>
.flow-decision-component {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.flow-decision-component:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2) !important;
}

.flow-decision-content {
  transform: rotate(-45deg);
  text-align: center;
  padding: 10px;
}

.flow-decision-title {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 5px;
}

.flow-decision-description {
  font-size: 12px;
  color: #606266;
}
</style>
