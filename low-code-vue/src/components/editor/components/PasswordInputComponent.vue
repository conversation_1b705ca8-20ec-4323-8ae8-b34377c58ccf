<template>
  <div class="password-input-component" :style="containerStyles" :data-component-id="component.id">
    <el-input
      v-model="inputValue"
      :type="showPassword ? 'text' : 'password'"
      :placeholder="component.props.placeholder"
      :disabled="component.props.disabled"
      :clearable="component.props.clearable"
      :show-password="component.props.showPasswordToggle"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    />
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Input value
const inputValue = ref('')
const showPassword = ref(false)

// Initialize value from props
onMounted(() => {
  if (props.component.props && props.component.props.defaultValue) {
    inputValue.value = props.component.props.defaultValue
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Event handlers
const handleInput = (value) => {
  // This event is emitted when the input value changes
  emitEvent('input', { value })
}

const handleChange = (value) => {
  // This event is emitted when the input value changes and the input loses focus
  emitEvent('change', { value })
}

const handleBlur = (event) => {
  // This event is emitted when the input loses focus
  emitEvent('blur', { value: inputValue.value })
}

const handleFocus = (event) => {
  // This event is emitted when the input gets focus
  emitEvent('focus', { value: inputValue.value })
}

// Helper function to emit events to parent EventHandlerWrapper
const emitEvent = (eventType, data) => {
  // Create and dispatch a custom event that will bubble up to the EventHandlerWrapper
  const customEvent = new CustomEvent(`component:${eventType}`, {
    bubbles: true, // Allow event to bubble up the DOM tree
    detail: data    // Event data
  })

  // Get the current component's root element
  const rootElement = document.querySelector(`[data-component-id="${props.component.id}"]`)
  if (rootElement) {
    // Dispatch the event from the component's root element
    rootElement.dispatchEvent(customEvent)
  } else {
    console.warn(`Could not find element for component ${props.component.id}`)
  }
}
</script>

<style scoped>
.password-input-component {
  width: 100%;
}
</style>
