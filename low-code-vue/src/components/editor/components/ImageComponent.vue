<template>
  <div class="image-component" :style="containerStyles">
    <img :src="component.props.src" :alt="component.props.alt" :style="imageStyles" />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  const { width, height, margin, padding, ...rest } = props.component.styles || {}
  return { margin, padding, ...rest }
})

const imageStyles = computed(() => {
  const { width, height } = props.component.styles || {}
  return { width, height }
})
</script>

<style scoped>
.image-component {
  width: 100%;
}

img {
  display: block;
  max-width: 100%;
}
</style>
