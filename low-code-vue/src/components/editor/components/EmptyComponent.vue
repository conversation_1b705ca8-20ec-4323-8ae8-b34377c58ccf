<template>
  <div class="empty-component" :style="containerStyles">
    <el-empty
      :description="component.props.description"
      :image="component.props.image || undefined"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.empty-component {
  width: 100%;
}
</style>
