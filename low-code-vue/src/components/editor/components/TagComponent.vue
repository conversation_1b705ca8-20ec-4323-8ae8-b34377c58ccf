<template>
  <div class="tag-component" :style="containerStyles">
    <el-tag
      :type="component.props.type"
      :effect="component.props.effect"
      :closable="component.props.closable"
      @close="handleClose"
    >
      {{ component.props.content }}
    </el-tag>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Handle close
const handleClose = () => {
  // In a real app, you might want to update the component state
  console.log('Tag closed')
}
</script>

<style scoped>
.tag-component {
  display: inline-block;
}
</style>
