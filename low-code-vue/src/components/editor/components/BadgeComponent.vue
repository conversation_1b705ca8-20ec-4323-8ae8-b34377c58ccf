<template>
  <div class="badge-component" :style="containerStyles">
    <el-badge
      :value="component.props.value"
      :max="component.props.max"
      :is-dot="component.props.isDot"
      :hidden="component.props.hidden"
    >
      <el-button>徽章演示</el-button>
    </el-badge>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.badge-component {
  display: inline-block;
}
</style>
