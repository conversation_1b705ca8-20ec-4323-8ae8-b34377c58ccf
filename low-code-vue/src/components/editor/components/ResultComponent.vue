<template>
  <div class="result-component" :style="containerStyles">
    <el-result
      :title="component.props.title"
      :sub-title="component.props.subTitle"
      :icon="component.props.icon"
      :status="component.props.status"
    >
      <template #extra>
        <el-button type="primary">返回</el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.result-component {
  width: 100%;
}
</style>
