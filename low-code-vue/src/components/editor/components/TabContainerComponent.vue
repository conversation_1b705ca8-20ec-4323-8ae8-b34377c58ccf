<template>
  <div
    class="tab-container-component"
    :style="containerStyles"
    @dragover.prevent
  >
    <el-tabs v-model="activeTab" class="tab-wrapper" @tab-click="handleTabClick">
      <el-tab-pane
        v-for="tab in component.props.tabs"
        :key="tab.name"
        :label="tab.title"
        :name="tab.name"
      >
        <div 
          class="tab-content"
          @dragover.prevent
          @drop.stop="handleDrop(tab.name, $event)"
        >
          <div class="container-children">
            <template v-if="hasChildren(tab.name)">
              <div
                v-for="(child, index) in getTabChildren(tab.name)"
                :key="child.id"
                class="container-child"
              >
                <div
                  class="component-wrapper"
                  :class="{ 'is-selected': selectedId === child.id }"
                  @click.stop="selectChildComponent(child, tab.name, index)"
                >
                  <component-renderer :component="child" @update="updateChild(tab.name, index, $event)" />
                  <div class="component-actions" v-if="selectedId === child.id">
                    <el-button size="small" circle icon="Delete" @click.stop="removeChildComponent(tab.name, index)"></el-button>
                    <el-button size="small" circle icon="CopyDocument" @click.stop="duplicateChildComponent(tab.name, index)"></el-button>
                    <el-button size="small" circle icon="Top" @click.stop="moveChildComponent(tab.name, index, index - 1)" :disabled="index === 0"></el-button>
                    <el-button size="small" circle icon="Bottom" @click.stop="moveChildComponent(tab.name, index, index + 1)" :disabled="index === getTabChildren(tab.name).length - 1"></el-button>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div v-if="!hasChildren(tab.name)" class="container-placeholder">
            <el-icon><Plus /></el-icon>
            <span>标签页容器（拖拽组件到这里）</span>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Plus, Delete, CopyDocument, Top, Bottom } from '@element-plus/icons-vue'
import ComponentRenderer from '../ComponentRenderer.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update', 'select'])

// Selected component ID
const selectedId = ref(null)

// Active tab
const activeTab = ref(props.component.props.activeName || (props.component.props.tabs[0] && props.component.props.tabs[0].name))

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Check if tab has children
const hasChildren = (tabName) => {
  const tab = props.component.props.tabs.find(t => t.name === tabName)
  return tab && tab.children && tab.children.length > 0
}

// Get tab children
const getTabChildren = (tabName) => {
  const tab = props.component.props.tabs.find(t => t.name === tabName)
  return tab ? (tab.children || []) : []
}

// Handle tab click
const handleTabClick = (tab) => {
  // Update active tab in component props
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      activeName: tab.props.name
    }
  }
  
  emit('update', updatedComponent)
}

// Update child component
const updateChild = (tabName, index, updatedChild) => {
  // Create a deep copy of tabs
  const updatedTabs = JSON.parse(JSON.stringify(props.component.props.tabs))
  
  // Find the tab
  const tabIndex = updatedTabs.findIndex(t => t.name === tabName)
  if (tabIndex === -1) return
  
  // Ensure children array exists
  if (!updatedTabs[tabIndex].children) {
    updatedTabs[tabIndex].children = []
  }
  
  // Update the child
  updatedTabs[tabIndex].children[index] = updatedChild
  
  // Create a new component object with the updated tabs
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      tabs: updatedTabs
    }
  }
  
  emit('update', updatedComponent)
}

// Handle component drop
const handleDrop = (tabName, event) => {
  event.preventDefault()
  event.stopPropagation()

  const componentData = event.dataTransfer.getData('application/json')

  if (componentData) {
    try {
      const newComponent = JSON.parse(componentData)
      
      // Create a deep copy of tabs
      const updatedTabs = JSON.parse(JSON.stringify(props.component.props.tabs))
      
      // Find the tab
      const tabIndex = updatedTabs.findIndex(t => t.name === tabName)
      if (tabIndex === -1) return
      
      // Ensure children array exists
      if (!updatedTabs[tabIndex].children) {
        updatedTabs[tabIndex].children = []
      }
      
      // Add component to children
      updatedTabs[tabIndex].children.push(newComponent)
      
      // Create a new component object with the updated tabs
      const updatedComponent = {
        ...props.component,
        props: {
          ...props.component.props,
          tabs: updatedTabs
        }
      }
      
      emit('update', updatedComponent)
    } catch (error) {
      console.error('Failed to parse component data:', error)
    }
  }
}

// Select child component
const selectChildComponent = (child, tabName, index) => {
  selectedId.value = child.id
  
  // Create a deep copy of the child to ensure it's not affected by reference issues
  const childCopy = JSON.parse(JSON.stringify(child))
  
  // Emit the select event with the child component
  emit('select', childCopy)
}

// Remove child component
const removeChildComponent = (tabName, index) => {
  // Create a deep copy of tabs
  const updatedTabs = JSON.parse(JSON.stringify(props.component.props.tabs))
  
  // Find the tab
  const tabIndex = updatedTabs.findIndex(t => t.name === tabName)
  if (tabIndex === -1) return
  
  // Ensure children array exists
  if (!updatedTabs[tabIndex].children) {
    updatedTabs[tabIndex].children = []
  }
  
  // Remove the child
  updatedTabs[tabIndex].children.splice(index, 1)
  
  // Create a new component object with the updated tabs
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      tabs: updatedTabs
    }
  }
  
  emit('update', updatedComponent)
  selectedId.value = null
}

// Duplicate child component
const duplicateChildComponent = (tabName, index) => {
  // Create a deep copy of tabs
  const updatedTabs = JSON.parse(JSON.stringify(props.component.props.tabs))
  
  // Find the tab
  const tabIndex = updatedTabs.findIndex(t => t.name === tabName)
  if (tabIndex === -1) return
  
  // Ensure children array exists
  if (!updatedTabs[tabIndex].children) {
    updatedTabs[tabIndex].children = []
  }
  
  // Get the child to duplicate
  const childToDuplicate = updatedTabs[tabIndex].children[index]
  
  // Create a duplicate with a new ID
  const duplicatedChild = JSON.parse(JSON.stringify(childToDuplicate))
  duplicatedChild.id = 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
  
  // Insert the duplicate after the original
  updatedTabs[tabIndex].children.splice(index + 1, 0, duplicatedChild)
  
  // Create a new component object with the updated tabs
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      tabs: updatedTabs
    }
  }
  
  emit('update', updatedComponent)
}

// Move child component
const moveChildComponent = (tabName, fromIndex, toIndex) => {
  // Create a deep copy of tabs
  const updatedTabs = JSON.parse(JSON.stringify(props.component.props.tabs))
  
  // Find the tab
  const tabIndex = updatedTabs.findIndex(t => t.name === tabName)
  if (tabIndex === -1) return
  
  // Ensure children array exists
  if (!updatedTabs[tabIndex].children) {
    updatedTabs[tabIndex].children = []
  }
  
  // Check if the target index is valid
  if (toIndex < 0 || toIndex >= updatedTabs[tabIndex].children.length) {
    return
  }
  
  // Move the child
  const [movedChild] = updatedTabs[tabIndex].children.splice(fromIndex, 1)
  updatedTabs[tabIndex].children.splice(toIndex, 0, movedChild)
  
  // Create a new component object with the updated tabs
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      tabs: updatedTabs
    }
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.tab-container-component {
  width: 100%;
  position: relative;
}

.tab-wrapper {
  width: 100%;
}

.tab-content {
  position: relative;
  min-height: 100px;
  padding: 10px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.6);
}

.container-children {
  width: 100%;
  min-height: 80px;
}

.container-child {
  margin-bottom: 10px;
  position: relative;
}

.container-child:last-child {
  margin-bottom: 0;
}

.component-wrapper {
  position: relative;
  border: 1px dashed transparent;
  transition: all 0.3s;
}

.component-wrapper:hover {
  border-color: #409eff;
}

.component-wrapper.is-selected {
  border-color: #409eff;
}

.component-actions {
  position: absolute;
  top: -15px;
  right: 0;
  display: flex;
  gap: 5px;
  z-index: 100;
}

.container-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  pointer-events: none;
  font-size: 14px;
}

.container-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
