<template>
  <div class="divider-component" :style="containerStyles">
    <el-divider 
      :direction="component.props.direction" 
      :content-position="component.props.contentPosition"
    >
      {{ component.props.content }}
    </el-divider>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.divider-component {
  width: 100%;
}
</style>
