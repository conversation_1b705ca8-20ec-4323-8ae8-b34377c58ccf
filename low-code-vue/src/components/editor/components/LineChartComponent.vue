<template>
  <div class="line-chart-component" :style="containerStyles">
    <div class="chart-title" v-if="component.props.title">{{ component.props.title }}</div>
    <div class="chart-container" ref="chartContainer"></div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const chartContainer = ref(null)
let chart = null

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Initialize chart
const initChart = () => {
  if (!chartContainer.value) return

  // Create chart instance
  chart = echarts.init(chartContainer.value)

  // Set chart options
  const options = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: props.component.props.series.map(item => item.name)
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.component.props.xAxis
    },
    yAxis: {
      type: 'value'
    },
    series: props.component.props.series.map(item => ({
      name: item.name,
      type: 'line',
      data: item.data
    }))
  }

  // Set options
  chart.setOption(options)
}

// Handle window resize
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// Watch for component changes
watch(() => props.component, () => {
  if (chart) {
    chart.dispose()
    initChart()
  }
}, { deep: true })

// Lifecycle hooks
onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.line-chart-component {
  width: 100%;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.chart-container {
  width: 100%;
  height: 300px;
}
</style>
