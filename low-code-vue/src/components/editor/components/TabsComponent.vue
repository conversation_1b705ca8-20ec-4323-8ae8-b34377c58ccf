<template>
  <div class="tabs-component" :style="containerStyles">
    <el-tabs :type="component.props.type" v-model="activeTab">
      <el-tab-pane 
        v-for="tab in component.props.tabs" 
        :key="tab.name" 
        :label="tab.label" 
        :name="tab.name"
      >
        {{ tab.content }}
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Active tab
const activeTab = ref(props.component.props.tabs[0]?.name || '')

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.tabs-component {
  width: 100%;
}
</style>
