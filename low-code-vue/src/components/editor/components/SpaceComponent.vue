<template>
  <div class="space-component" :style="containerStyles">
    <div class="space-header">
      <div class="space-title">间距组件</div>
      <div class="space-actions">
        <el-button size="small" type="primary" @click="addItem">添加子项</el-button>
        <el-select v-model="currentItemType" size="small" @change="changeItemType" style="width: 100px; margin-left: 10px;">
          <el-option label="卡片" value="card" />
          <el-option label="按钮" value="button" />
          <el-option label="文本" value="text" />
        </el-select>
      </div>
    </div>

    <el-space
      :direction="component.props.direction"
      :size="component.props.size"
      :wrap="component.props.wrap"
      :alignment="component.props.alignment"
      class="space-container"
    >
      <template v-for="(item, index) in items" :key="item.id">
        <!-- 卡片类型 -->
        <div v-if="item.type === 'card'" class="space-item-wrapper">
          <el-card
            class="space-item space-card"
            shadow="hover"
            @click.stop="handleItemEvent(item, 'click')"
          >
            <div>{{ item.content || `Item ${index + 1}` }}</div>
          </el-card>
          <div class="item-actions">
            <el-button size="small" circle icon="Edit" @click="editItem(index)"></el-button>
            <el-button size="small" circle icon="Delete" @click="removeItem(index)"></el-button>
            <el-button size="small" circle icon="Setting" @click="editItemEvents(index)"></el-button>
          </div>
        </div>

        <!-- 按钮类型 -->
        <div v-else-if="item.type === 'button'" class="space-item-wrapper">
          <el-button
            :type="item.buttonType || getButtonType(index + 1)"
            class="space-item"
            @click.stop="handleItemEvent(item, 'click')"
          >
            {{ item.content || `Button ${index + 1}` }}
          </el-button>
          <div class="item-actions">
            <el-button size="small" circle icon="Edit" @click="editItem(index)"></el-button>
            <el-button size="small" circle icon="Delete" @click="removeItem(index)"></el-button>
            <el-button size="small" circle icon="Setting" @click="editItemEvents(index)"></el-button>
          </div>
        </div>

        <!-- 文本类型 -->
        <div v-else-if="item.type === 'text'" class="space-item-wrapper">
          <div
            class="space-item space-text"
            @click.stop="handleItemEvent(item, 'click')"
          >
            {{ item.content || `Text ${index + 1}` }}
          </div>
          <div class="item-actions">
            <el-button size="small" circle icon="Edit" @click="editItem(index)"></el-button>
            <el-button size="small" circle icon="Delete" @click="removeItem(index)"></el-button>
            <el-button size="small" circle icon="Setting" @click="editItemEvents(index)"></el-button>
          </div>
        </div>

        <!-- 默认类型 -->
        <div v-else class="space-item-wrapper">
          <div
            class="space-item space-default"
            @click.stop="handleItemEvent(item, 'click')"
          >
            {{ item.content || `Item ${index + 1}` }}
          </div>
          <div class="item-actions">
            <el-button size="small" circle icon="Edit" @click="editItem(index)"></el-button>
            <el-button size="small" circle icon="Delete" @click="removeItem(index)"></el-button>
            <el-button size="small" circle icon="Setting" @click="editItemEvents(index)"></el-button>
          </div>
        </div>
      </template>
    </el-space>

    <!-- 编辑子项对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑子项" width="400px">
      <el-form label-position="top">
        <el-form-item label="内容">
          <el-input v-model="editingItem.content" placeholder="请输入内容"></el-input>
        </el-form-item>

        <el-form-item v-if="editingItem.type === 'button'" label="按钮类型">
          <el-select v-model="editingItem.buttonType">
            <el-option label="默认" value="default" />
            <el-option label="主要" value="primary" />
            <el-option label="成功" value="success" />
            <el-option label="警告" value="warning" />
            <el-option label="危险" value="danger" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveItem">保存</el-button>
      </template>
    </el-dialog>

    <!-- 编辑子项事件对话框 -->
    <el-dialog v-model="eventDialogVisible" title="编辑子项事件" width="500px">
      <el-form label-position="top">
        <el-form-item label="点击事件">
          <el-select v-model="editingEvent.type" style="width: 100%">
            <el-option label="无操作" value="none" />
            <el-option label="显示消息" value="message" />
            <el-option label="打开链接" value="link" />
            <el-option label="页面导航" value="navigate" />
            <el-option label="执行代码" value="code" />
          </el-select>
        </el-form-item>

        <template v-if="editingEvent.type === 'message'">
          <el-form-item label="消息内容">
            <el-input v-model="editingEvent.message" placeholder="请输入消息内容"></el-input>
          </el-form-item>

          <el-form-item label="消息类型">
            <el-select v-model="editingEvent.messageType">
              <el-option label="成功" value="success" />
              <el-option label="警告" value="warning" />
              <el-option label="错误" value="error" />
              <el-option label="信息" value="info" />
            </el-select>
          </el-form-item>
        </template>

        <template v-if="editingEvent.type === 'link'">
          <el-form-item label="链接地址">
            <el-input v-model="editingEvent.url" placeholder="请输入链接地址"></el-input>
          </el-form-item>

          <el-form-item label="打开方式">
            <el-select v-model="editingEvent.target">
              <el-option label="当前窗口" value="_self" />
              <el-option label="新窗口" value="_blank" />
            </el-select>
          </el-form-item>
        </template>

        <template v-if="editingEvent.type === 'navigate'">
          <el-form-item label="页面类型">
            <el-select v-model="editingEvent.pageType">
              <el-option label="项目页面" value="project" />
              <el-option label="外部页面" value="external" />
            </el-select>
          </el-form-item>

          <template v-if="editingEvent.pageType === 'project'">
            <el-form-item label="项目">
              <el-select v-model="editingEvent.projectId" @change="loadProjectPages" filterable placeholder="请选择项目">
                <el-option
                  v-for="project in projects"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="页面">
              <el-select v-model="editingEvent.pageId" filterable placeholder="请选择页面">
                <el-option
                  v-for="page in projectPages"
                  :key="page.id"
                  :label="page.name"
                  :value="page.id"
                />
              </el-select>
            </el-form-item>
          </template>

          <template v-else>
            <el-form-item label="页面路径">
              <el-input v-model="editingEvent.pagePath" placeholder="请输入页面路径"></el-input>
            </el-form-item>
          </template>
        </template>

        <template v-if="editingEvent.type === 'code'">
          <el-form-item label="代码">
            <el-input
              v-model="editingEvent.code"
              type="textarea"
              :rows="5"
              placeholder="请输入JavaScript代码"
            ></el-input>
          </el-form-item>
        </template>
      </el-form>

      <template #footer>
        <el-button @click="eventDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveItemEvent">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Edit, Delete, Setting } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { projectApi, pageApi } from '../../../api'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 路由器实例
const router = useRouter()

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// 子项列表
const items = ref([])

// 当前选择的子项类型
const currentItemType = ref('card')

// 编辑对话框
const editDialogVisible = ref(false)
const editingItem = ref({})
const editingIndex = ref(-1)

// 事件编辑对话框
const eventDialogVisible = ref(false)
const editingEvent = ref({
  type: 'none',
  message: '',
  messageType: 'info',
  url: '',
  target: '_blank',
  code: '',
  pageType: 'project',
  projectId: null,
  pageId: null,
  pagePath: ''
})
const editingEventItemIndex = ref(-1)

// 项目和页面数据
const projects = ref([])
const projectPages = ref([])

// 初始化子项
onMounted(() => {
  initItems()
})

// 监听组件属性变化
watch(() => props.component.props, (newProps) => {
  if (newProps && newProps.itemType) {
    currentItemType.value = newProps.itemType
  }
}, { deep: true })

// 初始化子项
const initItems = () => {
  // 如果组件已经有子项，使用现有子项
  if (props.component.props.items && props.component.props.items.length > 0) {
    items.value = JSON.parse(JSON.stringify(props.component.props.items))
    return
  }

  // 否则，根据itemCount和itemType创建新的子项
  const itemCount = props.component.props.itemCount || 3
  const itemType = props.component.props.itemType || 'card'

  items.value = []
  for (let i = 0; i < itemCount; i++) {
    items.value.push({
      id: `item_${Date.now()}_${i}`,
      type: itemType,
      content: `${itemType === 'card' ? 'Item' : itemType === 'button' ? 'Button' : 'Text'} ${i + 1}`,
      buttonType: itemType === 'button' ? getButtonType(i + 1) : undefined
    })
  }

  // 更新组件属性
  updateComponentProps()
}

// 获取按钮类型
const getButtonType = (index) => {
  const types = ['primary', 'success', 'warning', 'danger', 'info']
  return types[(index - 1) % types.length]
}

// 添加子项
const addItem = () => {
  const newItem = {
    id: `item_${Date.now()}_${items.value.length}`,
    type: currentItemType.value,
    content: `${currentItemType.value === 'card' ? 'Item' : currentItemType.value === 'button' ? 'Button' : 'Text'} ${items.value.length + 1}`,
    buttonType: currentItemType.value === 'button' ? getButtonType(items.value.length + 1) : undefined
  }

  items.value.push(newItem)

  // 更新组件属性
  updateComponentProps()
}

// 移除子项
const removeItem = (index) => {
  items.value.splice(index, 1)

  // 更新组件属性
  updateComponentProps()
}

// 编辑子项
const editItem = (index) => {
  editingIndex.value = index
  editingItem.value = JSON.parse(JSON.stringify(items.value[index]))
  editDialogVisible.value = true
}

// 保存子项
const saveItem = () => {
  if (editingIndex.value >= 0 && editingIndex.value < items.value.length) {
    items.value[editingIndex.value] = editingItem.value

    // 更新组件属性
    updateComponentProps()
  }

  editDialogVisible.value = false
}

// 获取所有项目
const loadProjects = async () => {
  try {
    const response = await projectApi.getProjects()
    if (response && response.code === 200) {
      projects.value = response.data
    }
  } catch (error) {
    console.error('Failed to load projects:', error)
    ElMessage.error('加载项目失败')
  }
}

// 根据项目ID获取页面
const loadProjectPages = async (projectId) => {
  if (!projectId) {
    projectPages.value = []
    return
  }

  try {
    const response = await pageApi.getPagesByProjectId(projectId)
    if (response && response.code === 200) {
      projectPages.value = response.data
    }
  } catch (error) {
    console.error('Failed to load project pages:', error)
    ElMessage.error('加载项目页面失败')
    projectPages.value = []
  }
}

// 编辑子项事件
const editItemEvents = async (index) => {
  editingEventItemIndex.value = index

  // 加载项目数据
  await loadProjects()

  // 如果子项已经有事件定义，加载它
  if (items.value[index].events && items.value[index].events.click) {
    editingEvent.value = JSON.parse(JSON.stringify(items.value[index].events.click))

    // 如果是页面导航事件，加载相关页面
    if (editingEvent.value.type === 'navigate' && editingEvent.value.pageType === 'project' && editingEvent.value.projectId) {
      await loadProjectPages(editingEvent.value.projectId)
    }
  } else {
    // 否则初始化为默认值
    editingEvent.value = {
      type: 'none',
      message: '',
      messageType: 'info',
      url: '',
      target: '_blank',
      code: '',
      pageType: 'project',
      projectId: null,
      pageId: null,
      pagePath: ''
    }
  }

  eventDialogVisible.value = true
}

// 保存子项事件
const saveItemEvent = () => {
  console.log('Saving item event:', editingEvent.value)

  if (editingEventItemIndex.value >= 0 && editingEventItemIndex.value < items.value.length) {
    // 确保子项有events属性
    if (!items.value[editingEventItemIndex.value].events) {
      items.value[editingEventItemIndex.value].events = {}
    }

    // 保存点击事件
    items.value[editingEventItemIndex.value].events.click = JSON.parse(JSON.stringify(editingEvent.value))

    // 如果是导航事件，确保数据类型正确
    if (editingEvent.value.type === 'navigate') {
      if (editingEvent.value.projectId) {
        // 确保 projectId 是数字
        items.value[editingEventItemIndex.value].events.click.projectId = Number(editingEvent.value.projectId)
      }

      if (editingEvent.value.pageId) {
        // 确保 pageId 是数字
        items.value[editingEventItemIndex.value].events.click.pageId = Number(editingEvent.value.pageId)
      }
    }

    // 更新组件属性
    updateComponentProps()

    ElMessage.success('事件配置已保存')
  }

  eventDialogVisible.value = false
}

// 处理子项事件
const handleItemEvent = (item, eventName) => {
  console.log(`Handling item event: ${eventName} for item:`, item)

  // 检查子项是否有事件定义
  if (item.events && item.events[eventName]) {
    console.log(`Found event definition for ${eventName}:`, item.events[eventName])
    const event = item.events[eventName]

    // 根据事件类型执行不同的操作
    switch (event.type) {
      case 'message':
        // 显示消息
        ElMessage({
          message: event.message,
          type: event.messageType
        })
        break

      case 'link':
        // 打开链接
        window.open(event.url, event.target)
        break

      case 'navigate':
        // 页面导航
        if (event.pageType === 'project' && event.projectId && event.pageId) {
          // 导航到项目页面
          console.log(`Navigating to project page: projectId=${event.projectId}, pageId=${event.pageId}`)
          try {
            router.push({
              name: 'Preview',
              params: { projectId: event.projectId, pageId: event.pageId }
            })
            console.log('Navigation successful')
          } catch (error) {
            console.error('Navigation error:', error)
            ElMessage.error('导航失败: ' + error.message)
          }
        } else if (event.pageType === 'external' && event.pagePath) {
          // 导航到外部页面
          console.log(`Navigating to external page: ${event.pagePath}`)
          try {
            router.push(event.pagePath)
            console.log('Navigation successful')
          } catch (error) {
            console.error('Navigation error:', error)
            ElMessage.error('导航失败: ' + error.message)
          }
        } else {
          console.warn('Incomplete navigation configuration:', event)
          ElMessage.warning('导航配置不完整')
        }
        break

      case 'code':
        // 执行代码
        try {
          // 使用Function构造函数执行代码
          const execFunction = new Function(event.code)
          execFunction()
        } catch (error) {
          console.error('Error executing code:', error)
          ElMessage.error('执行代码时出错: ' + error.message)
        }
        break
    }
  }
}

// 更改子项类型
const changeItemType = (type) => {
  // 更新组件属性
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      itemType: type
    }
  }

  emit('update', updatedComponent)
}

// 更新组件属性
const updateComponentProps = () => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      itemCount: items.value.length,
      items: items.value
    }
  }

  emit('update', updatedComponent)
}
</script>

<style scoped>
.space-component {
  width: 100%;
  padding: 20px;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.space-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.space-title {
  font-size: 16px;
  font-weight: bold;
}

.space-actions {
  display: flex;
  align-items: center;
}

.space-container {
  min-height: 100px;
  padding: 10px;
  border: 1px dashed #ebeef5;
  border-radius: 4px;
}

.space-item-wrapper {
  position: relative;
}

.space-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.space-card {
  width: 100px;
  height: 60px;
}

.space-text {
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f5f7fa;
  min-width: 80px;
  text-align: center;
}

.space-default {
  width: 80px;
  height: 40px;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.item-actions {
  position: absolute;
  top: -10px;
  right: -10px;
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.space-item-wrapper:hover .item-actions {
  opacity: 1;
}
</style>
