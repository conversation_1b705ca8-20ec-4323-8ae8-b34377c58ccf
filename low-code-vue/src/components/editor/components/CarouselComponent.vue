<template>
  <div class="carousel-component" :style="containerStyles">
    <el-carousel
      :height="carouselHeight"
      :autoplay="carouselAutoplay"
      :interval="carouselInterval"
    >
      <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
        <img :src="item.url" class="carousel-image" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup>
import { computed, watch, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Computed properties for carousel
const carouselHeight = computed(() => {
  return `${props.component.props.height || 200}px`
})

const carouselAutoplay = computed(() => {
  return props.component.props.autoplay !== false
})

const carouselInterval = computed(() => {
  return props.component.props.interval || 3000
})

// Use ref instead of computed for items to ensure we can force updates
const carouselItems = ref([])

// Initialize items
carouselItems.value = props.component.props.items || []

// Watch for changes in component props
watch(() => props.component.props.items, (newItems) => {
  console.log('CarouselComponent detected items change:', JSON.stringify(newItems))
  if (newItems) {
    // Create a new array to ensure reactivity
    carouselItems.value = [...newItems]
  }
}, { deep: true })
</script>

<style scoped>
.carousel-component {
  width: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
