<template>
  <div class="flow-start-component" :style="nodeStyles">
    <div class="flow-start-content">
      <div class="flow-start-icon">
        <el-icon><VideoPlay /></el-icon>
      </div>
      <div class="flow-start-title">{{ component.props.title }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { VideoPlay } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles with defaults
const nodeStyles = computed(() => {
  const styles = { ...props.component.styles }
  
  // Set default styles if not provided
  if (!styles.backgroundColor) styles.backgroundColor = '#67c23a'
  if (!styles.color) styles.color = '#ffffff'
  if (!styles.borderRadius) styles.borderRadius = '50%'
  if (!styles.width) styles.width = '60px'
  if (!styles.height) styles.height = '60px'
  if (!styles.boxShadow) styles.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
  
  return styles
})
</script>

<style scoped>
.flow-start-component {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.flow-start-component:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2) !important;
}

.flow-start-content {
  text-align: center;
}

.flow-start-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.flow-start-title {
  font-size: 12px;
  font-weight: bold;
}
</style>
