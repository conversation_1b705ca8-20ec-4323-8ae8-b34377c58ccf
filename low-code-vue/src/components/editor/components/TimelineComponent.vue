<template>
  <div class="timeline-component" :style="containerStyles">
    <el-timeline>
      <el-timeline-item
        v-for="(item, index) in component.props.items"
        :key="index"
        :timestamp="item.timestamp"
        :type="item.type"
      >
        {{ item.content }}
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.timeline-component {
  width: 100%;
}
</style>
