<template>
  <div class="link-component" :style="containerStyles">
    <div v-if="isEditing" class="link-editor">
      <el-form label-position="top" size="small">
        <el-form-item label="链接文本">
          <el-input v-model="editingText" />
        </el-form-item>
        <el-form-item label="链接地址">
          <el-input v-model="editingHref" />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="editingTarget">在新窗口打开</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="editingUnderline">显示下划线</el-checkbox>
        </el-form-item>
        <el-button type="primary" size="small" @click="saveLink">保存</el-button>
        <el-button size="small" @click="cancelEdit">取消</el-button>
      </el-form>
    </div>
    <el-link 
      v-else
      :href="component.props.href" 
      :target="component.props.target" 
      :underline="component.props.underline"
      @click.prevent="handleClick"
      @dblclick.stop="startEditing"
    >
      {{ component.props.text }}
    </el-link>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Editing state
const isEditing = ref(false)
const editingText = ref('')
const editingHref = ref('')
const editingTarget = ref(false)
const editingUnderline = ref(true)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Start editing
const startEditing = () => {
  editingText.value = props.component.props.text
  editingHref.value = props.component.props.href
  editingTarget.value = props.component.props.target === '_blank'
  editingUnderline.value = props.component.props.underline
  isEditing.value = true
}

// Save link
const saveLink = () => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      text: editingText.value,
      href: editingHref.value,
      target: editingTarget.value ? '_blank' : '_self',
      underline: editingUnderline.value
    }
  }
  
  emit('update', updatedComponent)
  isEditing.value = false
}

// Cancel edit
const cancelEdit = () => {
  isEditing.value = false
}

// Handle click
const handleClick = (e) => {
  e.preventDefault()
  // In editor mode, we prevent the default behavior
  // In preview mode, this would navigate to the link
}
</script>

<style scoped>
.link-component {
  display: inline-block;
}

.link-editor {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  margin: 5px 0;
}
</style>
