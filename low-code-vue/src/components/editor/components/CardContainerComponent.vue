<template>
  <div
    class="card-container-component"
    :style="containerStyles"
    @dragover.prevent
    @drop.stop="handleDrop"
  >
    <el-card :shadow="component.props.shadow" class="card-wrapper">
      <template #header v-if="component.props.title">
        <div class="card-header">
          <span>{{ component.props.title }}</span>
        </div>
      </template>
      <div class="card-content">
        <div class="container-children">
          <template v-if="hasChildren">
            <div
              v-for="(child, index) in component.props.children"
              :key="child.id"
              class="container-child"
            >
              <div
                class="component-wrapper"
                :class="{ 'is-selected': selectedId === child.id }"
                @click.stop="selectChildComponent(child)"
              >
                <component-renderer :component="child" @update="updateChild(index, $event)" />
                <div class="component-actions" v-if="selectedId === child.id">
                  <el-button size="small" circle icon="Delete" @click.stop="removeChildComponent(index)"></el-button>
                  <el-button size="small" circle icon="CopyDocument" @click.stop="duplicateChildComponent(index)"></el-button>
                  <el-button size="small" circle icon="Top" @click.stop="moveChildComponent(index, index - 1)" :disabled="index === 0"></el-button>
                  <el-button size="small" circle icon="Bottom" @click.stop="moveChildComponent(index, index + 1)" :disabled="index === component.props.children.length - 1"></el-button>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div v-if="!hasChildren" class="container-placeholder">
          <el-icon><Plus /></el-icon>
          <span>卡片容器（拖拽组件到这里）</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Plus, Delete, CopyDocument, Top, Bottom } from '@element-plus/icons-vue'
import ComponentRenderer from '../ComponentRenderer.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update', 'select'])

// Selected component ID
const selectedId = ref(null)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Check if container has children
const hasChildren = computed(() => {
  return props.component.props.children && props.component.props.children.length > 0
})

// Update child component
const updateChild = (index, updatedChild) => {
  // Create a new array of children to ensure reactivity
  const updatedChildren = [...(props.component.props.children || [])]

  // Create a deep copy of the updated child to ensure it's not affected by reference issues
  updatedChildren[index] = JSON.parse(JSON.stringify(updatedChild))

  // Create a new component object with the updated children
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
}

// Handle component drop
const handleDrop = (event) => {
  event.preventDefault()
  event.stopPropagation()

  const componentData = event.dataTransfer.getData('application/json')

  if (componentData) {
    try {
      const newComponent = JSON.parse(componentData)

      // Add component to children
      const updatedChildren = [...(props.component.props.children || [])]
      updatedChildren.push(newComponent)

      const updatedComponent = {
        ...props.component,
        props: {
          ...props.component.props,
          children: updatedChildren
        }
      }

      emit('update', updatedComponent)
    } catch (error) {
      console.error('Failed to parse component data:', error)
    }
  }
}

// Select child component
const selectChildComponent = (child) => {
  selectedId.value = child.id

  // Create a deep copy of the child to ensure it's not affected by reference issues
  const childCopy = JSON.parse(JSON.stringify(child))

  // Emit the select event with the child component
  emit('select', childCopy)
}

// Remove child component
const removeChildComponent = (index) => {
  const updatedChildren = [...(props.component.props.children || [])]
  updatedChildren.splice(index, 1)

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
  selectedId.value = null
}

// Duplicate child component
const duplicateChildComponent = (index) => {
  const childToDuplicate = props.component.props.children[index]
  const duplicatedChild = JSON.parse(JSON.stringify(childToDuplicate))
  duplicatedChild.id = 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000)

  const updatedChildren = [...(props.component.props.children || [])]
  updatedChildren.splice(index + 1, 0, duplicatedChild)

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
}

// Move child component
const moveChildComponent = (fromIndex, toIndex) => {
  if (toIndex < 0 || toIndex >= props.component.props.children.length) {
    return
  }

  const updatedChildren = [...(props.component.props.children || [])]
  const [movedChild] = updatedChildren.splice(fromIndex, 1)
  updatedChildren.splice(toIndex, 0, movedChild)

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
}
</script>

<style scoped>
.card-container-component {
  width: 100%;
  position: relative;
}

.card-wrapper {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  position: relative;
  min-height: 80px;
}

.container-children {
  width: 100%;
  min-height: 80px;
}

.container-child {
  margin-bottom: 10px;
  position: relative;
}

.container-child:last-child {
  margin-bottom: 0;
}

.component-wrapper {
  position: relative;
  border: 1px dashed transparent;
  transition: all 0.3s;
}

.component-wrapper:hover {
  border-color: #409eff;
}

.component-wrapper.is-selected {
  border-color: #409eff;
}

.component-actions {
  position: absolute;
  top: -15px;
  right: 0;
  display: flex;
  gap: 5px;
  z-index: 100;
}

.container-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  pointer-events: none;
  font-size: 14px;
}

.container-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
