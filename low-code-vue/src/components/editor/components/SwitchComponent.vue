<template>
  <div class="switch-component" :style="containerStyles">
    <el-switch
      v-model="switchValue"
      :active-text="component.props.activeText"
      :inactive-text="component.props.inactiveText"
      :disabled="component.props.disabled"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Switch value
const switchValue = ref(false)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.switch-component {
  width: 100%;
}
</style>
