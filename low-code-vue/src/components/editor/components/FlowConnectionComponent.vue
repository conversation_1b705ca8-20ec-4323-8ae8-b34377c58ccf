<template>
  <div class="flow-connection-component" :class="{ 'vertical': isVertical }" :style="containerStyles">
    <div class="flow-connection-line" :style="connectionStyles">
      <div class="flow-connection-arrow"></div>
    </div>
    <div v-if="component.props.label" class="flow-connection-label">
      {{ component.props.label }}
    </div>
    <div v-if="sourceLabel || targetLabel" class="flow-connection-endpoints">
      <div v-if="sourceLabel" class="flow-connection-source">{{ sourceLabel }}</div>
      <div v-if="targetLabel" class="flow-connection-target">{{ targetLabel }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Check if connection is vertical
const isVertical = computed(() => {
  return props.component.props?.orientation === 'vertical'
})

// Get source and target labels
const sourceLabel = computed(() => {
  return props.component.props?.source?.label || ''
})

const targetLabel = computed(() => {
  return props.component.props?.target?.label || ''
})

// Container styles
const containerStyles = computed(() => {
  const styles = { ...props.component.styles }

  // Only keep margin and position-related styles for the container
  const containerStyles = {
    margin: styles.margin || '10px',
    position: styles.position,
    top: styles.top,
    left: styles.left,
    right: styles.right,
    bottom: styles.bottom,
    zIndex: styles.zIndex
  }

  return containerStyles
})

// Connection line styles
const connectionStyles = computed(() => {
  const styles = { ...props.component.styles }
  const orientation = props.component.props?.orientation || 'horizontal'
  const lineStyle = props.component.props?.style || 'solid'

  // Base styles for the connection line
  const lineStyles = {
    backgroundColor: styles.backgroundColor || '#dcdfe6',
    borderColor: styles.borderColor || styles.backgroundColor || '#dcdfe6'
  }

  // Set dimensions based on orientation
  if (orientation === 'horizontal') {
    lineStyles.width = styles.width || '100px'
    lineStyles.height = styles.height || '2px'
  } else {
    lineStyles.width = styles.height || '2px' // Swap width and height for vertical
    lineStyles.height = styles.width || '100px'
  }

  // Set line style
  if (lineStyle === 'dashed') {
    lineStyles.borderTop = `${lineStyles.height} dashed ${lineStyles.borderColor}`
    lineStyles.borderLeft = `${lineStyles.width} dashed ${lineStyles.borderColor}`
    lineStyles.backgroundColor = 'transparent'
  } else if (lineStyle === 'dotted') {
    lineStyles.borderTop = `${lineStyles.height} dotted ${lineStyles.borderColor}`
    lineStyles.borderLeft = `${lineStyles.width} dotted ${lineStyles.borderColor}`
    lineStyles.backgroundColor = 'transparent'
  }

  return lineStyles
})
</script>

<style scoped>
.flow-connection-component {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  min-height: 20px;
}

.flow-connection-line {
  position: relative;
  background-color: inherit;
}

/* Horizontal orientation (default) */
.flow-connection-component:not(.vertical) {
  flex-direction: row;
}

/* Vertical orientation */
.vertical {
  flex-direction: column;
}

/* Horizontal arrow (default) */
.flow-connection-arrow {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 8px solid currentColor;
}

/* Vertical orientation arrow */
.vertical .flow-connection-arrow {
  right: 50%;
  top: auto;
  bottom: 0;
  transform: translateX(50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid currentColor;
  border-bottom: none;
}

.flow-connection-label {
  position: absolute;
  top: -20px;
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 4px;
  border-radius: 2px;
  z-index: 1;
}

.vertical .flow-connection-label {
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
}

.flow-connection-endpoints {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: #909399;
  z-index: 1;
}

.vertical .flow-connection-endpoints {
  flex-direction: column;
  height: 100%;
  width: auto;
  right: 10px;
}

.flow-connection-source {
  position: absolute;
  left: -5px;
  top: -18px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1px 3px;
  border-radius: 2px;
}

.flow-connection-target {
  position: absolute;
  right: -5px;
  top: -18px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1px 3px;
  border-radius: 2px;
}

.vertical .flow-connection-source {
  top: -5px;
  left: auto;
  right: 10px;
}

.vertical .flow-connection-target {
  top: auto;
  bottom: -5px;
  left: auto;
  right: 10px;
}
</style>
