<template>
  <div class="checkbox-component" :style="containerStyles">
    <div v-if="hasOptions">
      <el-checkbox-group v-model="checkboxValue" :disabled="isDisabled">
        <el-checkbox
          v-for="option in checkboxOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div v-else class="checkbox-placeholder">
      <el-alert
        title="请添加选项"
        type="info"
        :closable="false"
        center
      />
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Checkbox value
const checkboxValue = ref([])

// Check if component has options
const hasOptions = computed(() => {
  const options = props.component.props?.options
  return Array.isArray(options) && options.length > 0
})

// Computed properties for checkbox
const checkboxOptions = computed(() => {
  if (!props.component.props) return []
  if (!Array.isArray(props.component.props.options)) return []
  return props.component.props.options
})

const isDisabled = computed(() => {
  return props.component.props?.disabled || false
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Watch for changes in component props
watch(() => props.component, () => {
  console.log('CheckboxComponent detected component change')
}, { deep: true })

// Initialize component
onMounted(() => {
  console.log('CheckboxComponent mounted with component:', props.component.id)
})
</script>

<style scoped>
.checkbox-component {
  width: 100%;
  min-height: 40px;
}

.checkbox-placeholder {
  padding: 10px 0;
}
</style>
