<template>
  <div class="drawer-component" :style="containerStyles">
    <el-button type="primary" @click="drawerVisible = true">
      打开抽屉
    </el-button>
    
    <el-drawer
      v-model="drawerVisible"
      :title="component.props.title"
      :direction="component.props.direction"
      :size="component.props.size"
      :with-header="component.props.withHeader"
      :destroy-on-close="component.props.destroyOnClose"
      :modal="component.props.modal"
      :show-close="component.props.showClose"
      :close-on-click-modal="component.props.closeOnClickModal"
      :close-on-press-escape="component.props.closeOnPressEscape"
    >
      <span>抽屉内容</span>
    </el-drawer>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Drawer visibility
const drawerVisible = ref(false)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.drawer-component {
  width: 100%;
}
</style>
