<template>
  <div class="slider-component" :style="containerStyles">
    <el-slider
      v-model="sliderValue"
      :min="component.props.min"
      :max="component.props.max"
      :step="component.props.step"
      :show-stops="component.props.showStops"
      :show-input="component.props.showInput"
      :disabled="component.props.disabled"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Slider value
const sliderValue = ref(0)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.slider-component {
  width: 100%;
}
</style>
