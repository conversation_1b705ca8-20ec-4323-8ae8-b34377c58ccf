<template>
  <div class="time-picker-component" :style="containerStyles">
    <el-time-picker
      v-model="timeValue"
      :placeholder="component.props.placeholder"
      :clearable="component.props.clearable"
      :disabled="component.props.disabled"
      style="width: 100%"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Time value
const timeValue = ref(null)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.time-picker-component {
  width: 100%;
}
</style>
