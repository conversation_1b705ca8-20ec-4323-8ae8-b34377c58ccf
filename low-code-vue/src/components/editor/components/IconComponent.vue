<template>
  <div class="icon-component" :style="containerStyles">
    <div v-if="isEditing" class="icon-editor">
      <el-form label-position="top" size="small">
        <el-form-item label="图标">
          <el-select v-model="editingName" filterable>
            <el-option 
              v-for="icon in availableIcons" 
              :key="icon.name" 
              :label="icon.label" 
              :value="icon.name"
            >
              <div class="icon-option">
                <el-icon><component :is="icon.name" /></el-icon>
                <span>{{ icon.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="大小">
          <el-select v-model="editingSize">
            <el-option label="小" value="small" />
            <el-option label="默认" value="default" />
            <el-option label="大" value="large" />
          </el-select>
        </el-form-item>
        <el-form-item label="颜色">
          <el-color-picker v-model="editingColor" show-alpha />
        </el-form-item>
        <el-button type="primary" size="small" @click="saveIcon">保存</el-button>
        <el-button size="small" @click="cancelEdit">取消</el-button>
      </el-form>
    </div>
    <el-icon 
      v-else
      :size="component.props.size"
      :color="component.props.color"
      @dblclick.stop="startEditing"
    >
      <component :is="component.props.name" />
    </el-icon>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import * as ElementPlusIcons from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Available icons
const availableIcons = computed(() => {
  return Object.keys(ElementPlusIcons).map(name => ({
    name,
    label: name.replace(/([A-Z])/g, ' $1').trim()
  }))
})

// Editing state
const isEditing = ref(false)
const editingName = ref('')
const editingSize = ref('default')
const editingColor = ref('')

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Start editing
const startEditing = () => {
  editingName.value = props.component.props.name
  editingSize.value = props.component.props.size
  editingColor.value = props.component.props.color
  isEditing.value = true
}

// Save icon
const saveIcon = () => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      name: editingName.value,
      size: editingSize.value,
      color: editingColor.value
    }
  }
  
  emit('update', updatedComponent)
  isEditing.value = false
}

// Cancel edit
const cancelEdit = () => {
  isEditing.value = false
}
</script>

<style scoped>
.icon-component {
  display: inline-block;
  cursor: pointer;
}

.icon-editor {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  margin: 5px 0;
  width: 250px;
}

.icon-option {
  display: flex;
  align-items: center;
}

.icon-option .el-icon {
  margin-right: 8px;
}
</style>
