<template>
  <div
    class="card-component"
    :style="containerStyles"
    @dragover.prevent
    @drop.stop="handleDrop">
    <el-card :shadow="component.props.shadow">
      <template #header v-if="component.props.title">
        <div class="card-header">
          {{ component.props.title }}
        </div>
      </template>
      <div class="card-content">
        <template v-if="hasChildren">
          <div
            v-for="(child, index) in component.props.children"
            :key="child.id"
            class="card-child"
          >
            <component-renderer :component="child" @update="updateChild(index, $event)" />
          </div>
        </template>
        <div v-else class="card-placeholder">
          <el-icon><Document /></el-icon>
          <span>卡片内容（拖拽组件到这里）</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Document } from '@element-plus/icons-vue'
import ComponentRenderer from '../ComponentRenderer.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Check if card has children
const hasChildren = computed(() => {
  return props.component.props.children && props.component.props.children.length > 0
})

// Update child component
const updateChild = (index, updatedChild) => {
  const updatedChildren = [...(props.component.props.children || [])]
  updatedChildren[index] = updatedChild

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
}

// Handle component drop
const handleDrop = (event) => {
  event.preventDefault()
  event.stopPropagation()

  const componentData = event.dataTransfer.getData('application/json')

  if (componentData) {
    try {
      const newComponent = JSON.parse(componentData)

      // Add component to children
      const updatedChildren = [...(props.component.props.children || [])]
      updatedChildren.push(newComponent)

      const updatedComponent = {
        ...props.component,
        props: {
          ...props.component.props,
          children: updatedChildren
        }
      }

      emit('update', updatedComponent)
    } catch (error) {
      console.error('Failed to parse component data:', error)
    }
  }
}
</script>

<style scoped>
.card-component {
  width: 100%;
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #303133;
}

.card-content {
  min-height: 80px;
  position: relative;
}

.card-child {
  margin-bottom: 10px;
}

.card-child:last-child {
  margin-bottom: 0;
}

.card-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  pointer-events: none;
  font-size: 14px;
}

.card-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
