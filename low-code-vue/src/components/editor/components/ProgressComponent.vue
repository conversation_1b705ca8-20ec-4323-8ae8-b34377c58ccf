<template>
  <div class="progress-component" :style="containerStyles">
    <el-progress
      :percentage="component.props.percentage"
      :type="component.props.type"
      :stroke-width="component.props.strokeWidth"
      :text-inside="component.props.textInside"
      :status="component.props.status"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.progress-component {
  width: 100%;
}
</style>
