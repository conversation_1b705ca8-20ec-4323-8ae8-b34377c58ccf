<template>
  <div
    class="row-component"
    :style="containerStyles"
    @dragover.prevent
    @drop.stop="handleDrop">
    <el-row
      :gutter="component.props.gutter"
      :justify="component.props.justify"
      :align="component.props.align"
    >
      <template v-if="hasChildren">
        <el-col
          v-for="(child, index) in component.props.children"
          :key="child.id"
          :span="24 / component.props.children.length"
        >
          <div
            class="component-wrapper"
            :class="{ 'is-selected': selectedId === child.id }"
            @click.stop="selectChildComponent(child)"
          >
            <component-renderer :component="child" @update="updateChild(index, $event)" @select="selectChildComponent" />
            <div class="component-actions" v-if="selectedId === child.id">
              <el-button size="small" circle icon="Delete" @click.stop="removeChildComponent(index)"></el-button>
              <el-button size="small" circle icon="CopyDocument" @click.stop="duplicateChildComponent(index)"></el-button>
              <el-button size="small" circle icon="Top" @click.stop="moveChildComponent(index, index - 1)" :disabled="index === 0"></el-button>
              <el-button size="small" circle icon="Bottom" @click.stop="moveChildComponent(index, index + 1)" :disabled="index === component.props.children.length - 1"></el-button>
            </div>
          </div>
        </el-col>
      </template>
      <div v-else class="row-placeholder">
        <el-icon><Grid /></el-icon>
        <span>行（拖拽组件到这里）</span>
      </div>
    </el-row>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Grid, Delete, CopyDocument, Top, Bottom } from '@element-plus/icons-vue'
import ComponentRenderer from '../ComponentRenderer.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update', 'select'])

// Selected component ID
const selectedId = ref(null)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Check if row has children
const hasChildren = computed(() => {
  return props.component.props.children && props.component.props.children.length > 0
})

// Update child component
const updateChild = (index, updatedChild) => {
  const updatedChildren = [...(props.component.props.children || [])]
  updatedChildren[index] = updatedChild

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
}

// Handle component drop
const handleDrop = (event) => {
  event.preventDefault()
  event.stopPropagation()

  const componentData = event.dataTransfer.getData('application/json')

  if (componentData) {
    try {
      const newComponent = JSON.parse(componentData)

      // Add component to children
      const updatedChildren = [...(props.component.props.children || [])]
      updatedChildren.push(newComponent)

      const updatedComponent = {
        ...props.component,
        props: {
          ...props.component.props,
          children: updatedChildren
        }
      }

      emit('update', updatedComponent)
    } catch (error) {
      console.error('Failed to parse component data:', error)
    }
  }
}

// Select child component
const selectChildComponent = (child) => {
  selectedId.value = child.id
  emit('select', child)
}

// Remove child component
const removeChildComponent = (index) => {
  const updatedChildren = [...(props.component.props.children || [])]
  updatedChildren.splice(index, 1)

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
  selectedId.value = null
}

// Duplicate child component
const duplicateChildComponent = (index) => {
  const childToDuplicate = props.component.props.children[index]
  const duplicatedChild = JSON.parse(JSON.stringify(childToDuplicate))
  duplicatedChild.id = 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000)

  const updatedChildren = [...(props.component.props.children || [])]
  updatedChildren.splice(index + 1, 0, duplicatedChild)

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
}

// Move child component
const moveChildComponent = (fromIndex, toIndex) => {
  if (toIndex < 0 || toIndex >= props.component.props.children.length) {
    return
  }

  const updatedChildren = [...(props.component.props.children || [])]
  const [movedChild] = updatedChildren.splice(fromIndex, 1)
  updatedChildren.splice(toIndex, 0, movedChild)

  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      children: updatedChildren
    }
  }

  emit('update', updatedComponent)
}
</script>

<style scoped>
.row-component {
  width: 100%;
  min-height: 80px;
  position: relative;
  margin-bottom: 15px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: rgba(245, 247, 250, 0.6);
  padding: 10px;
}

.component-wrapper {
  position: relative;
  border: 1px dashed transparent;
  transition: all 0.3s;
  height: 100%;
  min-height: 60px;
}

.component-wrapper:hover {
  border-color: #409eff;
}

.component-wrapper.is-selected {
  border-color: #409eff;
}

.component-actions {
  position: absolute;
  top: -15px;
  right: 0;
  display: flex;
  gap: 5px;
  z-index: 100;
}

.row-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  pointer-events: none;
  font-size: 14px;
}

.row-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
