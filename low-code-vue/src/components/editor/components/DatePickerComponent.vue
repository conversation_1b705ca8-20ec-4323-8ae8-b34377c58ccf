<template>
  <div class="date-picker-component" :style="containerStyles">
    <el-date-picker
      v-model="dateValue"
      :type="component.props.type"
      :placeholder="component.props.placeholder"
      :format="component.props.format"
      :clearable="component.props.clearable"
      :disabled="component.props.disabled"
      style="width: 100%"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Date value
const dateValue = ref(null)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.date-picker-component {
  width: 100%;
}
</style>
