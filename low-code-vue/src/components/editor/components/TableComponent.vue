<template>
  <div class="table-component" :style="containerStyles">
    <el-table
      :data="component.props.data"
      :stripe="component.props.stripe"
      :border="component.props.border"
      style="width: 100%"
    >
      <el-table-column
        v-for="column in component.props.columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
      />
    </el-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.table-component {
  width: 100%;
}
</style>
