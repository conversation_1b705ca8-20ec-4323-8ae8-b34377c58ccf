<template>
  <div class="text-component" :style="componentStyles">
    <div v-if="isEditing" class="text-editor">
      <el-input
        type="textarea"
        v-model="editingContent"
        :rows="getRows(editingContent)"
        @blur="saveContent"
        ref="textareaRef"
        autofocus
      />
    </div>
    <div v-else @dblclick="startEditing" class="text-content">
      {{ component.props.content }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref, nextTick } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Editing state
const isEditing = ref(false)
const editingContent = ref('')
const textareaRef = ref(null)

// Computed styles
const componentStyles = computed(() => {
  return props.component.styles || {}
})

// Start editing
const startEditing = () => {
  editingContent.value = props.component.props.content
  isEditing.value = true

  // Focus the textarea after it's rendered
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.focus()
    }
  })
}

// Save content
const saveContent = () => {
  isEditing.value = false

  // Only update if content has changed
  if (editingContent.value !== props.component.props.content) {
    const updatedComponent = {
      ...props.component,
      props: {
        ...props.component.props,
        content: editingContent.value
      }
    }

    emit('update', updatedComponent)
  }
}

// Calculate rows for textarea based on content
const getRows = (content) => {
  if (!content) return 1
  const lineCount = (content.match(/\n/g) || []).length + 1
  return Math.min(Math.max(lineCount, 1), 5) // Min 1, max 5 rows
}
</script>

<style scoped>
.text-component {
  width: 100%;
}

.text-content {
  cursor: text;
  white-space: pre-wrap;
}

.text-editor {
  width: 100%;
}
</style>
