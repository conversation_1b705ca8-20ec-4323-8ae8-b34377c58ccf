<template>
  <div class="pagination-component" :style="containerStyles">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="component.props.total"
      :layout="component.props.layout"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Pagination state
const currentPage = ref(props.component.props.currentPage || 1)
const pageSize = ref(props.component.props.pageSize || 10)

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.pagination-component {
  width: 100%;
}
</style>
