<template>
  <div class="color-picker-component" :style="containerStyles">
    <el-color-picker
      v-model="colorValue"
      :show-alpha="component.props.showAlpha"
      :color-format="component.props.colorFormat"
    />
    <span v-if="colorValue" class="color-value">{{ colorValue }}</span>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Color value
const colorValue = ref('')

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})
</script>

<style scoped>
.color-picker-component {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-value {
  font-size: 14px;
  color: #606266;
}
</style>
