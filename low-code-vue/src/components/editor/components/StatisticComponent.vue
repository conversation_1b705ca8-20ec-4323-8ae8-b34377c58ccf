<template>
  <div class="statistic-component" :style="containerStyles" :class="{ 'is-disabled': isDisabled }">
    <div class="statistic-card">
      <div class="statistic-title">{{ component.props.title || '统计数值' }}</div>
      <div class="statistic-value" :style="valueStyle">
        <span v-if="component.props.prefix" class="prefix">{{ component.props.prefix }}</span>
        {{ formatValue(component.props.value) }}
        <span v-if="component.props.suffix" class="suffix">{{ component.props.suffix }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

// Computed styles
const containerStyles = computed(() => {
  return props.component.styles || {}
})

// Value style
const valueStyle = computed(() => {
  return props.component.props.valueStyle || {}
})

// Check if component is disabled
const isDisabled = computed(() => {
  return props.component.disabled === true
})

// Format value with precision
const formatValue = (value) => {
  if (value === undefined || value === null) return '0'

  const numValue = Number(value)
  if (isNaN(numValue)) return value

  const precision = props.component.props.precision !== undefined ? props.component.props.precision : 0
  return numValue.toFixed(precision)
}
</script>

<style scoped>
.statistic-component {
  width: 100%;
}

.statistic-card {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.statistic-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.prefix, .suffix {
  font-size: 16px;
  margin: 0 4px;
}

.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
</style>
