<template>
  <div class="component-list">
    <div class="component-list-header">
      <h3>组件库</h3>
      <div class="component-list-actions">
        <el-tooltip content="展开全部" placement="top">
          <el-button size="small" circle @click="expandAll">
            <el-icon><Expand /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="折叠全部" placement="top">
          <el-button size="small" circle @click="collapseAll">
            <el-icon><Fold /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <el-input
      v-model="searchText"
      placeholder="搜索组件"
      prefix-icon="Search"
      clearable
      class="component-search"
    />
    <el-collapse v-model="activeCategories" class="component-categories">
      <el-collapse-item
        v-for="category in filteredCategories"
        :key="category.code"
        :name="category.code"
        class="category-item"
      >
        <template #title>
          <div class="category-header">
            <template v-if="isBase64Icon(category.icon)">
              <img :src="category.icon" class="custom-icon" alt="category icon" />
            </template>
            <template v-else-if="isCustomIcon(category.icon)">
              <img :src="getIconUrl(category.icon)" class="custom-icon" alt="category icon" />
            </template>
            <template v-else>
              <el-icon><component :is="category.icon || 'Folder'" /></el-icon>
            </template>
            <span>{{ category.name }}</span>
            <span class="component-count">{{ getComponentsForCategory(category.code).length }}</span>
          </div>
        </template>

        <div class="component-grid">
          <div
            v-for="component in getFilteredComponentsForCategory(category.code)"
            :key="component.type"
            class="component-card"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <div class="component-icon">
              <template v-if="isBase64Icon(component.icon)">
                <img :src="component.icon" class="custom-icon" alt="component icon" />
              </template>
              <template v-else-if="isCustomIcon(component.icon)">
                <img :src="getIconUrl(component.icon)" class="custom-icon" alt="component icon" />
              </template>
              <template v-else>
                <el-icon><component :is="component.icon || 'Document'" /></el-icon>
              </template>
            </div>
            <div class="component-name">{{ component.name }}</div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import { useEditorStore } from '../../store'
import { Folder, Search, Expand, Fold } from '@element-plus/icons-vue'
import { componentCategoryApi } from '../../api/componentCategory'
import { iconApi } from '../../api/icon'
import { ElLoading, ElMessage } from 'element-plus'

const editorStore = useEditorStore()

// Search text
const searchText = ref('')

// Active categories (for collapse)
const activeCategories = ref([])

// Categories from API
const categories = ref([])

// Expand all categories
const expandAll = () => {
  activeCategories.value = categories.value.map(cat => cat.code)
}

// Collapse all categories
const collapseAll = () => {
  activeCategories.value = []
}

// Load categories from API
const loadCategories = async () => {
  const loadingInstance = ElLoading.service({
    target: '.component-list',
    text: '加载组件分类...',
    background: 'rgba(255, 255, 255, 0.7)'
  })

  try {
    console.log('Fetching component categories from API...')
    const result = await componentCategoryApi.getAllCategories()
    console.log('API response for categories:', result)

    if (result && Array.isArray(result) && result.length > 0) {
      categories.value = result
      console.log('Loaded component categories from API:', result)
    } else {
      console.warn('No categories returned from API or invalid format, using default categories')
      useDefaultCategories()
    }
  } catch (error) {
    console.error('Error loading component categories from API:', error)
    ElMessage.warning('加载组件分类失败，使用默认分类')
    useDefaultCategories()
  } finally {
    loadingInstance.close()
  }
}

// Use default categories
const useDefaultCategories = () => {
  console.log('Using default categories')
  const defaultCategories = [
    { id: 1, code: 'basic', name: '基础组件', icon: 'Document', sort: 1 },
    { id: 2, code: 'form', name: '表单组件', icon: 'Edit', sort: 2 },
    { id: 3, code: 'layout', name: '布局组件', icon: 'Grid', sort: 3 },
    { id: 4, code: 'advanced', name: '高级组件', icon: 'Star', sort: 4 },
    { id: 5, code: 'flow', name: '流程组件', icon: 'Connection', sort: 5 },
    { id: 6, code: 'icon', name: '图标组件', icon: 'PictureRounded', sort: 6 },
    { id: 7, code: 'custom', name: '自定义组件', icon: 'SetUp', sort: 7 }
  ]

  console.log('Default categories:', defaultCategories)
  categories.value = defaultCategories
}

// Check if icon is a base64 string
const isBase64Icon = (icon) => {
  return icon && typeof icon === 'string' && icon.startsWith('data:')
}

// Check if icon is a custom icon (URL or filename)
const isCustomIcon = (icon) => {
  return icon && typeof icon === 'string' && (
    icon.startsWith('http') ||
    icon.startsWith('/') ||
    icon.includes('.png') ||
    icon.includes('.jpg') ||
    icon.includes('.jpeg') ||
    icon.includes('.svg') ||
    icon.includes('.gif')
  )
}

// Get icon URL
const getIconUrl = (icon) => {
  return iconApi.getIconUrl(icon)
}

// Filter out disabled categories and then apply search filter
const filteredCategories = computed(() => {
  console.log('Filtering categories, total:', categories.value.length);

  // First filter out disabled categories
  const enabledCategories = categories.value.filter(category => {
    // If enabled is undefined or 1, show the category (default to enabled)
    const isEnabled = category.enabled === undefined || category.enabled === 1 || category.enabled === '1';
    if (!isEnabled) {
      console.log(`Category ${category.name} (${category.code}) is disabled and will not be shown`);
    }
    return isEnabled;
  })

  console.log(`${enabledCategories.length} of ${categories.value.length} categories are enabled`);

  // Then apply search filter if needed
  if (!searchText.value) {
    return enabledCategories
  }

  const searchLower = searchText.value.toLowerCase()
  return enabledCategories.filter(category => {
    // Include category if its name matches or if any of its components match
    const categoryMatches = category.name.toLowerCase().includes(searchLower)
    const hasMatchingComponents = getFilteredComponentsForCategory(category.code).length > 0
    return categoryMatches || hasMatchingComponents
  })
})

// 只打开第一个分类
const openFirstCategory = () => {
  if (categories.value && categories.value.length > 0) {
    // 按照 sort 字段排序分类
    const sortedCategories = [...categories.value].sort((a, b) => (a.sort || 0) - (b.sort || 0))
    // 只打开第一个分类
    activeCategories.value = [sortedCategories[0].code]
    console.log('Opening first category:', sortedCategories[0].code)
  } else {
    activeCategories.value = []
  }
}

// Load categories on mount
onMounted(() => {
  loadCategories()
  // 只打开第一个分类
  openFirstCategory()
})

// Watch for changes in categories and update active categories
watch(categories, () => {
  // 只打开第一个分类
  openFirstCategory()
})

// Watch for changes in search text
watch(searchText, (newValue) => {
  if (newValue) {
    // 如果有搜索文本，展开所有包含搜索结果的分类
    const matchingCategories = filteredCategories.value.map(cat => cat.code)
    activeCategories.value = matchingCategories
  } else {
    // 如果搜索文本为空，只打开第一个分类
    openFirstCategory()
  }
})

// Get components for a category (only enabled components)
const getComponentsForCategory = (categoryCode) => {
  // First get all components for the category
  console.log(`Getting components for category: ${categoryCode}, total components in library:`, editorStore.componentLibrary.length)
  let allComponents = [];

  switch (categoryCode) {
    case 'basic':
      allComponents = editorStore.componentLibrary.filter(c => ['text', 'heading', 'paragraph', 'image', 'divider', 'button', 'link', 'tag', 'badge', 'alert'].includes(c.type))
      break;
    case 'form':
      allComponents = editorStore.componentLibrary.filter(c => [
        'input', 'textarea', 'select', 'checkbox', 'radio', 'switch', 'slider', 'rate',
        'date-picker', 'time-picker', 'color-picker', 'upload',
        // 新增的输入框组件
        'number-input', 'password-input', 'search-input'
      ].includes(c.type))
      break;
    case 'layout':
      allComponents = editorStore.componentLibrary.filter(c => [
        'container', 'row', 'column', 'card', 'tabs', 'divider', 'space', 'drawer',
        // 新增的容器组件
        'card-container', 'tab-container', 'collapse-container', 'grid-container'
      ].includes(c.type))
      break;
    case 'advanced':
      allComponents = editorStore.componentLibrary.filter(c => ['table', 'pagination', 'progress', 'timeline', 'carousel', 'collapse', 'steps', 'result', 'empty', 'descriptions'].includes(c.type))
      break;
    case 'data':
      allComponents = editorStore.componentLibrary.filter(c => ['statistic', 'line-chart', 'bar-chart', 'pie-chart'].includes(c.type))
      break;
    case 'flow':
      allComponents = editorStore.componentLibrary.filter(c => ['flowNode', 'flowConnection', 'flowStart', 'flowEnd', 'flowDecision', 'flowProcess'].includes(c.type))
      break;
    case 'icon':
      allComponents = editorStore.componentLibrary.filter(c => ['icon'].includes(c.type))
      break;
    case 'custom':
      allComponents = editorStore.componentLibrary.filter(c => c.category === 'custom')
      break;
    default:
      allComponents = editorStore.componentLibrary.filter(c => c.category === categoryCode)
      break;
  }

  // Filter out disabled components
  const enabledComponents = allComponents.filter(component => {
    // If enabled is undefined or 1, show the component (default to enabled)
    const isEnabled = component.enabled === undefined || component.enabled === 1 || component.enabled === '1';
    if (!isEnabled) {
      console.log(`Component ${component.name} (${component.type}) is disabled and will not be shown`);
    }
    return isEnabled;
  });

  console.log(`Category ${categoryCode}: ${enabledComponents.length} of ${allComponents.length} components are enabled`);
  console.log(`Components for category ${categoryCode}:`, enabledComponents.map(c => c.type));
  return enabledComponents;
};

// Get filtered components for a category based on search text
const getFilteredComponentsForCategory = (categoryCode) => {
  const components = getComponentsForCategory(categoryCode);

  if (!searchText.value) {
    return components;
  }

  const searchLower = searchText.value.toLowerCase();
  return components.filter(component => {
    return component.name.toLowerCase().includes(searchLower) ||
           component.type.toLowerCase().includes(searchLower);
  });
};

// Handle drag start
const handleDragStart = (event, component) => {
  // Create a new component instance
  const newComponent = {
    id: uuidv4(),
    type: component.type,
    props: { ...component.defaultProps },
    styles: { ...component.defaultStyles },
    events: []
  }

  // Set drag data
  event.dataTransfer.setData('application/json', JSON.stringify(newComponent))
}
</script>

<style scoped>
.component-list {
  height: 100%;
  overflow-y: auto;
  padding: 0 5px;
  background-color: #f5f7fa;
}

.component-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 10px;
}

.component-list-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.component-list-actions {
  display: flex;
  gap: 5px;
}

.component-search {
  margin-bottom: 15px;
}

.component-categories {
  border: none;
}

:deep(.el-collapse-item__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 5px;
  height: 40px;
}

:deep(.el-collapse-item__content) {
  padding: 10px 0;
}

.category-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.category-header .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.component-count {
  margin-left: auto;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 12px;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 5px;
}

.component-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s;
  background-color: #fff;
  height: 80px;
  width: 100%;
  box-sizing: border-box;
}

.component-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px 0 rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
  background-color: #f0f9ff;
}

.component-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  margin-bottom: 4px;
}

.component-icon .el-icon {
  font-size: 24px;
  color: #409eff;
}

.component-name {
  font-size: 12px;
  color: #606266;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  margin-top: 5px;
}

.custom-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

:deep(.el-collapse-item__arrow) {
  margin-left: 8px;
}
</style>
