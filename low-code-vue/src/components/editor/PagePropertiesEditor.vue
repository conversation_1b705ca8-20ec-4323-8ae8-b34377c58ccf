<template>
  <div class="page-properties-editor">
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息" lazy>
        <el-form label-position="top">
          <el-form-item label="页面名称">
            <el-input v-model="pageData.name" @change="updatePage" />
          </el-form-item>
          
          <el-form-item label="页面标题">
            <el-input v-model="pageData.title" @change="updatePage" />
          </el-form-item>
          
          <el-form-item label="页面路径">
            <el-input v-model="pageData.path" @change="updatePage" />
          </el-form-item>
          
          <el-form-item label="页面描述">
            <el-input
              type="textarea"
              v-model="pageData.description"
              :rows="3"
              @change="updatePage"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="背景设置" lazy>
        <page-background-settings
          :pageConfig="pageData"
          @update="updatePageConfig"
        />
      </el-tab-pane>
      
      <el-tab-pane label="高级设置" lazy>
        <el-form label-position="top">
          <el-form-item label="Meta 标签">
            <el-input
              type="textarea"
              v-model="pageData.meta"
              :rows="5"
              placeholder="例如: { &quot;keywords&quot;: &quot;关键词1,关键词2&quot;, &quot;description&quot;: &quot;页面描述&quot; }"
              @change="updatePage"
            />
            <div class="tip-text">使用 JSON 格式定义页面的 Meta 标签</div>
          </el-form-item>
          
          <el-form-item label="自定义 CSS">
            <el-input
              type="textarea"
              v-model="pageData.customCss"
              :rows="5"
              placeholder=".my-class { color: red; }"
              @change="updatePage"
            />
          </el-form-item>
          
          <el-form-item label="自定义 JavaScript">
            <el-input
              type="textarea"
              v-model="pageData.customJs"
              :rows="5"
              placeholder="function myFunction() { console.log('Hello'); }"
              @change="updatePage"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import PageBackgroundSettings from './PageBackgroundSettings.vue'

const props = defineProps({
  pageConfig: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 页面数据
const pageData = reactive({
  id: null,
  projectId: null,
  name: '',
  title: '',
  path: '',
  description: '',
  meta: '',
  customCss: '',
  customJs: '',
  styles: {},
  components: []
})

// 初始化页面数据
onMounted(() => {
  Object.assign(pageData, props.pageConfig)
  
  // 确保 styles 对象存在
  if (!pageData.styles) {
    pageData.styles = {}
  }
})

// 监听页面配置变化
watch(() => props.pageConfig, (newConfig) => {
  Object.assign(pageData, newConfig)
  
  // 确保 styles 对象存在
  if (!pageData.styles) {
    pageData.styles = {}
  }
}, { deep: true })

// 更新页面
const updatePage = () => {
  emit('update', { ...pageData })
}

// 更新页面配置
const updatePageConfig = (updatedConfig) => {
  Object.assign(pageData, updatedConfig)
  updatePage()
}
</script>

<style scoped>
.page-properties-editor {
  padding: 10px 0;
}

.tip-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
