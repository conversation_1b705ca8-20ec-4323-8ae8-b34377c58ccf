<template>
  <div class="image-uploader">
    <el-upload
      class="uploader"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :http-request="customUpload"
    >
      <img v-if="modelValue" :src="modelValue" class="preview-image" />
      <div v-else class="uploader-placeholder">
        <el-icon><Plus /></el-icon>
        <div class="uploader-text">点击上传图片</div>
      </div>
    </el-upload>

    <div class="uploader-actions" v-if="modelValue">
      <el-button type="danger" size="small" @click="clearImage">删除图片</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { uploadImage, IMAGE_CATEGORIES, BUSINESS_TYPES } from '../../api/image'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  // 图片分类，默认为用户上传
  category: {
    type: Number,
    default: IMAGE_CATEGORIES.USER_UPLOAD
  },
  // 业务类型
  businessType: {
    type: String,
    default: BUSINESS_TYPES.USER
  },
  // 业务ID
  businessId: {
    type: Number,
    default: null
  },
  // 备注
  remark: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success'])

// 上传前检查
const beforeUpload = (file) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小 (限制为10MB)
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过10MB!')
    return false
  }

  return true
}

// 自定义上传
const customUpload = async (options) => {
  try {
    const file = options.file

    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)
    formData.append('category', props.category)
    if (props.businessType) {
      formData.append('businessType', props.businessType)
    }
    if (props.businessId) {
      formData.append('businessId', props.businessId)
    }
    if (props.remark) {
      formData.append('remark', props.remark)
    }

    console.log('开始上传图片:', {
      fileName: file.name,
      fileSize: file.size,
      category: props.category,
      businessType: props.businessType,
      businessId: props.businessId
    })

    // 调用后端API上传
    const response = await uploadImage(formData)

    console.log('后端响应完整数据:', response)

    if (response.code === 200) {
      const imageData = response.data
      console.log('图片上传成功，返回数据:', imageData)
      console.log('图片URL:', imageData.url)

      // 检查URL是否存在
      if (!imageData.url) {
        console.error('警告：后端返回的数据中没有URL字段')
        ElMessage.error('上传成功但无法获取图片URL，请检查MinIO服务')
        return
      }

      // 更新值
      emit('update:modelValue', imageData.url)
      emit('upload-success', imageData)
      ElMessage.success('图片上传成功')
    } else {
      console.error('后端返回错误:', response)
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败详细信息:', error)
    console.error('错误堆栈:', error.stack)
    ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))
  }
}

// 清除图片
const clearImage = () => {
  emit('update:modelValue', '')
}
</script>

<style scoped>
.image-uploader {
  width: 100%;
}

.uploader {
  width: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.uploader:hover {
  border-color: #409eff;
}

.uploader-placeholder {
  width: 100%;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.uploader-text {
  margin-top: 10px;
  font-size: 14px;
}

.preview-image {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
  display: block;
}

.uploader-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
