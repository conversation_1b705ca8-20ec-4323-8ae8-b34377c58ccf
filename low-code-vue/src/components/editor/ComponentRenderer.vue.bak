<template>
  <div :class="{ 'component-wrapper': true, 'is-disabled': component.disabled }">
    <event-handler-wrapper
      :component="component"
      :is-preview="isPreview"
      :page-components="pageComponents"
      @toggle-component="handleToggleComponent"
    >
      <component :is="componentType" :component="component" @update="updateComponent" @select="selectComponent" />
    </event-handler-wrapper>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import EventHandlerWrapper from './EventHandlerWrapper.vue'
// 基础组件
import TextComponent from './components/TextComponent.vue'
import HeadingComponent from './components/HeadingComponent.vue'
import ParagraphComponent from './components/ParagraphComponent.vue'
import ImageComponent from './components/ImageComponent.vue'
import DividerComponent from './components/DividerComponent.vue'
import ButtonComponent from './components/ButtonComponent.vue'
import LinkComponent from './components/LinkComponent.vue'
import IconComponent from './components/IconComponent.vue'
import TagComponent from './components/TagComponent.vue'
import BadgeComponent from './components/BadgeComponent.vue'
import AlertComponent from './components/AlertComponent.vue'

// 表单组件
import InputComponent from './components/InputComponent.vue'
import TextareaComponent from './components/TextareaComponent.vue'
import SelectComponent from './components/SelectComponent.vue'
import RadioComponent from './components/RadioComponent.vue'
import CheckboxComponent from './components/CheckboxComponent.vue'
import SwitchComponent from './components/SwitchComponent.vue'
import SliderComponent from './components/SliderComponent.vue'
import RateComponent from './components/RateComponent.vue'
import DatePickerComponent from './components/DatePickerComponent.vue'
import TimePickerComponent from './components/TimePickerComponent.vue'
import ColorPickerComponent from './components/ColorPickerComponent.vue'
import UploadComponent from './components/UploadComponent.vue'

// 布局组件
import ContainerComponent from './components/ContainerComponent.vue'
import RowComponent from './components/RowComponent.vue'
import ColumnComponent from './components/ColumnComponent.vue'
import CardComponent from './components/CardComponent.vue'
import TabsComponent from './components/TabsComponent.vue'
import SpaceComponent from './components/SpaceComponent.vue'
import CardContainerComponent from './components/CardContainerComponent.vue'
import TabContainerComponent from './components/TabContainerComponent.vue'
import CollapseContainerComponent from './components/CollapseContainerComponent.vue'
import GridContainerComponent from './components/GridContainerComponent.vue'
import DrawerComponent from './components/DrawerComponent.vue'

// 高级组件
import TableComponent from './components/TableComponent.vue'
import PaginationComponent from './components/PaginationComponent.vue'
import ProgressComponent from './components/ProgressComponent.vue'
import TimelineComponent from './components/TimelineComponent.vue'
import CarouselComponent from './components/CarouselComponent.vue'
import CollapseComponent from './components/CollapseComponent.vue'
import StepsComponent from './components/StepsComponent.vue'
import ResultComponent from './components/ResultComponent.vue'
import EmptyComponent from './components/EmptyComponent.vue'
import DescriptionsComponent from './components/DescriptionsComponent.vue'

// 数据组件
import StatisticComponent from './components/StatisticComponent.vue'
import SimpleChartComponent from './components/SimpleChartComponent.vue'

// 流程组件
import FlowNodeComponent from './components/FlowNodeComponent.vue'
import FlowConnectionComponent from './components/FlowConnectionComponent.vue'
import FlowStartComponent from './components/FlowStartComponent.vue'
import FlowEndComponent from './components/FlowEndComponent.vue'
import FlowDecisionComponent from './components/FlowDecisionComponent.vue'
import FlowProcessComponent from './components/FlowProcessComponent.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  },
  isPreview: {
    type: Boolean,
    default: false
  },
  pageComponents: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update', 'select', 'toggle-component'])

// Update component
const updateComponent = (updatedComponent) => {
  console.log('ComponentRenderer received update for:', updatedComponent.type)

  if (updatedComponent.type === 'carousel') {
    console.log('Carousel items in renderer:', JSON.stringify(updatedComponent.props.items))
  }

  // Log events for debugging
  if (updatedComponent.events) {
    console.log('Component has events:', JSON.stringify(updatedComponent.events))

    // 检查事件类型
    if (Array.isArray(updatedComponent.events)) {
      console.log('Events is an array with length:', updatedComponent.events.length)
      updatedComponent.events.forEach((event, index) => {
        console.log(`Event ${index}:`, event)
      })
    } else {
      console.log('Events is an object with keys:', Object.keys(updatedComponent.events))
      if (updatedComponent.events.click) {
        console.log('Click event:', updatedComponent.events.click)
      }
    }
  } else {
    console.log('Component has no events')
  }

  // Create a completely new component object to ensure reactivity
  const newComponent = JSON.parse(JSON.stringify(updatedComponent))

  // 确保事件配置被正确地序列化
  if (newComponent.events) {
    console.log('Serialized events:', JSON.stringify(newComponent.events))
  }

  emit('update', newComponent)
}

// Select component
const selectComponent = (component) => {
  emit('select', component)
}

// Handle toggle component action
const handleToggleComponent = (payload) => {
  emit('toggle-component', payload)
}

// Map component types to components
const componentMap = {
  // 基础组件
  text: TextComponent,
  heading: HeadingComponent,
  paragraph: ParagraphComponent,
  image: ImageComponent,
  divider: DividerComponent,
  button: ButtonComponent,
  link: LinkComponent,
  icon: IconComponent,
  tag: TagComponent,
  badge: BadgeComponent,
  alert: AlertComponent,

  // 表单组件
  input: InputComponent,
  textarea: TextareaComponent,
  select: SelectComponent,
  radio: RadioComponent,
  checkbox: CheckboxComponent,
  switch: SwitchComponent,
  slider: SliderComponent,
  rate: RateComponent,
  'date-picker': DatePickerComponent,
  'time-picker': TimePickerComponent,
  'color-picker': ColorPickerComponent,
  upload: UploadComponent,

  // 布局组件
  container: ContainerComponent,
  row: RowComponent,
  column: ColumnComponent,
  card: CardComponent,
  tabs: TabsComponent,
  space: SpaceComponent,
  drawer: DrawerComponent,
  'card-container': CardContainerComponent,
  'tab-container': TabContainerComponent,
  'collapse-container': CollapseContainerComponent,
  'grid-container': GridContainerComponent,

  // 高级组件
  table: TableComponent,
  pagination: PaginationComponent,
  progress: ProgressComponent,
  timeline: TimelineComponent,
  carousel: CarouselComponent,
  collapse: CollapseComponent,
  steps: StepsComponent,
  result: ResultComponent,
  empty: EmptyComponent,
  descriptions: DescriptionsComponent,

  // 数据组件
  statistic: StatisticComponent,
  'line-chart': SimpleChartComponent,
  'bar-chart': SimpleChartComponent,
  'pie-chart': SimpleChartComponent,

  // 流程组件
  flowNode: FlowNodeComponent,
  flowConnection: FlowConnectionComponent,
  flowStart: FlowStartComponent,
  flowEnd: FlowEndComponent,
  flowDecision: FlowDecisionComponent,
  flowProcess: FlowProcessComponent
}

// Get component type
const componentType = computed(() => {
  return componentMap[props.component.type] || null
})
</script>

<style scoped>
.component-wrapper {
  position: relative;
}

.component-wrapper.is-disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  pointer-events: none;
  z-index: 1;
}
</style>