<template>
  <div class="component-events-editor">
    <div v-if="!events.length" class="empty-events">
      <el-empty description="暂无事件配置">
        <el-button type="primary" @click="addEvent">添加事件</el-button>
      </el-empty>
    </div>
    <div v-else class="events-list">
      <div v-for="(event, index) in events" :key="index" class="event-item">
        <el-card>
          <template #header>
            <div class="event-header">
              <div class="event-type">
                <el-select v-model="event.type" placeholder="选择事件类型" @change="updateEvents">
                  <el-option
                    v-for="type in availableEventTypes"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  />
                </el-select>
              </div>
              <div class="event-actions">
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click="removeEvent(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>

          <div class="event-content">
            <div class="action-type">
              <el-form-item label="动作类型">
                <div class="action-type-select">
                  <el-select
                    v-model="event.action.type"
                    placeholder="选择动作类型"
                    @change="updateActionType(index)"
                  >
                    <el-option
                      v-for="type in actionTypes"
                      :key="type.value"
                      :label="type.label"
                      :value="type.value"
                    />
                  </el-select>
                  <el-tag :type="getActionTagType(event.action.type)" class="action-tag">
                    {{ getActionTypeLabel(event.action.type) }}
                  </el-tag>
                </div>
              </el-form-item>
            </div>

            <div class="action-config">
              <el-form>
                <!-- 导航动作配置 -->
                <template v-if="event.action.type === 'navigate'">
                  <el-form-item label="导航类型">
                    <el-radio-group v-model="event.action.navigationType" @change="updateEvents">
                      <el-radio label="page">页面</el-radio>
                      <el-radio label="url">URL</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item v-if="event.action.navigationType === 'page'" label="目标页面">
                    <el-select v-model="event.action.pageId" placeholder="选择页面" @change="updateEvents">
                      <el-option
                        v-for="page in availablePages"
                        :key="page.id"
                        :label="page.name"
                        :value="page.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item v-else label="URL">
                    <el-input v-model="event.action.url" placeholder="https://example.com" @change="updateEvents" />
                  </el-form-item>
                </template>

                <!-- 消息动作配置 -->
                <template v-else-if="event.action.type === 'message'">
                  <el-form-item label="消息类型">
                    <el-select v-model="event.action.messageType" placeholder="选择消息类型" @change="updateEvents">
                      <el-option label="成功" value="success" />
                      <el-option label="警告" value="warning" />
                      <el-option label="错误" value="error" />
                      <el-option label="信息" value="info" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="消息内容">
                    <el-input v-model="event.action.message" placeholder="消息内容" @change="updateEvents" />
                  </el-form-item>
                </template>

                <!-- 切换组件状态动作配置 -->
                <template v-else-if="event.action.type === 'toggleComponent'">
                  <el-form-item label="目标组件">
                    <el-select v-model="event.action.targetComponentId" placeholder="选择目标组件" @change="updateEvents">
                      <el-option
                        v-for="comp in availableComponents"
                        :key="comp.id"
                        :label="getComponentLabel(comp)"
                        :value="comp.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="操作">
                    <el-select v-model="event.action.operation" placeholder="选择操作" @change="updateEvents">
                      <el-option label="显示" value="show" />
                      <el-option label="隐藏" value="hide" />
                      <el-option label="切换显示/隐藏" value="toggle" />
                    </el-select>
                  </el-form-item>
                </template>
                
                <!-- 调用API动作配置 -->
                <template v-else-if="event.action.type === 'api'">
                  <el-form-item label="API路径">
                    <el-input v-model="event.action.apiUrl" placeholder="/api/example" @change="updateEvents" />
                  </el-form-item>
                  
                  <el-form-item label="请求方法">
                    <el-select v-model="event.action.apiMethod" placeholder="选择请求方法" @change="updateEvents">
                      <el-option label="GET" value="get" />
                      <el-option label="POST" value="post" />
                      <el-option label="PUT" value="put" />
                      <el-option label="DELETE" value="delete" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="请求参数">
                    <el-input
                      v-model="event.action.apiParams"
                      type="textarea"
                      placeholder='{"key": "value"}'
                      rows="4"
                      @change="updateEvents"
                    />
                  </el-form-item>
                  
                  <el-form-item label="成功后显示消息">
                    <el-switch v-model="event.action.showSuccessMessage" @change="updateEvents" />
                  </el-form-item>
                </template>
                
                <!-- 设置变量动作配置 -->
                <template v-else-if="event.action.type === 'setVariable'">
                  <el-form-item label="变量名称">
                    <el-input v-model="event.action.variableName" placeholder="variableName" @change="updateEvents" />
                  </el-form-item>
                  
                  <el-form-item label="变量值">
                    <el-input v-model="event.action.variableValue" placeholder="value" @change="updateEvents" />
                  </el-form-item>
                </template>
                
                <!-- 执行JavaScript动作配置 -->
                <template v-else-if="event.action.type === 'javascript'">
                  <el-form-item label="JavaScript代码">
                    <el-input
                      v-model="event.action.jsCode"
                      type="textarea"
                      placeholder="// 在这里编写JavaScript代码
// 例如: console.log('Hello world');"
                      rows="6"
                      @change="updateEvents"
                    />
                  </el-form-item>
                </template>
              </el-form>
            </div>
          </div>
        </el-card>
      </div>

      <div class="add-event-button">
        <el-button type="primary" @click="addEvent">添加事件</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  },
  availablePages: {
    type: Array,
    default: () => []
  },
  availableComponents: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update'])

// 事件列表
const events = ref([])

// 初始化事件
const initEvents = () => {
  if (props.component && props.component.events) {
    if (Array.isArray(props.component.events)) {
      events.value = JSON.parse(JSON.stringify(props.component.events))
    } else {
      try {
        events.value = JSON.parse(props.component.events)
      } catch (e) {
        events.value = []
      }
    }
  } else {
    events.value = []
  }
}

// 监听组件变化
watch(() => props.component, () => {
  initEvents()
}, { immediate: true })

// 可用的事件类型
const availableEventTypes = computed(() => {
  const baseTypes = [
    { label: '点击', value: 'click' },
    { label: '双击', value: 'dblclick' },
    { label: '鼠标进入', value: 'mouseenter' },
    { label: '鼠标离开', value: 'mouseleave' },
    { label: '按键按下', value: 'keydown' },
    { label: '按键释放', value: 'keyup' }
  ]

  // 根据组件类型添加特定事件
  if (props.component) {
    switch (props.component.type) {
      case 'input':
      case 'textarea':
        return [
          ...baseTypes,
          { label: '输入变化', value: 'input' },
          { label: '失去焦点', value: 'blur' },
          { label: '获得焦点', value: 'focus' },
          { label: '按下回车', value: 'enter' }
        ]
      case 'select':
      case 'radio':
      case 'checkbox':
      case 'switch':
      case 'slider':
      case 'rate':
      case 'date-picker':
      case 'time-picker':
      case 'color-picker':
        return [
          ...baseTypes,
          { label: '值变化', value: 'change' }
        ]
      case 'button':
        return [
          { label: '点击', value: 'click' },
          { label: '双击', value: 'dblclick' },
          { label: '鼠标进入', value: 'mouseenter' },
          { label: '鼠标离开', value: 'mouseleave' }
        ]
      case 'form':
        return [
          { label: '提交', value: 'submit' },
          { label: '重置', value: 'reset' }
        ]
      case 'image':
      case 'avatar':
        return [
          ...baseTypes,
          { label: '加载完成', value: 'load' },
          { label: '加载失败', value: 'error' }
        ]
      case 'carousel':
        return [
          ...baseTypes,
          { label: '切换', value: 'change' }
        ]
      default:
        return baseTypes
    }
  }

  return baseTypes
})

// 动作类型
const actionTypes = [
  { label: '导航', value: 'navigate' },
  { label: '显示消息', value: 'message' },
  { label: '切换组件状态', value: 'toggleComponent' },
  { label: '调用API', value: 'api' },
  { label: '设置变量', value: 'setVariable' },
  { label: '执行JavaScript', value: 'javascript' }
]

// 获取动作类型标签
const getActionTypeLabel = (type) => {
  const actionType = actionTypes.find(t => t.value === type)
  return actionType ? actionType.label : type
}

// 添加事件
const addEvent = () => {
  events.value.push({
    type: availableEventTypes.value[0].value,
    action: {
      type: 'navigate',
      navigationType: 'page',
      pageId: '',
      url: '',
      messageType: 'info',
      message: '',
      targetComponentId: '',
      operation: 'show',
      apiUrl: '',
      apiMethod: 'get',
      apiParams: '{}',
      showSuccessMessage: true,
      variableName: '',
      variableValue: '',
      jsCode: '// 在这里编写JavaScript代码'
    }
  })

  updateEvents()
}

// 更新动作类型
const updateActionType = (index) => {
  const event = events.value[index]

  // 重置动作配置
  if (event.action.type === 'navigate') {
    event.action.navigationType = 'page'
    event.action.pageId = ''
    event.action.url = ''
  } else if (event.action.type === 'message') {
    event.action.messageType = 'info'
    event.action.message = ''
  } else if (event.action.type === 'toggleComponent') {
    event.action.targetComponentId = ''
    event.action.operation = 'show'
  } else if (event.action.type === 'api') {
    event.action.apiUrl = ''
    event.action.apiMethod = 'get'
    event.action.apiParams = '{}'
    event.action.showSuccessMessage = true
  } else if (event.action.type === 'setVariable') {
    event.action.variableName = ''
    event.action.variableValue = ''
  } else if (event.action.type === 'javascript') {
    event.action.jsCode = '// 在这里编写JavaScript代码'
  }

  updateEvents()
}

// 移除事件
const removeEvent = (index) => {
  events.value.splice(index, 1)
  updateEvents()
}

// 更新事件
const updateEvents = () => {
  const updatedComponent = {
    ...props.component,
    events: events.value
  }
  emit('update', updatedComponent)
}

// 获取组件标签
const getComponentLabel = (component) => {
  return `${component.name} (${component.type})`
}

// 获取动作标签类型
const getActionTagType = (type) => {
  switch (type) {
    case 'navigate':
      return 'primary'
    case 'message':
      return 'success'
    case 'toggleComponent':
      return 'warning'
    case 'api':
      return 'danger'
    case 'setVariable':
      return 'info'
    case 'javascript':
      return 'danger'
    default:
      return 'info'
  }
}

// 初始化
initEvents()
</script>

<style scoped>
.component-events-editor {
  width: 100%;
}

.empty-events {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.event-item {
  border-radius: 4px;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.event-type {
  flex: 1;
}

.event-content {
  padding: 10px 0;
}

.action-type {
  margin-bottom: 20px;
}

.action-type-select {
  display: flex;
  align-items: center;
  gap: 10px;
}

.action-tag {
  margin-left: 10px;
}

.add-event-button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
