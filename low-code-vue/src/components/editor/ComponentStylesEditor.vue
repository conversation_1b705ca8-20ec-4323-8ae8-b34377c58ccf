<template>
  <div class="styles-editor">
    <el-form label-position="top">
      <!-- 字体样式 -->
      <el-collapse v-if="hasFontStyles" v-model="activeCollapse">
        <el-collapse-item title="字体" name="font">
          <div class="style-section">
            <el-form-item label="字体大小">
              <el-input-number v-model="fontSize" :min="10" :max="72" :step="1" @change="updateFontSize" size="small" />
            </el-form-item>

            <el-form-item label="字体颜色">
              <div class="color-picker-wrapper">
                <el-color-picker v-model="styles.color" @change="updateStyles" size="small" />
                <el-input v-model="styles.color" @change="updateStyles" size="small" placeholder="颜色值" />
              </div>
            </el-form-item>

            <el-form-item label="文本对齐">
              <el-radio-group v-model="styles.textAlign" @change="updateStyles" size="small">
                <el-radio-button label="left">左对齐</el-radio-button>
                <el-radio-button label="center">居中</el-radio-button>
                <el-radio-button label="right">右对齐</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="字体粗细">
              <el-select v-model="styles.fontWeight" @change="updateStyles" size="small" style="width: 100%">
                <el-option label="正常" value="normal" />
                <el-option label="粗体" value="bold" />
              </el-select>
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 尺寸和位置 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="尺寸和位置" name="size">
          <div class="style-section">
            <div class="style-row">
              <el-form-item label="宽度">
                <el-input v-model="styles.width" @change="updateStyles" size="small" placeholder="例如: 100px" />
              </el-form-item>

              <el-form-item label="高度">
                <el-input v-model="styles.height" @change="updateStyles" size="small" placeholder="例如: 50px" />
              </el-form-item>
            </div>

            <!-- 外边距控制 -->
            <el-form-item label="外边距">
              <div class="margin-padding-control">
                <div class="margin-padding-row">
                  <div class="spacer"></div>
                  <el-input v-model="styles.marginTop" @change="updateStyles" size="small" placeholder="上" />
                  <div class="spacer"></div>
                </div>
                <div class="margin-padding-row">
                  <el-input v-model="styles.marginLeft" @change="updateStyles" size="small" placeholder="左" />
                  <div class="margin-padding-center">
                    <span class="margin-label">外边距</span>
                  </div>
                  <el-input v-model="styles.marginRight" @change="updateStyles" size="small" placeholder="右" />
                </div>
                <div class="margin-padding-row">
                  <div class="spacer"></div>
                  <el-input v-model="styles.marginBottom" @change="updateStyles" size="small" placeholder="下" />
                  <div class="spacer"></div>
                </div>
              </div>
            </el-form-item>

            <!-- 内边距控制 -->
            <el-form-item label="内边距">
              <div class="margin-padding-control">
                <div class="margin-padding-row">
                  <div class="spacer"></div>
                  <el-input v-model="styles.paddingTop" @change="updateStyles" size="small" placeholder="上" />
                  <div class="spacer"></div>
                </div>
                <div class="margin-padding-row">
                  <el-input v-model="styles.paddingLeft" @change="updateStyles" size="small" placeholder="左" />
                  <div class="margin-padding-center">
                    <span class="padding-label">内边距</span>
                  </div>
                  <el-input v-model="styles.paddingRight" @change="updateStyles" size="small" placeholder="右" />
                </div>
                <div class="margin-padding-row">
                  <div class="spacer"></div>
                  <el-input v-model="styles.paddingBottom" @change="updateStyles" size="small" placeholder="下" />
                  <div class="spacer"></div>
                </div>
              </div>
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 背景 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="背景" name="background">
          <div class="style-section">
            <el-form-item label="背景颜色">
              <div class="color-picker-wrapper">
                <el-color-picker v-model="styles.backgroundColor" @change="updateStyles" show-alpha size="small" />
                <el-input v-model="styles.backgroundColor" @change="updateStyles" size="small" placeholder="颜色值" />
              </div>
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 边框 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="边框" name="border">
          <div class="style-section">
            <el-form-item label="边框样式">
              <el-select v-model="styles.borderStyle" @change="updateStyles" size="small" style="width: 100%">
                <el-option label="无" value="none" />
                <el-option label="实线" value="solid" />
                <el-option label="虚线" value="dashed" />
                <el-option label="点线" value="dotted" />
              </el-select>
            </el-form-item>

            <div class="style-row">
              <el-form-item label="边框宽度">
                <el-input-number v-model="borderWidth" :min="0" :max="10" :step="1" @change="updateBorderWidth" size="small" />
              </el-form-item>

              <el-form-item label="边框颜色">
                <div class="color-picker-wrapper">
                  <el-color-picker v-model="styles.borderColor" @change="updateStyles" size="small" />
                  <el-input v-model="styles.borderColor" @change="updateStyles" size="small" placeholder="颜色值" />
                </div>
              </el-form-item>
            </div>

            <el-form-item label="边框圆角">
              <div class="border-radius-control">
                <el-slider v-model="borderRadius" :min="0" :max="50" :step="1" @change="updateBorderRadius" />
                <el-input-number v-model="borderRadius" :min="0" :max="50" :step="1" @change="updateBorderRadius" size="small" controls-position="right" />
              </div>
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// 默认展开的折叠面板
const activeCollapse = ref(['font', 'size', 'background', 'border'])

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create a reactive copy of styles
const styles = ref({ ...props.component.styles })

// 初始化边距值
const initializeMarginPadding = () => {
  // 处理 margin
  if (styles.value.margin && !styles.value.marginTop && !styles.value.marginRight && !styles.value.marginBottom && !styles.value.marginLeft) {
    const margin = styles.value.margin.trim()

    // 如果是单一值，则四个方向都使用这个值
    if (!margin.includes(' ')) {
      styles.value.marginTop = margin
      styles.value.marginRight = margin
      styles.value.marginBottom = margin
      styles.value.marginLeft = margin
    } else {
      // 如果是多个值，则按照 CSS 规则分配
      const values = margin.split(' ').map(v => v.trim()).filter(v => v)

      if (values.length === 2) {
        // margin: 10px 20px; -> top/bottom = 10px, left/right = 20px
        styles.value.marginTop = values[0]
        styles.value.marginRight = values[1]
        styles.value.marginBottom = values[0]
        styles.value.marginLeft = values[1]
      } else if (values.length === 3) {
        // margin: 10px 20px 30px; -> top = 10px, left/right = 20px, bottom = 30px
        styles.value.marginTop = values[0]
        styles.value.marginRight = values[1]
        styles.value.marginLeft = values[1]
        styles.value.marginBottom = values[2]
      } else if (values.length === 4) {
        // margin: 10px 20px 30px 40px; -> top = 10px, right = 20px, bottom = 30px, left = 40px
        styles.value.marginTop = values[0]
        styles.value.marginRight = values[1]
        styles.value.marginBottom = values[2]
        styles.value.marginLeft = values[3]
      }
    }
  }

  // 处理 padding
  if (styles.value.padding && !styles.value.paddingTop && !styles.value.paddingRight && !styles.value.paddingBottom && !styles.value.paddingLeft) {
    const padding = styles.value.padding.trim()

    // 如果是单一值，则四个方向都使用这个值
    if (!padding.includes(' ')) {
      styles.value.paddingTop = padding
      styles.value.paddingRight = padding
      styles.value.paddingBottom = padding
      styles.value.paddingLeft = padding
    } else {
      // 如果是多个值，则按照 CSS 规则分配
      const values = padding.split(' ').map(v => v.trim()).filter(v => v)

      if (values.length === 2) {
        // padding: 10px 20px; -> top/bottom = 10px, left/right = 20px
        styles.value.paddingTop = values[0]
        styles.value.paddingRight = values[1]
        styles.value.paddingBottom = values[0]
        styles.value.paddingLeft = values[1]
      } else if (values.length === 3) {
        // padding: 10px 20px 30px; -> top = 10px, left/right = 20px, bottom = 30px
        styles.value.paddingTop = values[0]
        styles.value.paddingRight = values[1]
        styles.value.paddingLeft = values[1]
        styles.value.paddingBottom = values[2]
      } else if (values.length === 4) {
        // padding: 10px 20px 30px 40px; -> top = 10px, right = 20px, bottom = 30px, left = 40px
        styles.value.paddingTop = values[0]
        styles.value.paddingRight = values[1]
        styles.value.paddingBottom = values[2]
        styles.value.paddingLeft = values[3]
      }
    }
  }
}

// 初始化边距值
initializeMarginPadding()

// Computed properties for numeric values
const fontSize = computed({
  get: () => {
    const size = styles.value.fontSize
    return size ? parseInt(size) : 16
  },
  set: (value) => {
    styles.value.fontSize = `${value}px`
  }
})

const borderWidth = computed({
  get: () => {
    const width = styles.value.borderWidth
    return width ? parseInt(width) : 1
  },
  set: (value) => {
    styles.value.borderWidth = `${value}px`
  }
})

const borderRadius = computed({
  get: () => {
    const radius = styles.value.borderRadius
    return radius ? parseInt(radius) : 0
  },
  set: (value) => {
    styles.value.borderRadius = `${value}px`
  }
})

// Check if component has font styles
const hasFontStyles = computed(() => {
  return ['text', 'heading', 'paragraph', 'button', 'link'].includes(props.component.type)
})

// 合并边距值
const mergeMarginPadding = () => {
  // 处理 margin
  if (styles.value.marginTop || styles.value.marginRight || styles.value.marginBottom || styles.value.marginLeft) {
    // 检查四个方向的值是否相同
    const top = styles.value.marginTop || '0';
    const right = styles.value.marginRight || '0';
    const bottom = styles.value.marginBottom || '0';
    const left = styles.value.marginLeft || '0';

    if (top === right && right === bottom && bottom === left) {
      // 四个方向相同，使用简写
      styles.value.margin = top;
    } else if (top === bottom && right === left) {
      // 上下相同，左右相同，使用两个值的简写
      styles.value.margin = `${top} ${right}`;
    } else if (right === left) {
      // 左右相同，使用三个值的简写
      styles.value.margin = `${top} ${right} ${bottom}`;
    } else {
      // 四个方向都不同，使用完整写法
      styles.value.margin = `${top} ${right} ${bottom} ${left}`;
    }
  }

  // 处理 padding
  if (styles.value.paddingTop || styles.value.paddingRight || styles.value.paddingBottom || styles.value.paddingLeft) {
    // 检查四个方向的值是否相同
    const top = styles.value.paddingTop || '0';
    const right = styles.value.paddingRight || '0';
    const bottom = styles.value.paddingBottom || '0';
    const left = styles.value.paddingLeft || '0';

    if (top === right && right === bottom && bottom === left) {
      // 四个方向相同，使用简写
      styles.value.padding = top;
    } else if (top === bottom && right === left) {
      // 上下相同，左右相同，使用两个值的简写
      styles.value.padding = `${top} ${right}`;
    } else if (right === left) {
      // 左右相同，使用三个值的简写
      styles.value.padding = `${top} ${right} ${bottom}`;
    } else {
      // 四个方向都不同，使用完整写法
      styles.value.padding = `${top} ${right} ${bottom} ${left}`;
    }
  }
}

// Update styles
const updateStyles = () => {
  console.log('ComponentStylesEditor updating styles for:', props.component.id, props.component.type)

  // 合并边距值
  mergeMarginPadding()

  // 创建一个新的样式对象，删除单独的边距值
  const finalStyles = { ...styles.value }

  // 删除单独的边距值，因为它们已经合并到 margin 和 padding 中
  delete finalStyles.marginTop
  delete finalStyles.marginRight
  delete finalStyles.marginBottom
  delete finalStyles.marginLeft
  delete finalStyles.paddingTop
  delete finalStyles.paddingRight
  delete finalStyles.paddingBottom
  delete finalStyles.paddingLeft

  // Create a completely new component object to ensure reactivity
  const updatedComponent = JSON.parse(JSON.stringify({
    ...props.component,
    styles: finalStyles
  }))

  // Emit the update event
  emit('update', updatedComponent)
}

// Update specific styles
const updateFontSize = (value) => {
  styles.value.fontSize = `${value}px`
  updateStyles()
}

const updateBorderWidth = (value) => {
  styles.value.borderWidth = `${value}px`
  updateStyles()
}

const updateBorderRadius = (value) => {
  styles.value.borderRadius = `${value}px`
  updateStyles()
}

// Watch for changes in component props
watch(() => props.component, (newComponent) => {
  styles.value = { ...newComponent.styles }
  // 在组件属性变化时重新初始化边距值
  initializeMarginPadding()
}, { deep: true })
</script>

<style scoped>
.styles-editor {
  padding: 10px 0;
}

.style-section {
  padding: 10px 0;
}

.style-row {
  display: flex;
  gap: 15px;
}

.style-row .el-form-item {
  flex: 1;
  margin-bottom: 20px;
}

:deep(.el-collapse-item__header) {
  font-weight: bold;
  color: #303133;
  font-size: 15px;
  padding: 12px 0;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 20px;
}

:deep(.el-form-item__label) {
  padding-bottom: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-radio-group) {
  width: 100%;
  display: flex;
}

:deep(.el-radio-button) {
  flex: 1;
}

:deep(.el-slider) {
  margin-top: 10px;
}

.border-radius-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.border-radius-control :deep(.el-slider) {
  flex: 1;
  margin: 0;
}

.border-radius-control :deep(.el-input-number) {
  width: 100px;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-picker-wrapper :deep(.el-color-picker) {
  margin-right: 0;
}

.color-picker-wrapper :deep(.el-input) {
  flex: 1;
}

/* 边距控制器样式 */
.margin-padding-control {
  display: flex;
  flex-direction: column;
  gap: 5px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
}

.margin-padding-row {
  display: flex;
  gap: 5px;
}

.margin-padding-row .el-input {
  width: 80px;
}

.margin-padding-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 32px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.margin-label {
  color: #409eff;
  font-size: 12px;
}

.padding-label {
  color: #67c23a;
  font-size: 12px;
}

.spacer {
  flex: 1;
}
</style>
