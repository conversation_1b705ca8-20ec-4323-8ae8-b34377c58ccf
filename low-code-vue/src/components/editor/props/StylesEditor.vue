<template>
  <div class="styles-editor">
    <el-divider content-position="center">样式设置</el-divider>

    <el-form label-position="top">
      <!-- 尺寸设置 -->
      <el-collapse>
        <el-collapse-item title="尺寸设置" name="size">
          <el-form-item label="宽度">
            <el-input v-model="styles.width" @change="updateStyles" />
          </el-form-item>

          <el-form-item label="高度">
            <el-input v-model="styles.height" @change="updateStyles" />
          </el-form-item>

          <el-form-item label="最小宽度">
            <el-input v-model="styles.minWidth" @change="updateStyles" />
          </el-form-item>

          <el-form-item label="最小高度">
            <el-input v-model="styles.minHeight" @change="updateStyles" />
          </el-form-item>

          <el-form-item label="最大宽度">
            <el-input v-model="styles.maxWidth" @change="updateStyles" />
          </el-form-item>

          <el-form-item label="最大高度">
            <el-input v-model="styles.maxHeight" @change="updateStyles" />
          </el-form-item>
        </el-collapse-item>
      </el-collapse>

      <!-- 边距设置 -->
      <el-collapse>
        <el-collapse-item title="边距设置" name="margin">
          <el-form-item label="外边距">
            <el-input v-model="styles.margin" @change="updateStyles" />
            <div class="tip-text">可设置为 "10px" 或 "10px 20px" 或 "10px 20px 10px 20px"</div>
          </el-form-item>

          <el-form-item label="内边距">
            <el-input v-model="styles.padding" @change="updateStyles" />
            <div class="tip-text">可设置为 "10px" 或 "10px 20px" 或 "10px 20px 10px 20px"</div>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>

      <!-- 边框设置 -->
      <el-collapse>
        <el-collapse-item title="边框设置" name="border">
          <el-form-item label="边框宽度">
            <el-input v-model="styles.borderWidth" @change="updateStyles" />
            <div class="tip-text">例如: "1px" 或 "2px"</div>
          </el-form-item>

          <el-form-item label="边框样式">
            <el-select v-model="styles.borderStyle" @change="updateStyles" style="width: 100%">
              <el-option label="无" value="none" />
              <el-option label="实线" value="solid" />
              <el-option label="虚线" value="dashed" />
              <el-option label="点线" value="dotted" />
              <el-option label="双线" value="double" />
            </el-select>
          </el-form-item>

          <el-form-item label="边框颜色">
            <el-color-picker v-model="styles.borderColor" @change="updateStyles" show-alpha />
            <el-input v-model="styles.borderColor" @change="updateStyles" class="color-input" />
          </el-form-item>

          <el-form-item label="边框">
            <el-input v-model="styles.border" @change="updateStyles" />
            <div class="tip-text">例如: "1px solid #ccc"</div>
          </el-form-item>

          <el-form-item label="边框圆角">
            <el-input v-model="styles.borderRadius" @change="updateStyles" />
            <div class="tip-text">例如: "4px" 或 "50%"</div>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>

      <!-- 颜色设置 -->
      <el-collapse>
        <el-collapse-item title="颜色设置" name="color">
          <el-form-item label="背景颜色">
            <el-color-picker v-model="styles.backgroundColor" @change="updateStyles" show-alpha />
            <el-input v-model="styles.backgroundColor" @change="updateStyles" class="color-input" />
          </el-form-item>

          <el-form-item label="文字颜色">
            <el-color-picker v-model="styles.color" @change="updateStyles" show-alpha />
            <el-input v-model="styles.color" @change="updateStyles" class="color-input" />
          </el-form-item>
        </el-collapse-item>
      </el-collapse>

      <!-- 阴影设置 -->
      <el-collapse>
        <el-collapse-item title="阴影设置" name="shadow">
          <el-form-item label="阴影">
            <el-input v-model="styles.boxShadow" @change="updateStyles" />
            <div class="tip-text">例如: "0 2px 12px 0 rgba(0, 0, 0, 0.1)"</div>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 样式对象
const styles = reactive({
  // 尺寸
  width: '',
  height: '',
  minWidth: '',
  minHeight: '',
  maxWidth: '',
  maxHeight: '',

  // 边距
  margin: '',
  padding: '',

  // 边框
  border: '',
  borderWidth: '',
  borderStyle: '',
  borderColor: '',
  borderRadius: '',

  // 颜色
  backgroundColor: '',
  color: '',

  // 阴影
  boxShadow: ''
})

// 初始化样式
onMounted(() => {
  Object.assign(styles, props.modelValue)
})

// 监听外部样式变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(styles, newValue)
}, { deep: true })

// 更新样式
const updateStyles = () => {
  // 创建一个新对象，只包含有值的属性
  const updatedStyles = {}

  for (const [key, value] of Object.entries(styles)) {
    if (value !== undefined && value !== null && value !== '') {
      updatedStyles[key] = value
    }
  }

  emit('update:modelValue', updatedStyles)
}
</script>

<style scoped>
.styles-editor {
  margin-top: 20px;
}

.tip-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.color-input {
  margin-top: 8px;
}

:deep(.el-collapse-item__header) {
  font-weight: bold;
}
</style>
