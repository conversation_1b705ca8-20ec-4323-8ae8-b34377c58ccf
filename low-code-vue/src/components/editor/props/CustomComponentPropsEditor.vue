<template>
  <div class="custom-component-props-editor">
    <common-props-editor :component="component" @update="updateComponent">
      <el-form label-position="top">
        <el-form-item label="组件名称">
          <el-input v-model="componentInfo.name" disabled />
        </el-form-item>
        
        <el-form-item label="组件类型">
          <el-input v-model="componentInfo.type" disabled />
        </el-form-item>
        
        <el-form-item label="组件分类">
          <el-input v-model="componentInfo.category" disabled />
        </el-form-item>
        
        <el-divider content-position="center">元素列表</el-divider>
        
        <div class="elements-list">
          <div 
            v-for="element in elements" 
            :key="element.id"
            class="element-item"
            :class="{ 'selected': selectedElement && selectedElement.id === element.id }"
            @click="selectElement(element)"
          >
            <div class="element-preview" :class="`element-${element.type}`"></div>
            <div class="element-info">
              <div class="element-name">{{ element.name || element.type }}</div>
              <div class="element-type">{{ element.tag || element.type }}</div>
            </div>
          </div>
        </div>
        
        <el-divider v-if="selectedElement" content-position="center">元素属性</el-divider>
        
        <template v-if="selectedElement">
          <el-form-item label="元素名称">
            <el-input v-model="selectedElement.name" @change="updateElement" />
          </el-form-item>
          
          <el-form-item label="元素内容">
            <el-input 
              v-model="selectedElement.content" 
              type="textarea" 
              :rows="3"
              @change="updateElement" 
            />
          </el-form-item>
          
          <el-form-item label="元素样式">
            <styles-editor v-model="selectedElement.styles" @update:modelValue="updateElementStyles" />
          </el-form-item>
        </template>
        
        <el-divider content-position="center">样式设置</el-divider>
        
        <styles-editor v-model="componentStyles" @update:modelValue="updateStyles" />
      </el-form>
    </common-props-editor>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import CommonPropsEditor from './CommonPropsEditor.vue'
import StylesEditor from './StylesEditor.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 组件信息
const componentInfo = reactive({
  name: '',
  type: '',
  category: ''
})

// 组件样式
const componentStyles = reactive({})

// 元素列表
const elements = ref([])

// 当前选中的元素
const selectedElement = ref(null)

// 初始化组件信息
onMounted(() => {
  // 初始化组件信息
  componentInfo.name = props.component.name || ''
  componentInfo.type = props.component.type || ''
  componentInfo.category = props.component.category || ''
  
  // 初始化组件样式
  if (props.component.styles) {
    Object.assign(componentStyles, props.component.styles)
  }
  
  // 初始化元素列表
  if (props.component.elements) {
    elements.value = [...props.component.elements]
  }
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  // 更新组件信息
  componentInfo.name = newComponent.name || ''
  componentInfo.type = newComponent.type || ''
  componentInfo.category = newComponent.category || ''
  
  // 更新组件样式
  Object.assign(componentStyles, newComponent.styles || {})
  
  // 更新元素列表
  if (newComponent.elements) {
    elements.value = [...newComponent.elements]
  }
}, { deep: true })

// 选择元素
const selectElement = (element) => {
  selectedElement.value = element
}

// 更新元素
const updateElement = () => {
  if (!selectedElement.value) return
  
  const index = elements.value.findIndex(el => el.id === selectedElement.value.id)
  if (index !== -1) {
    elements.value[index] = { ...selectedElement.value }
  }
  
  updateComponent()
}

// 更新元素样式
const updateElementStyles = (styles) => {
  if (!selectedElement.value) return
  
  selectedElement.value.styles = styles
  updateElement()
}

// 更新组件样式
const updateStyles = (styles) => {
  Object.assign(componentStyles, styles)
  
  const updatedComponent = {
    ...props.component,
    styles: { ...componentStyles }
  }
  
  emit('update', updatedComponent)
}

// 更新组件
const updateComponent = () => {
  const updatedComponent = {
    ...props.component,
    styles: { ...componentStyles },
    elements: [...elements.value]
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.custom-component-props-editor {
  padding: 10px 0;
}

.elements-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 15px;
}

.element-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s;
}

.element-item:last-child {
  border-bottom: none;
}

.element-item:hover {
  background-color: #f5f7fa;
}

.element-item.selected {
  background-color: #e6f7ff;
}

.element-preview {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.element-info {
  flex: 1;
}

.element-name {
  font-size: 14px;
  color: #303133;
}

.element-type {
  font-size: 12px;
  color: #909399;
}

.element-rectangle {
  background-color: #e6f7ff;
}

.element-circle {
  border-radius: 50%;
  background-color: #e6f7ff;
}

.element-triangle {
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-bottom: 24px solid #e6f7ff;
  background-color: transparent !important;
}

.element-text {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 10px;
}

.element-button {
  background-color: #409eff;
  color: white;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
}

.element-input {
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
}
</style>
