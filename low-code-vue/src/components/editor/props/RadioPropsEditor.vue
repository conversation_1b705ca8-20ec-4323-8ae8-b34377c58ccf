<template>
  <div class="radio-props-editor">
    <el-form label-position="top">
      <el-form-item label="选项">
        <div v-for="(option, index) in options" :key="index" class="option-item">
          <el-input v-model="option.label" placeholder="选项文本" @change="updateOptions">
            <template #prepend>文本</template>
          </el-input>
          <el-input v-model="option.value" placeholder="选项值" @change="updateOptions">
            <template #prepend>值</template>
          </el-input>
          <el-button 
            type="danger" 
            icon="Delete" 
            circle 
            @click="removeOption(index)"
            :disabled="options.length <= 1"
          ></el-button>
        </div>
        <el-button type="primary" icon="Plus" @click="addOption" style="margin-top: 10px;">添加选项</el-button>
      </el-form-item>
      
      <el-form-item label="默认值">
        <el-select v-model="defaultValue" placeholder="请选择默认值" @change="updateProps">
          <el-option
            v-for="option in options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="禁用">
        <el-switch v-model="componentProps.disabled" @change="updateProps" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Delete, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = ref({
  options: [],
  disabled: false
})

// Options for easier manipulation
const options = ref([])
const defaultValue = ref('')

// Initialize component props
onMounted(() => {
  componentProps.value = { ...props.component.props }
  options.value = [...(componentProps.value.options || [])].map(option => ({
    value: option.value,
    label: option.label
  }))
  
  // Ensure we have at least one option
  if (options.value.length === 0) {
    const defaultOption = {
      value: '1',
      label: '选项1'
    }
    options.value.push(defaultOption)
    defaultValue.value = defaultOption.value
  } else {
    defaultValue.value = options.value[0].value
  }
})

// Add option
const addOption = () => {
  const newIndex = options.value.length + 1
  const newOption = {
    value: `${newIndex}`,
    label: `选项${newIndex}`
  }
  options.value.push(newOption)
  updateOptions()
}

// Remove option
const removeOption = (index) => {
  if (options.value.length > 1) {
    options.value.splice(index, 1)
    if (defaultValue.value === options.value[index]?.value) {
      defaultValue.value = options.value[0]?.value
    }
    updateOptions()
  }
}

// Update options
const updateOptions = () => {
  componentProps.value.options = options.value.map(option => ({
    value: option.value,
    label: option.label
  }))
  updateProps()
}

// Update props
const updateProps = () => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...componentProps.value,
      modelValue: defaultValue.value
    }
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.radio-props-editor {
  padding: 10px 0;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.option-item .el-input {
  flex: 1;
}
</style>
