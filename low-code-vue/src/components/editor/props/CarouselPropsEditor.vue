<template>
  <div class="carousel-props-editor">
    <el-form label-position="top">
      <el-form-item label="轮播图高度">
        <el-input-number
          v-model="componentProps.height"
          :min="100"
          :max="800"
          :step="10"
          @change="updateProps"
        />
      </el-form-item>

      <el-form-item label="自动播放">
        <el-switch v-model="componentProps.autoplay" @change="updateProps" />
      </el-form-item>

      <el-form-item label="播放间隔 (毫秒)" v-if="componentProps.autoplay">
        <el-input-number
          v-model="componentProps.interval"
          :min="1000"
          :max="10000"
          :step="500"
          @change="updateProps"
        />
      </el-form-item>

      <el-form-item label="轮播图片">
        <div v-for="(item, index) in items" :key="index" class="carousel-item">
          <div class="carousel-item-header">
            <h4>图片 {{ index + 1 }}</h4>
            <el-button
              type="danger"
              icon="Delete"
              circle
              @click="removeItem(index)"
              :disabled="items.length <= 1"
            ></el-button>
          </div>

          <div class="carousel-item-content">
            <el-form-item label="图片URL">
              <div class="image-input">
                <el-input v-model="item.url" placeholder="输入图片URL" @change="updateItems" />
                <image-uploader v-model="item.url" @update:modelValue="handleImageUpload($event, index)" />
              </div>
            </el-form-item>

            <el-form-item label="图片预览">
              <div class="image-preview">
                <img v-if="item.url" :src="item.url" alt="轮播图片预览" />
                <div v-else class="no-image">无图片预览</div>
              </div>
            </el-form-item>
          </div>
        </div>

        <div style="display: flex; gap: 10px; margin-top: 10px;">
          <el-button type="primary" icon="Plus" @click="addItem">添加图片</el-button>
          <el-button type="success" @click="forceUpdate">刷新轮播图</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ImageUploader from '../ImageUploader.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = ref({
  height: 200,
  autoplay: true,
  interval: 3000,
  items: []
})

// Items for easier manipulation
const items = ref([])

// Initialize component props
onMounted(() => {
  componentProps.value = { ...props.component.props }
  items.value = [...(componentProps.value.items || [])]

  // Ensure we have at least one item
  if (items.value.length === 0) {
    items.value.push({
      url: 'https://via.placeholder.com/800x200/409eff/ffffff?text=Slide+1'
    })
  }
})

// Add item
const addItem = () => {
  const newIndex = items.value.length + 1
  items.value.push({
    url: `https://via.placeholder.com/800x200/409eff/ffffff?text=Slide+${newIndex}`
  })
  updateItems()
}

// Remove item
const removeItem = (index) => {
  if (items.value.length > 1) {
    items.value.splice(index, 1)
    updateItems()
  }
}

// Update items
const updateItems = () => {
  componentProps.value.items = [...items.value]
  updateProps()
}

// Handle image upload
const handleImageUpload = (imageUrl, index) => {
  if (items.value[index]) {
    // Create a new object to ensure reactivity
    const newItems = [...items.value]
    newItems[index] = { ...newItems[index], url: imageUrl }
    items.value = newItems

    // Force immediate update
    componentProps.value.items = [...newItems]
    updateProps()

    // Log for debugging
    console.log('Image uploaded:', imageUrl)
    console.log('Updated items:', JSON.stringify(componentProps.value.items))
  }
}

// Update props
const updateProps = () => {
  // Create a completely new object to ensure reactivity
  const updatedProps = {
    height: componentProps.value.height,
    autoplay: componentProps.value.autoplay,
    interval: componentProps.value.interval,
    items: componentProps.value.items.map(item => ({
      url: item.url
    }))
  }

  // Log for debugging
  console.log('Emitting update with props:', JSON.stringify(updatedProps))

  emit('update', updatedProps)
}

// Force update - this is a debug function to help diagnose issues
const forceUpdate = () => {
  // Create a completely new items array with current timestamp to force reactivity
  const timestamp = Date.now()
  const newItems = items.value.map((item, index) => ({
    url: item.url + '?t=' + timestamp
  }))

  // Update the items
  items.value = newItems
  componentProps.value.items = newItems

  // Force update
  updateProps()

  // Show success message
  ElMessage.success('轮播图已刷新')
}
</script>

<style scoped>
.carousel-props-editor {
  padding: 10px 0;
}

.carousel-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 15px;
  background-color: #f5f7fa;
}

.carousel-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #ebeef5;
}

.carousel-item-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.carousel-item-content {
  padding: 15px;
}

.image-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.image-input .el-input {
  flex: 1;
}

.image-preview {
  width: 100%;
  height: 100px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.no-image {
  color: #909399;
  font-size: 14px;
}
</style>
