<template>
  <div class="flow-process-props-editor">
    <el-form label-position="top">
      <el-form-item label="标题">
        <el-input v-model="componentProps.title" placeholder="输入流程节点标题" @input="updateProps" />
      </el-form-item>
      
      <el-form-item label="描述">
        <el-input 
          type="textarea" 
          v-model="componentProps.description" 
          placeholder="输入流程节点描述" 
          :rows="3" 
          @input="updateProps" 
        />
      </el-form-item>
      
      <el-form-item label="图标">
        <el-select v-model="componentProps.icon" placeholder="选择图标" style="width: 100%" @change="updateProps">
          <el-option v-for="icon in iconList" :key="icon" :label="icon" :value="icon">
            <div style="display: flex; align-items: center;">
              <el-icon><component :is="icon" /></el-icon>
              <span style="margin-left: 8px;">{{ icon }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="处理时间">
        <el-input-number v-model="componentProps.processingTime" :min="0" style="width: 100%" @change="updateProps" />
      </el-form-item>
      
      <el-form-item label="负责人">
        <el-input v-model="componentProps.assignee" placeholder="输入负责人" @input="updateProps" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Get all available icons
const iconList = ref(Object.keys(ElementPlusIconsVue))

// Component props
const componentProps = ref({
  title: '',
  description: '',
  icon: '',
  processingTime: 0,
  assignee: ''
})

// Initialize component props
onMounted(() => {
  console.log('FlowProcessPropsEditor mounted with component:', props.component.id)
  initializeProps()
})

// Watch for changes in component
watch(() => props.component, () => {
  console.log('FlowProcessPropsEditor detected component change')
  initializeProps()
}, { deep: true })

// Initialize props from component
const initializeProps = () => {
  componentProps.value = {
    title: props.component.props?.title || '流程',
    description: props.component.props?.description || '',
    icon: props.component.props?.icon || 'Operation',
    processingTime: props.component.props?.processingTime || 0,
    assignee: props.component.props?.assignee || ''
  }
}

// Update props
const updateProps = () => {
  // Create a completely new component object to ensure reactivity
  const updatedComponent = {
    ...props.component,
    props: {
      ...componentProps.value
    }
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.flow-process-props-editor {
  padding: 10px 0;
}
</style>
