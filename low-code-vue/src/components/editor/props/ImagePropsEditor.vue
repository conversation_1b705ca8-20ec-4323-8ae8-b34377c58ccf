<template>
  <div class="image-props-editor">
    <el-form label-position="top">
      <el-form-item label="图片">
        <image-uploader v-model="componentProps.src" @update:modelValue="updateProps" />
      </el-form-item>

      <el-form-item label="图片URL">
        <el-input
          v-model="componentProps.src"
          placeholder="https://example.com/image.jpg"
          @change="updateProps"
        />
      </el-form-item>

      <el-form-item label="替代文本">
        <el-input
          v-model="componentProps.alt"
          placeholder="图片描述"
          @change="updateProps"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import ImageUploader from '../ImageUploader.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create a reactive copy of props
const componentProps = ref({ ...props.component.props })

// Update props
const updateProps = () => {
  emit('update', { ...componentProps.value })
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  componentProps.value = { ...newProps }
}, { deep: true })
</script>
