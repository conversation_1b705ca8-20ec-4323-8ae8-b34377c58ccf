<template>
  <div class="tab-container-props-editor">
    <el-form label-position="top">
      <el-form-item label="当前激活的标签页">
        <el-select v-model="activeName" @change="updateActiveName">
          <el-option 
            v-for="tab in component.props.tabs" 
            :key="tab.name" 
            :label="tab.title" 
            :value="tab.name" 
          />
        </el-select>
      </el-form-item>

      <el-divider>标签页管理</el-divider>

      <div v-for="(tab, index) in tabs" :key="index" class="tab-item">
        <el-row :gutter="10">
          <el-col :span="10">
            <el-form-item label="标签名称">
              <el-input v-model="tab.title" @change="updateTabs" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="标签标识">
              <el-input v-model="tab.name" @change="updateTabs" />
            </el-form-item>
          </el-col>
          <el-col :span="4" class="tab-actions">
            <el-button 
              type="danger" 
              icon="Delete" 
              circle 
              size="small" 
              @click="removeTab(index)"
              :disabled="tabs.length <= 1"
            ></el-button>
          </el-col>
        </el-row>
      </div>

      <el-button type="primary" icon="Plus" @click="addTab">添加标签页</el-button>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create reactive properties
const tabs = ref([...props.component.props.tabs] || [])
const activeName = ref(props.component.props.activeName || '')

// Update active name
const updateActiveName = (value) => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      activeName: value
    }
  }

  emit('update', updatedComponent)
}

// Update tabs
const updateTabs = () => {
  // Create a deep copy of tabs to ensure reactivity
  const updatedTabs = JSON.parse(JSON.stringify(tabs.value))
  
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      tabs: updatedTabs
    }
  }

  emit('update', updatedComponent)
}

// Add a new tab
const addTab = () => {
  // Generate a unique name
  const newName = 'tab' + (tabs.value.length + 1)
  
  // Add the new tab
  tabs.value.push({
    title: '新标签页',
    name: newName,
    children: []
  })
  
  // Update the component
  updateTabs()
}

// Remove a tab
const removeTab = (index) => {
  // Don't remove the last tab
  if (tabs.value.length <= 1) {
    return
  }
  
  // Get the tab being removed
  const removedTab = tabs.value[index]
  
  // Remove the tab
  tabs.value.splice(index, 1)
  
  // If the active tab is being removed, set a new active tab
  if (activeName.value === removedTab.name) {
    activeName.value = tabs.value[0].name
    updateActiveName(activeName.value)
  }
  
  // Update the component
  updateTabs()
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  tabs.value = [...newProps.tabs] || []
  activeName.value = newProps.activeName || ''
}, { deep: true })
</script>

<style scoped>
.tab-container-props-editor {
  padding: 10px;
}

.tab-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.tab-actions {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 100%;
  padding-bottom: 10px;
}
</style>
