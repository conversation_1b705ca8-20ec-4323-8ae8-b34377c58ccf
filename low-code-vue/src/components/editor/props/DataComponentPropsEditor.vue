<template>
  <div class="data-component-props-editor">
    <common-props-editor :component="component" @update="updateComponent">
      <el-form label-position="top">
        <el-form-item label="标题">
          <el-input v-model="componentProps.title" @change="updateProps" />
        </el-form-item>
        
        <template v-if="component.type === 'statistic'">
          <el-form-item label="数值">
            <el-input-number v-model="componentProps.value" :precision="componentProps.precision" @change="updateProps" style="width: 100%" />
          </el-form-item>
          
          <el-form-item label="精度">
            <el-input-number v-model="componentProps.precision" :min="0" :max="10" @change="updateProps" style="width: 100%" />
          </el-form-item>
          
          <el-form-item label="前缀">
            <el-input v-model="componentProps.prefix" @change="updateProps" />
          </el-form-item>
          
          <el-form-item label="后缀">
            <el-input v-model="componentProps.suffix" @change="updateProps" />
          </el-form-item>
          
          <el-form-item label="数值颜色">
            <el-color-picker v-model="valueStyle.color" @change="updateValueStyle" />
          </el-form-item>
        </template>
        
        <template v-else-if="['line-chart', 'bar-chart', 'pie-chart'].includes(component.type)">
          <el-alert
            title="图表配置"
            type="info"
            description="图表将在预览模式下显示，您可以在此配置图表的基本属性。"
            :closable="false"
            show-icon
          />
        </template>
        
        <el-divider content-position="center">样式设置</el-divider>
        
        <el-form-item label="宽度">
          <el-input v-model="componentStyles.width" @change="updateStyles" />
        </el-form-item>
        
        <el-form-item label="高度">
          <el-input v-model="componentStyles.height" @change="updateStyles" />
        </el-form-item>
        
        <el-form-item label="外边距">
          <el-input v-model="componentStyles.margin" @change="updateStyles" />
        </el-form-item>
        
        <el-form-item label="内边距">
          <el-input v-model="componentStyles.padding" @change="updateStyles" />
        </el-form-item>
      </el-form>
    </common-props-editor>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import CommonPropsEditor from './CommonPropsEditor.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = reactive({
  title: '',
  value: 0,
  precision: 0,
  prefix: '',
  suffix: ''
})

// Component styles
const componentStyles = reactive({
  width: '',
  height: '',
  margin: '',
  padding: ''
})

// Value style for statistic component
const valueStyle = reactive({
  color: ''
})

// Initialize component props and styles
onMounted(() => {
  // Initialize props
  if (props.component.props) {
    Object.assign(componentProps, props.component.props)
    
    // Initialize value style
    if (props.component.props.valueStyle && props.component.props.valueStyle.color) {
      valueStyle.color = props.component.props.valueStyle.color
    }
  }
  
  // Initialize styles
  if (props.component.styles) {
    Object.assign(componentStyles, props.component.styles)
  }
})

// Watch for component changes
watch(() => props.component, (newComponent) => {
  // Update props
  if (newComponent.props) {
    Object.assign(componentProps, newComponent.props)
    
    // Update value style
    if (newComponent.props.valueStyle && newComponent.props.valueStyle.color) {
      valueStyle.color = newComponent.props.valueStyle.color
    }
  }
  
  // Update styles
  if (newComponent.styles) {
    Object.assign(componentStyles, newComponent.styles)
  }
}, { deep: true })

// Update component props
const updateProps = () => {
  const updatedProps = { ...componentProps }
  
  // Add value style for statistic component
  if (props.component.type === 'statistic') {
    updatedProps.valueStyle = { ...valueStyle }
  }
  
  const updatedComponent = {
    ...props.component,
    props: updatedProps
  }
  
  emit('update', updatedComponent)
}

// Update value style
const updateValueStyle = () => {
  componentProps.valueStyle = { ...valueStyle }
  updateProps()
}

// Update component styles
const updateStyles = () => {
  const updatedComponent = {
    ...props.component,
    styles: { ...componentStyles }
  }
  
  emit('update', updatedComponent)
}

// Update component
const updateComponent = (updatedComponent) => {
  emit('update', updatedComponent)
}
</script>

<style scoped>
.data-component-props-editor {
  padding: 0 10px;
}
</style>
