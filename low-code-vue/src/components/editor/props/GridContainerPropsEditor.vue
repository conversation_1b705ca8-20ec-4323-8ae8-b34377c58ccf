<template>
  <div class="grid-container-props-editor">
    <el-form label-position="top">
      <el-form-item label="列数">
        <el-slider v-model="cols" :min="1" :max="6" :step="1" @change="updateCols" />
      </el-form-item>

      <el-form-item label="间距">
        <el-slider v-model="gutter" :min="0" :max="40" :step="5" @change="updateGutter" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create reactive properties
const cols = ref(props.component.props.cols || 3)
const gutter = ref(props.component.props.gutter || 20)

// Update cols
const updateCols = (value) => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      cols: value
    }
  }

  emit('update', updatedComponent)
}

// Update gutter
const updateGutter = (value) => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      gutter: value
    }
  }

  emit('update', updatedComponent)
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  cols.value = newProps.cols || 3
  gutter.value = newProps.gutter || 20
}, { deep: true })
</script>

<style scoped>
.grid-container-props-editor {
  padding: 10px;
}
</style>
