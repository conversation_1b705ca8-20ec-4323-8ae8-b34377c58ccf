<template>
  <div class="text-props-editor">
    <el-form label-position="top">
      <el-form-item label="Content">
        <el-input
          v-model="componentProps.content"
          type="textarea"
          :rows="4"
          @change="updateProps"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create a reactive copy of props
const componentProps = ref({})

// Initialize component props
onMounted(() => {
  componentProps.value = { ...props.component.props }
})

// Update props
const updateProps = () => {
  emit('update', { ...componentProps.value })
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  componentProps.value = { ...newProps }
}, { deep: true })
</script>
