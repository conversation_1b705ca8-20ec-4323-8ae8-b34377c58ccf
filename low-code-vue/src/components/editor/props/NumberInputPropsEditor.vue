<template>
  <div class="number-input-props-editor">
    <common-props-editor :component="component" @update="updateComponent">
      <el-form label-position="top">
        <el-form-item label="默认值">
          <el-input-number
            v-model="componentProps.defaultValue"
            :min="componentProps.min"
            :max="componentProps.max"
            :step="componentProps.step"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="最小值">
          <el-input-number
            v-model="componentProps.min"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="最大值">
          <el-input-number
            v-model="componentProps.max"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="步长">
          <el-input-number
            v-model="componentProps.step"
            :min="0.001"
            :precision="3"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="精度">
          <el-input-number
            v-model="componentProps.precision"
            :min="0"
            :max="10"
            :step="1"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="严格步长">
          <el-switch
            v-model="componentProps.stepStrictly"
            @change="updateProps"
          />
          <div class="tip-text">启用后，只能输入步长的倍数</div>
        </el-form-item>

        <el-form-item label="显示控制按钮">
          <el-switch
            v-model="componentProps.controls"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="禁用状态">
          <el-switch
            v-model="componentProps.disabled"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="占位文本">
          <el-input
            v-model="componentProps.placeholder"
            @change="updateProps"
          />
        </el-form-item>

        <!-- 使用通用样式编辑器 -->
        <styles-editor v-model="componentStyles" @update:modelValue="updateStyles" />
      </el-form>
    </common-props-editor>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import CommonPropsEditor from './CommonPropsEditor.vue'
import StylesEditor from './StylesEditor.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = reactive({
  defaultValue: 0,
  min: -Infinity,
  max: Infinity,
  step: 1,
  precision: 0,
  stepStrictly: false,
  controls: true,
  disabled: false,
  placeholder: '请输入数字'
})

// Component styles
const componentStyles = reactive({
  width: '100%',
  margin: '0'
})

// Initialize component props and styles
onMounted(() => {
  // Initialize props
  if (props.component.props) {
    Object.assign(componentProps, props.component.props)
  }

  // Initialize styles
  if (props.component.styles) {
    Object.assign(componentStyles, props.component.styles)
  }
})

// Watch for changes in component
watch(() => props.component, (newComponent) => {
  // Update props
  if (newComponent.props) {
    Object.assign(componentProps, newComponent.props)
  }

  // Update styles
  if (newComponent.styles) {
    Object.assign(componentStyles, newComponent.styles)
  }
}, { deep: true })

// Update props
const updateProps = () => {
  const updatedComponent = {
    ...props.component,
    props: { ...componentProps }
  }

  emit('update', updatedComponent)
}

// Update styles
const updateStyles = () => {
  const updatedComponent = {
    ...props.component,
    styles: { ...componentStyles }
  }

  emit('update', updatedComponent)
}

// Update component
const updateComponent = (updatedComponent) => {
  emit('update', updatedComponent)
}
</script>

<style scoped>
.number-input-props-editor {
  padding: 10px 0;
}

.tip-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
