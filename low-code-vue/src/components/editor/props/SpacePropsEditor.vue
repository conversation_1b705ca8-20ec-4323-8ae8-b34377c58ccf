<template>
  <div class="space-props-editor">
    <el-form label-position="top">
      <el-form-item label="方向">
        <el-select v-model="componentProps.direction" @change="updateProps">
          <el-option label="水平" value="horizontal" />
          <el-option label="垂直" value="vertical" />
        </el-select>
      </el-form-item>

      <el-form-item label="间距大小">
        <el-select v-model="componentProps.size" @change="updateProps">
          <el-option label="小" value="small" />
          <el-option label="默认" value="default" />
          <el-option label="大" value="large" />
          <el-option label="自定义" value="custom" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="componentProps.size === 'custom'" label="自定义间距 (px)">
        <el-input-number v-model="customSize" :min="0" :max="100" @change="updateCustomSize" />
      </el-form-item>

      <el-form-item label="是否换行">
        <el-switch v-model="componentProps.wrap" @change="updateProps" />
      </el-form-item>

      <el-form-item label="对齐方式">
        <el-select v-model="componentProps.alignment" @change="updateProps">
          <el-option label="居中" value="center" />
          <el-option label="顶部对齐" value="flex-start" />
          <el-option label="底部对齐" value="flex-end" />
          <el-option label="两端对齐" value="space-between" />
          <el-option label="环绕对齐" value="space-around" />
        </el-select>
      </el-form-item>

      <el-form-item label="子项数量">
        <el-input-number v-model="itemCount" :min="1" :max="10" @change="updateItemCount" />
      </el-form-item>

      <el-form-item label="子项类型">
        <el-select v-model="itemType" @change="updateItemType">
          <el-option label="卡片" value="card" />
          <el-option label="按钮" value="button" />
          <el-option label="文本" value="text" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create reactive properties
const componentProps = ref({
  direction: 'horizontal',
  size: 'default',
  wrap: false,
  alignment: 'center',
  itemCount: 3,
  itemType: 'card'
})

// Custom size for when size is set to 'custom'
const customSize = ref(20)

// Item count and type
const itemCount = ref(3)
const itemType = ref('card')

// Initialize component props
onMounted(() => {
  // Initialize from component props
  if (props.component.props) {
    componentProps.value.direction = props.component.props.direction || 'horizontal'
    componentProps.value.size = props.component.props.size || 'default'
    componentProps.value.wrap = props.component.props.wrap || false
    componentProps.value.alignment = props.component.props.alignment || 'center'
    
    // Initialize custom size if size is a number
    if (typeof props.component.props.size === 'number') {
      customSize.value = props.component.props.size
      componentProps.value.size = 'custom'
    }
    
    // Initialize item count and type
    if (props.component.props.itemCount) {
      itemCount.value = props.component.props.itemCount
    }
    
    if (props.component.props.itemType) {
      itemType.value = props.component.props.itemType
    }
  }
})

// Update props
const updateProps = () => {
  // Handle custom size
  let finalSize = componentProps.value.size
  if (finalSize === 'custom') {
    finalSize = customSize.value
  }
  
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      direction: componentProps.value.direction,
      size: finalSize,
      wrap: componentProps.value.wrap,
      alignment: componentProps.value.alignment,
      itemCount: itemCount.value,
      itemType: itemType.value
    }
  }
  
  emit('update', updatedComponent)
}

// Update custom size
const updateCustomSize = (value) => {
  customSize.value = value
  updateProps()
}

// Update item count
const updateItemCount = (value) => {
  itemCount.value = value
  updateProps()
}

// Update item type
const updateItemType = (value) => {
  itemType.value = value
  updateProps()
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  if (newProps) {
    componentProps.value.direction = newProps.direction || 'horizontal'
    componentProps.value.wrap = newProps.wrap || false
    componentProps.value.alignment = newProps.alignment || 'center'
    
    // Handle size
    if (typeof newProps.size === 'number') {
      customSize.value = newProps.size
      componentProps.value.size = 'custom'
    } else {
      componentProps.value.size = newProps.size || 'default'
    }
    
    // Handle item count and type
    if (newProps.itemCount) {
      itemCount.value = newProps.itemCount
    }
    
    if (newProps.itemType) {
      itemType.value = newProps.itemType
    }
  }
}, { deep: true })
</script>

<style scoped>
.space-props-editor {
  padding: 10px;
}
</style>
