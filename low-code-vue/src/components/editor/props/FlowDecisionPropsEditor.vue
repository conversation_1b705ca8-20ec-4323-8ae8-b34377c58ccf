<template>
  <div class="flow-decision-props-editor">
    <el-form label-position="top">
      <el-form-item label="标题">
        <el-input v-model="componentProps.title" placeholder="输入决策节点标题" @input="updateProps" />
      </el-form-item>
      
      <el-form-item label="描述">
        <el-input 
          type="textarea" 
          v-model="componentProps.description" 
          placeholder="输入决策节点描述" 
          :rows="3" 
          @input="updateProps" 
        />
      </el-form-item>
      
      <el-form-item label="条件分支">
        <div v-for="(branch, index) in branches" :key="index" class="branch-item">
          <el-input v-model="branch.label" placeholder="分支名称" @input="updateBranches">
            <template #prepend>分支</template>
          </el-input>
          <el-button 
            type="danger" 
            icon="Delete" 
            circle 
            @click="removeBranch(index)"
            :disabled="branches.length <= 1"
          ></el-button>
        </div>
        <el-button type="primary" icon="Plus" @click="addBranch" style="margin-top: 10px;">添加分支</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Delete, Plus } from '@element-plus/icons-vue'

// 不需要导入 defineProps 和 defineEmits
const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = ref({
  title: '',
  description: '',
  branches: []
})

// Branches for easier manipulation
const branches = ref([])

// Initialize component props
onMounted(() => {
  console.log('FlowDecisionPropsEditor mounted with component:', props.component.id)
  initializeProps()
})

// Watch for changes in component
watch(() => props.component, () => {
  console.log('FlowDecisionPropsEditor detected component change')
  initializeProps()
}, { deep: true })

// Initialize props from component
const initializeProps = () => {
  componentProps.value = {
    title: props.component.props?.title || '决策',
    description: props.component.props?.description || '',
    branches: props.component.props?.branches || []
  }
  
  // Initialize branches
  if (Array.isArray(componentProps.value.branches) && componentProps.value.branches.length > 0) {
    branches.value = [...componentProps.value.branches]
  } else {
    branches.value = [{ label: '是' }, { label: '否' }]
    componentProps.value.branches = [...branches.value]
  }
}

// Add branch
const addBranch = () => {
  const newIndex = branches.value.length + 1
  branches.value.push({ label: `分支${newIndex}` })
  updateBranches()
}

// Remove branch
const removeBranch = (index) => {
  if (branches.value.length > 1) {
    branches.value.splice(index, 1)
    updateBranches()
  }
}

// Update branches
const updateBranches = () => {
  componentProps.value.branches = [...branches.value]
  updateProps()
}

// Update props
const updateProps = () => {
  // Create a completely new component object to ensure reactivity
  const updatedComponent = {
    ...props.component,
    props: {
      ...componentProps.value
    }
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.flow-decision-props-editor {
  padding: 10px 0;
}

.branch-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.branch-item .el-input {
  flex: 1;
}
</style>
