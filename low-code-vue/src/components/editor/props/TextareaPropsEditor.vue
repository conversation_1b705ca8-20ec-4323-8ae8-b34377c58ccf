<template>
  <div class="textarea-props-editor">
    <common-props-editor :component="component" @update="updateComponent">
      <el-form label-position="top">
        <el-form-item label="默认值">
          <el-input
            type="textarea"
            v-model="componentProps.defaultValue"
            :rows="3"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="占位文本">
          <el-input
            v-model="componentProps.placeholder"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="行数">
          <el-input-number
            v-model="componentProps.rows"
            :min="1"
            :max="20"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="自动调整高度">
          <el-switch
            v-model="componentProps.autosize"
            @change="updateProps"
          />
        </el-form-item>

        <template v-if="componentProps.autosize">
          <el-form-item label="最小行数">
            <el-input-number
              v-model="componentProps.minRows"
              :min="1"
              :max="componentProps.maxRows"
              @change="updateProps"
            />
          </el-form-item>

          <el-form-item label="最大行数">
            <el-input-number
              v-model="componentProps.maxRows"
              :min="componentProps.minRows"
              :max="50"
              @change="updateProps"
            />
          </el-form-item>
        </template>

        <el-form-item label="最大长度">
          <el-input-number
            v-model="componentProps.maxlength"
            :min="0"
            :max="1000"
            @change="updateProps"
          />
          <div class="tip-text">0表示不限制</div>
        </el-form-item>

        <el-form-item label="显示字数统计">
          <el-switch
            v-model="componentProps.showWordLimit"
            :disabled="!componentProps.maxlength"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="可清空">
          <el-switch
            v-model="componentProps.clearable"
            @change="updateProps"
          />
        </el-form-item>

        <el-form-item label="禁用状态">
          <el-switch
            v-model="componentProps.disabled"
            @change="updateProps"
          />
        </el-form-item>

        <!-- 使用通用样式编辑器 -->
        <styles-editor v-model="componentStyles" @update:modelValue="updateStyles" />
      </el-form>
    </common-props-editor>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import CommonPropsEditor from './CommonPropsEditor.vue'
import StylesEditor from './StylesEditor.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = reactive({
  defaultValue: '',
  placeholder: '请输入内容',
  rows: 4,
  autosize: false,
  minRows: 2,
  maxRows: 6,
  maxlength: 0,
  showWordLimit: false,
  clearable: true,
  disabled: false
})

// Component styles
const componentStyles = reactive({
  width: '100%',
  margin: '0'
})

// Initialize component props and styles
onMounted(() => {
  // Initialize props
  if (props.component.props) {
    Object.assign(componentProps, props.component.props)
  }

  // Initialize styles
  if (props.component.styles) {
    Object.assign(componentStyles, props.component.styles)
  }
})

// Watch for changes in component
watch(() => props.component, (newComponent) => {
  // Update props
  if (newComponent.props) {
    Object.assign(componentProps, newComponent.props)
  }

  // Update styles
  if (newComponent.styles) {
    Object.assign(componentStyles, newComponent.styles)
  }
}, { deep: true })

// Update props
const updateProps = () => {
  const updatedComponent = {
    ...props.component,
    props: { ...componentProps }
  }

  emit('update', updatedComponent)
}

// Update styles
const updateStyles = () => {
  const updatedComponent = {
    ...props.component,
    styles: { ...componentStyles }
  }

  emit('update', updatedComponent)
}

// Update component
const updateComponent = (updatedComponent) => {
  emit('update', updatedComponent)
}
</script>

<style scoped>
.textarea-props-editor {
  padding: 10px 0;
}

.tip-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
