<template>
  <div class="checkbox-props-editor">
    <el-form label-position="top">
      <el-form-item label="选项">
        <div v-for="(option, index) in options" :key="index" class="option-item">
          <el-input v-model="option.label" placeholder="选项文本" @input="updateOptions">
            <template #prepend>文本</template>
          </el-input>
          <el-input v-model="option.value" placeholder="选项值" @input="updateOptions">
            <template #prepend>值</template>
          </el-input>
          <el-button
            type="danger"
            icon="Delete"
            circle
            @click="removeOption(index)"
            :disabled="options.length <= 1"
          ></el-button>
        </div>
        <div style="display: flex; gap: 10px; margin-top: 10px;">
          <el-button type="primary" icon="Plus" @click="addOption">添加选项</el-button>
          <el-button type="success" @click="forceUpdate">刷新组件</el-button>
        </div>
      </el-form-item>

      <el-form-item label="禁用">
        <el-switch v-model="disabled" @change="updateDisabled" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Options for easier manipulation
const options = ref([])
const disabled = ref(false)

// Initialize component props
onMounted(() => {
  console.log('CheckboxPropsEditor mounted with component:', props.component.id)
  initializeProps()
})

// Watch for changes in component
watch(() => props.component, () => {
  console.log('CheckboxPropsEditor detected component change')
  initializeProps()
}, { deep: true })

// Initialize props from component
const initializeProps = () => {
  // Get options from component props
  if (props.component.props && Array.isArray(props.component.props.options)) {
    options.value = props.component.props.options.map(option => ({
      label: option.label,
      value: option.value
    }))
  } else {
    // Default options if none exist
    options.value = [{ label: '选项1', value: '1' }]
  }

  // Get disabled state
  disabled.value = props.component.props?.disabled || false

  console.log('CheckboxPropsEditor initialized with options:', JSON.stringify(options.value))
}

// Add option
const addOption = () => {
  const newIndex = options.value.length + 1
  options.value.push({
    label: `选项${newIndex}`,
    value: `${newIndex}`
  })
  updateOptions()
}

// Remove option
const removeOption = (index) => {
  if (options.value.length > 1) {
    options.value.splice(index, 1)
    updateOptions()
  }
}

// Update options
const updateOptions = () => {
  emitUpdate()
}

// Update disabled state
const updateDisabled = () => {
  emitUpdate()
}

// Emit update event with current props
const emitUpdate = () => {
  // Create a completely new component object to ensure reactivity
  const updatedComponent = {
    ...props.component,
    props: {
      options: options.value.map(option => ({
        label: option.label,
        value: option.value
      })),
      disabled: disabled.value
    }
  }

  console.log('CheckboxPropsEditor emitting update:', JSON.stringify(updatedComponent.props))
  emit('update', updatedComponent)
}

// Force update - this is a debug function to help diagnose issues
const forceUpdate = () => {
  // Create a completely new options array to force reactivity
  const newOptions = options.value.map((option) => ({
    label: option.label,
    value: option.value
  }))

  // Update the options
  options.value = newOptions

  // Force update
  emitUpdate()

  // Show success message
  ElMessage.success('组件已刷新')
}
</script>

<style scoped>
.checkbox-props-editor {
  padding: 10px 0;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.option-item .el-input {
  flex: 1;
}
</style>
