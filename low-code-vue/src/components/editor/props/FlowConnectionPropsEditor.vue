<template>
  <div class="flow-connection-props-editor">
    <el-form label-position="top">
      <el-form-item label="连接标签">
        <el-input v-model="componentProps.label" placeholder="输入连接标签" @input="updateProps" />
      </el-form-item>

      <el-form-item label="连接方向">
        <el-radio-group v-model="componentProps.orientation" @change="updateProps">
          <el-radio-button label="horizontal">水平</el-radio-button>
          <el-radio-button label="vertical">垂直</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="连接类型">
        <el-select v-model="componentProps.type" placeholder="选择连接类型" style="width: 100%" @change="updateProps">
          <el-option label="直线" value="straight" />
          <el-option label="曲线" value="curved" />
          <el-option label="折线" value="angled" />
        </el-select>
      </el-form-item>

      <el-form-item label="连接样式">
        <el-select v-model="componentProps.style" placeholder="选择连接样式" style="width: 100%" @change="updateProps">
          <el-option label="实线" value="solid" />
          <el-option label="虚线" value="dashed" />
          <el-option label="点线" value="dotted" />
        </el-select>
      </el-form-item>

      <el-divider content-position="center">连接节点</el-divider>

      <el-form-item label="起始节点">
        <el-select
          v-model="componentProps.source"
          placeholder="选择起始节点"
          style="width: 100%"
          @change="updateProps"
          filterable
        >
          <el-option
            v-for="component in availableComponents"
            :key="component.id"
            :label="getComponentLabel(component)"
            :value="{ id: component.id, type: component.type, label: getComponentLabel(component) }"
          >
            <div class="component-option">
              <el-tag size="small" :type="getTagType(component.type)">
                {{ component.type }}
              </el-tag>
              <span class="component-label">{{ getComponentLabel(component) }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="结束节点">
        <el-select
          v-model="componentProps.target"
          placeholder="选择结束节点"
          style="width: 100%"
          @change="updateProps"
          filterable
        >
          <el-option
            v-for="component in availableComponents"
            :key="component.id"
            :label="getComponentLabel(component)"
            :value="{ id: component.id, type: component.type, label: getComponentLabel(component) }"
          >
            <div class="component-option">
              <el-tag size="small" :type="getTagType(component.type)">
                {{ component.type }}
              </el-tag>
              <span class="component-label">{{ getComponentLabel(component) }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { useEditorStore } from '../../../store'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])
const editorStore = useEditorStore()

// Component props
const componentProps = ref({
  label: '',
  orientation: 'horizontal',
  type: 'straight',
  style: 'solid',
  source: null,
  target: null
})

// Get all available components from the store
const availableComponents = computed(() => {
  // Get all components from the page
  const allComponents = [...editorStore.pageConfig.components]

  // Recursively get components from containers
  const getChildComponents = (components) => {
    let children = []
    components.forEach(component => {
      if (component.props?.children && Array.isArray(component.props.children)) {
        children = [...children, ...component.props.children]
        children = [...children, ...getChildComponents(component.props.children)]
      }
    })
    return children
  }

  // Add child components from containers
  const childComponents = getChildComponents(allComponents)
  const allAvailableComponents = [...allComponents, ...childComponents]

  // Filter out the current component and non-flow components
  return allAvailableComponents.filter(c =>
    c.id !== props.component.id &&
    (c.type.startsWith('flow') || ['container', 'card'].includes(c.type))
  )
})

// Get component label for display
const getComponentLabel = (component) => {
  if (component.type === 'flowNode' || component.type === 'flowProcess') {
    return component.props?.title || component.type
  } else if (component.type === 'flowStart') {
    return component.props?.title || '开始'
  } else if (component.type === 'flowEnd') {
    return component.props?.title || '结束'
  } else if (component.type === 'flowDecision') {
    return component.props?.title || '决策'
  } else {
    return `${component.type} (${component.id.substring(0, 6)})`
  }
}

// Get tag type based on component type
const getTagType = (type) => {
  if (type === 'flowStart') return 'success'
  if (type === 'flowEnd') return 'danger'
  if (type === 'flowDecision') return 'warning'
  if (type === 'flowProcess') return 'primary'
  if (type === 'flowNode') return 'info'
  return ''
}

// Initialize component props
onMounted(() => {
  console.log('FlowConnectionPropsEditor mounted with component:', props.component.id)
  initializeProps()
})

// Watch for changes in component
watch(() => props.component, () => {
  console.log('FlowConnectionPropsEditor detected component change')
  initializeProps()
}, { deep: true })

// Initialize props from component
const initializeProps = () => {
  componentProps.value = {
    label: props.component.props?.label || '',
    orientation: props.component.props?.orientation || 'horizontal',
    type: props.component.props?.type || 'straight',
    style: props.component.props?.style || 'solid',
    source: props.component.props?.source || null,
    target: props.component.props?.target || null
  }
}

// Update props
const updateProps = () => {
  // Create a completely new component object to ensure reactivity
  const updatedComponent = {
    ...props.component,
    props: {
      ...componentProps.value
    }
  }

  emit('update', updatedComponent)
}
</script>

<style scoped>
.flow-connection-props-editor {
  padding: 10px 0;
}

.component-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.component-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
