<template>
  <div class="button-props-editor">
    <el-form label-position="top">
      <el-form-item label="按钮文本">
        <el-input
          v-model="componentProps.text"
          placeholder="输入按钮文本"
          @change="updateProps"
        />
      </el-form-item>

      <el-form-item label="按钮类型">
        <el-select v-model="componentProps.type" @change="updateProps">
          <el-option label="默认" value="" />
          <el-option label="主要" value="primary" />
          <el-option label="成功" value="success" />
          <el-option label="警告" value="warning" />
          <el-option label="危险" value="danger" />
          <el-option label="信息" value="info" />
        </el-select>
      </el-form-item>

      <el-form-item label="链接（可选）">
        <el-input
          v-model="componentProps.link"
          placeholder="例如：https://example.com"
          @change="updateProps"
        />
        <div class="tip-text">
          注意：点击事件请在「事件」选项卡中配置
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create a reactive copy of props
const componentProps = ref({ ...props.component.props })



// Update props
const updateProps = () => {
  const updatedComponent = {
    ...props.component,
    props: componentProps.value
  }

  emit('update', updatedComponent)
}

// Watch for component changes
watch(() => props.component, () => {
  componentProps.value = { ...props.component.props }
}, { deep: true })
</script>

<style scoped>
.button-props-editor {
  padding: 10px;
}

.tip-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
