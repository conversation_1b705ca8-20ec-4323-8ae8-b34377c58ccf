<template>
  <div class="flow-start-props-editor">
    <el-form label-position="top">
      <el-form-item label="标题">
        <el-input v-model="componentProps.title" placeholder="输入开始节点标题" @input="updateProps" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'

// 不需要导入 defineProps 和 defineEmits
const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = ref({
  title: ''
})

// Initialize component props
onMounted(() => {
  console.log('FlowStartPropsEditor mounted with component:', props.component.id)
  initializeProps()
})

// Watch for changes in component
watch(() => props.component, () => {
  console.log('FlowStartPropsEditor detected component change')
  initializeProps()
}, { deep: true })

// Initialize props from component
const initializeProps = () => {
  componentProps.value = {
    title: props.component.props?.title || '开始'
  }
}

// Update props
const updateProps = () => {
  // Create a completely new component object to ensure reactivity
  const updatedComponent = {
    ...props.component,
    props: {
      ...componentProps.value
    }
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.flow-start-props-editor {
  padding: 10px 0;
}
</style>
