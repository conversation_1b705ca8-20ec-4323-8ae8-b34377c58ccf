<template>
  <div class="collapse-container-props-editor">
    <el-form label-position="top">
      <el-form-item label="当前展开的面板">
        <el-select v-model="activeNames" multiple @change="updateActiveNames">
          <el-option 
            v-for="item in component.props.items" 
            :key="item.name" 
            :label="item.title" 
            :value="item.name" 
          />
        </el-select>
      </el-form-item>

      <el-divider>面板管理</el-divider>

      <div v-for="(item, index) in items" :key="index" class="panel-item">
        <el-row :gutter="10">
          <el-col :span="10">
            <el-form-item label="面板标题">
              <el-input v-model="item.title" @change="updateItems" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="面板标识">
              <el-input v-model="item.name" @change="updateItems" />
            </el-form-item>
          </el-col>
          <el-col :span="4" class="panel-actions">
            <el-button 
              type="danger" 
              icon="Delete" 
              circle 
              size="small" 
              @click="removePanel(index)"
              :disabled="items.length <= 1"
            ></el-button>
          </el-col>
        </el-row>
      </div>

      <el-button type="primary" icon="Plus" @click="addPanel">添加面板</el-button>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create reactive properties
const items = ref([...props.component.props.items] || [])
const activeNames = ref([...props.component.props.activeNames] || [])

// Update active names
const updateActiveNames = (value) => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      activeNames: value
    }
  }

  emit('update', updatedComponent)
}

// Update items
const updateItems = () => {
  // Create a deep copy of items to ensure reactivity
  const updatedItems = JSON.parse(JSON.stringify(items.value))
  
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      items: updatedItems
    }
  }

  emit('update', updatedComponent)
}

// Add a new panel
const addPanel = () => {
  // Generate a unique name
  const newName = 'panel' + (items.value.length + 1)
  
  // Add the new panel
  items.value.push({
    title: '新面板',
    name: newName,
    children: []
  })
  
  // Update the component
  updateItems()
}

// Remove a panel
const removePanel = (index) => {
  // Don't remove the last panel
  if (items.value.length <= 1) {
    return
  }
  
  // Get the panel being removed
  const removedPanel = items.value[index]
  
  // Remove the panel
  items.value.splice(index, 1)
  
  // If the panel is in activeNames, remove it
  const nameIndex = activeNames.value.indexOf(removedPanel.name)
  if (nameIndex !== -1) {
    activeNames.value.splice(nameIndex, 1)
    updateActiveNames(activeNames.value)
  }
  
  // Update the component
  updateItems()
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  items.value = [...newProps.items] || []
  activeNames.value = [...newProps.activeNames] || []
}, { deep: true })
</script>

<style scoped>
.collapse-container-props-editor {
  padding: 10px;
}

.panel-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.panel-actions {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 100%;
  padding-bottom: 10px;
}
</style>
