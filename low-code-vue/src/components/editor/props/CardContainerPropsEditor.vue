<template>
  <div class="card-container-props-editor">
    <el-form label-position="top">
      <el-form-item label="卡片标题">
        <el-input v-model="title" @change="updateTitle" />
      </el-form-item>

      <el-form-item label="阴影显示时机">
        <el-select v-model="shadow" @change="updateShadow">
          <el-option label="始终显示" value="always" />
          <el-option label="鼠标悬停时显示" value="hover" />
          <el-option label="从不显示" value="never" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create reactive properties
const title = ref(props.component.props.title || '')
const shadow = ref(props.component.props.shadow || 'always')

// Update title
const updateTitle = (value) => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      title: value
    }
  }

  emit('update', updatedComponent)
}

// Update shadow
const updateShadow = (value) => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...props.component.props,
      shadow: value
    }
  }

  emit('update', updatedComponent)
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  title.value = newProps.title || ''
  shadow.value = newProps.shadow || 'always'
}, { deep: true })
</script>

<style scoped>
.card-container-props-editor {
  padding: 10px;
}
</style>
