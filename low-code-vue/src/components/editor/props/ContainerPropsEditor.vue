<template>
  <div class="container-props-editor">
    <el-form label-position="top">
      <el-form-item label="容器类型">
        <el-select v-model="containerType" @change="updateContainerType">
          <el-option label="垂直排列" value="column" />
          <el-option label="水平排列" value="row" />
        </el-select>
      </el-form-item>

      <el-form-item label="主轴对齐">
        <el-select v-model="justifyContent" @change="updateJustifyContent">
          <el-option label="左对齐/顶对齐" value="flex-start" />
          <el-option label="居中对齐" value="center" />
          <el-option label="右对齐/底对齐" value="flex-end" />
          <el-option label="两端对齐" value="space-between" />
          <el-option label="均匀分布" value="space-around" />
        </el-select>
      </el-form-item>

      <el-form-item label="交叉轴对齐">
        <el-select v-model="alignItems" @change="updateAlignItems">
          <el-option label="左对齐/顶对齐" value="flex-start" />
          <el-option label="居中对齐" value="center" />
          <el-option label="右对齐/底对齐" value="flex-end" />
          <el-option label="拉伸填充" value="stretch" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Create a reactive copy of props
const componentProps = ref({ ...props.component.props })

// Computed properties for container styles
const containerType = computed({
  get: () => {
    return props.component.styles.flexDirection || 'column'
  },
  set: (value) => {
    // This will be handled by updateContainerType
  }
})

const justifyContent = computed({
  get: () => {
    return props.component.styles.justifyContent || 'flex-start'
  },
  set: (value) => {
    // This will be handled by updateJustifyContent
  }
})

const alignItems = computed({
  get: () => {
    return props.component.styles.alignItems || 'flex-start'
  },
  set: (value) => {
    // This will be handled by updateAlignItems
  }
})

// Update container type
const updateContainerType = (value) => {
  const updatedComponent = {
    ...props.component,
    styles: {
      ...props.component.styles,
      flexDirection: value
    }
  }

  emit('update', updatedComponent)
}

// Update justify content
const updateJustifyContent = (value) => {
  const updatedComponent = {
    ...props.component,
    styles: {
      ...props.component.styles,
      justifyContent: value
    }
  }

  emit('update', updatedComponent)
}

// Update align items
const updateAlignItems = (value) => {
  const updatedComponent = {
    ...props.component,
    styles: {
      ...props.component.styles,
      alignItems: value
    }
  }

  emit('update', updatedComponent)
}

// Watch for changes in component props
watch(() => props.component.props, (newProps) => {
  componentProps.value = { ...newProps }
}, { deep: true })
</script>
