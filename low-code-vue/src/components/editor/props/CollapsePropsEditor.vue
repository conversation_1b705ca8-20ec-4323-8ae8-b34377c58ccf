<template>
  <div class="collapse-props-editor">
    <el-form label-position="top">
      <el-form-item label="手风琴模式">
        <el-switch v-model="componentProps.accordion" @change="updateProps" />
      </el-form-item>
      
      <el-form-item label="折叠面板项">
        <div v-for="(item, index) in items" :key="index" class="collapse-item">
          <div class="collapse-item-header">
            <h4>面板 {{ index + 1 }}</h4>
            <el-button 
              type="danger" 
              icon="Delete" 
              circle 
              @click="removeItem(index)"
              :disabled="items.length <= 1"
            ></el-button>
          </div>
          
          <div class="collapse-item-content">
            <el-form-item label="标题">
              <el-input v-model="item.title" placeholder="输入标题" @change="updateItems" />
            </el-form-item>
            
            <el-form-item label="内容">
              <el-input 
                type="textarea" 
                v-model="item.content" 
                placeholder="输入内容" 
                :rows="4" 
                @change="updateItems" 
              />
            </el-form-item>
          </div>
        </div>
        
        <el-button type="primary" icon="Plus" @click="addItem" style="margin-top: 10px;">添加面板</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Delete, Plus } from '@element-plus/icons-vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component props
const componentProps = ref({
  accordion: false,
  items: []
})

// Items for easier manipulation
const items = ref([])

// Initialize component props
onMounted(() => {
  componentProps.value = { ...props.component.props }
  items.value = [...(componentProps.value.items || [])]
  
  // Ensure we have at least one item
  if (items.value.length === 0) {
    items.value.push({ 
      title: '面板标题', 
      content: '面板内容' 
    })
  }
})

// Add item
const addItem = () => {
  const newIndex = items.value.length + 1
  items.value.push({ 
    title: `面板标题 ${newIndex}`, 
    content: `面板内容 ${newIndex}` 
  })
  updateItems()
}

// Remove item
const removeItem = (index) => {
  if (items.value.length > 1) {
    items.value.splice(index, 1)
    updateItems()
  }
}

// Update items
const updateItems = () => {
  componentProps.value.items = [...items.value]
  updateProps()
}

// Update props
const updateProps = () => {
  const updatedComponent = {
    ...props.component,
    props: {
      ...componentProps.value
    }
  }
  
  emit('update', updatedComponent)
}
</script>

<style scoped>
.collapse-props-editor {
  padding: 10px 0;
}

.collapse-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 15px;
  background-color: #f5f7fa;
}

.collapse-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #ebeef5;
}

.collapse-item-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.collapse-item-content {
  padding: 15px;
}
</style>
