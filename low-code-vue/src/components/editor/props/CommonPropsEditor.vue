<template>
  <div class="common-props-editor">
    <el-form label-position="top">
      <el-divider content-position="center">通用属性</el-divider>
      
      <el-form-item label="组件状态">
        <el-switch
          v-model="isEnabled"
          active-text="启用"
          inactive-text="禁用"
          @change="toggleComponentState"
        />
      </el-form-item>
      
      <slot></slot>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Component enabled state
const isEnabled = ref(true)

// Initialize component state
onMounted(() => {
  isEnabled.value = props.component.disabled !== true
})

// Watch for component changes
watch(() => props.component, (newComponent) => {
  isEnabled.value = newComponent.disabled !== true
}, { deep: true })

// Toggle component state
const toggleComponentState = (value) => {
  const updatedComponent = {
    ...props.component,
    disabled: !value
  }
  emit('update', updatedComponent)
}
</script>

<style scoped>
.common-props-editor {
  margin-bottom: 20px;
}
</style>
