<template>
  <div class="canvas-container">
    <div
      class="canvas"
      :class="[`device-${device}`]"
      :style="{ transform: `scale(${scale})`, ...pageStyles }"
      @dragover.prevent
      @drop="handleDrop"
      @click="clearSelection"
    >
      <div class="canvas-content">
        <template v-if="modelValue && modelValue.length > 0">
          <div
            v-for="(element, index) in components"
            :key="element.id"
            class="component-wrapper"
            :class="{ 'is-selected': selectedId === element.id }"
            @click.stop="selectComponent(element)"
            draggable="true"
            @dragstart="startDrag($event, index)"
            @dragover.prevent
            @dragenter.prevent
            @drop="onDrop($event, index)"
          >
            <component-renderer :component="element" @update="updateComponent" @select="selectComponent" />
            <div class="component-actions" v-if="selectedId === element.id">
              <el-button size="small" circle icon="Delete" @click.stop="removeComponent(index)"></el-button>
              <el-button size="small" circle icon="CopyDocument" @click.stop="duplicateComponent(index)"></el-button>
              <el-button size="small" circle icon="Top" @click.stop="moveComponent(index, index - 1)" :disabled="index === 0"></el-button>
              <el-button size="small" circle icon="Bottom" @click.stop="moveComponent(index, index + 1)" :disabled="index === components.length - 1"></el-button>
            </div>
          </div>
        </template>
        <div v-else class="empty-canvas">
          <el-empty description="拖拽组件到这里开始设计" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import ComponentRenderer from './ComponentRenderer.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  device: {
    type: String,
    default: 'mobile'
  },
  scale: {
    type: Number,
    default: 1
  },
  selectedId: {
    type: String,
    default: null
  },
  pageStyles: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'select', 'remove', 'duplicate'])

// Local components array
const components = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})

// Handle component drop
const handleDrop = (event) => {
  const componentData = event.dataTransfer.getData('application/json')

  if (componentData) {
    try {
      const newComponent = JSON.parse(componentData)

      // Add component to the list
      components.value.push(newComponent)

      // Select the new component
      selectComponent(newComponent)
    } catch (error) {
      console.error('Failed to parse component data:', error)
    }
  }
}

// Select component
const selectComponent = (component) => {
  emit('select', component)
}

// Clear selection
const clearSelection = (event) => {
  // Only clear if clicking directly on the canvas (not on a component)
  if (event.target.classList.contains('canvas') || event.target.classList.contains('canvas-content')) {
    emit('select', null)
  }
}

// Remove component
const removeComponent = (index) => {
  emit('remove', index)
}

// Duplicate component
const duplicateComponent = (index) => {
  emit('duplicate', index)
}

// Update component
const updateComponent = (updatedComponent) => {
  console.log('Canvas received updated component:', updatedComponent.id, updatedComponent.type)

  // Log specific component types for debugging
  if (updatedComponent.type === 'carousel') {
    console.log('Carousel items:', JSON.stringify(updatedComponent.props.items))
  } else if (updatedComponent.type === 'checkbox') {
    console.log('Checkbox options:', JSON.stringify(updatedComponent.props.options))
  }

  const index = components.value.findIndex(c => c.id === updatedComponent.id)
  if (index !== -1) {
    try {
      // Create a new array to ensure reactivity
      const newComponents = [...components.value]

      // Create a completely new component object
      const newComponent = {
        id: updatedComponent.id,
        type: updatedComponent.type,
        props: JSON.parse(JSON.stringify(updatedComponent.props || {})),
        styles: JSON.parse(JSON.stringify(updatedComponent.styles || {}))
      }

      // Update the component in the array
      newComponents[index] = newComponent

      // Update the components
      components.value = newComponents

      // Force update to parent
      emit('update:modelValue', newComponents)

      console.log('Canvas updated component successfully')
    } catch (error) {
      console.error('Error updating component:', error)
    }
  } else {
    console.warn('Component not found for update:', updatedComponent.id)
  }
}

// Move component
const moveComponent = (fromIndex, toIndex) => {
  if (toIndex < 0 || toIndex >= components.value.length) {
    return
  }

  const component = components.value[fromIndex]
  components.value.splice(fromIndex, 1)
  components.value.splice(toIndex, 0, component)
}

// Drag and drop implementation
const draggedItem = ref(null);

const startDrag = (event, index) => {
  draggedItem.value = index;
  event.dataTransfer.effectAllowed = 'move';
}

const onDrop = (event, index) => {
  event.stopPropagation();

  // If dropping on a component, reorder
  if (draggedItem.value !== null) {
    const itemToMove = components.value[draggedItem.value];
    const newComponents = [...components.value];

    // Remove from old position
    newComponents.splice(draggedItem.value, 1);

    // Add at new position
    newComponents.splice(index, 0, itemToMove);

    // Update components
    emit('update:modelValue', newComponents);
    draggedItem.value = null;
  }
}
</script>

<style scoped>

.canvas-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  background-color: #f5f7fa;
}

.canvas {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform-origin: top center;
  transition: transform 0.3s;
  overflow-y: auto;
}

.device-mobile {
  width: 375px;
  height: 667px;
}

.device-tablet {
  width: 768px;
  height: 1024px;
}

.device-desktop {
  width: 1280px;
  height: 800px;
}

.canvas-content {
  min-height: 100%;
}

.empty-canvas {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.component-wrapper {
  position: relative;
  border: 1px dashed transparent;
}

.component-wrapper:hover {
  border-color: #409eff;
}

.component-wrapper.is-selected {
  border-color: #409eff;
}

.component-actions {
  position: absolute;
  top: -15px;
  right: 0;
  display: flex;
  gap: 5px;
  z-index: 100;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.chosen {
  background: #f0f9ff;
}
</style>
