<template>
  <div class="format-guide">
    <div class="guide-header">
      <h3>{{ title }}</h3>
      <el-tooltip effect="dark" placement="top">
        <template #content>
          <div>{{ description }}</div>
        </template>
        <el-icon class="help-icon"><QuestionFilled /></el-icon>
      </el-tooltip>
    </div>

    <div class="guide-content">
      <div v-if="examples.length > 0" class="examples-section">
        <h4>示例:</h4>
        <div v-for="(example, index) in examples" :key="index" class="example-item">
          <div class="example-title">{{ example.title }}:</div>
          <div class="example-code">
            <pre>{{ example.code }}</pre>
            <el-button
              type="primary"
              size="small"
              class="copy-button"
              @click="copyToClipboard(example.code)"
            >
              复制
            </el-button>
          </div>
          <div v-if="example.description" class="example-description">
            {{ example.description }}
          </div>
        </div>
      </div>

      <div v-if="properties.length > 0" class="properties-section">
        <h4>
          常用属性:
          <el-input
            v-model="searchQuery"
            placeholder="搜索属性"
            prefix-icon="Search"
            clearable
            size="small"
            class="search-input"
          />
        </h4>

        <el-tabs type="card" class="property-tabs">
          <el-tab-pane label="全部属性">
            <el-table
              :data="filteredProperties"
              style="width: 100%"
              size="small"
              :max-height="400"
              :default-sort="{prop: 'name'}"
            >
              <el-table-column prop="name" label="属性名" width="120" sortable />
              <el-table-column prop="type" label="类型" width="100" sortable />
              <el-table-column prop="description" label="说明" />
              <el-table-column label="示例" width="120">
                <template #default="{row}">
                  <div class="example-cell">
                    <span>{{ row.example }}</span>
                    <el-button
                      type="primary"
                      size="small"
                      circle
                      class="copy-icon"
                      @click="copyToClipboard(row.example)"
                    >
                      <el-icon><CopyDocument /></el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane v-if="hasCategories" label="分类属性">
            <el-collapse>
              <el-collapse-item
                v-for="category in propertyCategories"
                :key="category.name"
                :title="category.name + ' (' + category.properties.length + ')'"
                :name="category.name"
              >
                <el-table
                  :data="filterPropertiesByCategory(category.name)"
                  style="width: 100%"
                  size="small"
                >
                  <el-table-column prop="name" label="属性名" width="120" sortable />
                  <el-table-column prop="type" label="类型" width="100" />
                  <el-table-column prop="description" label="说明" />
                  <el-table-column label="示例" width="120">
                    <template #default="{row}">
                      <div class="example-cell">
                        <span>{{ row.example }}</span>
                        <el-button
                          type="primary"
                          size="small"
                          circle
                          class="copy-icon"
                          @click="copyToClipboard(row.example)"
                        >
                          <el-icon><CopyDocument /></el-icon>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div v-if="notes.length > 0" class="notes-section">
        <h4>注意事项:</h4>
        <ul>
          <li v-for="(note, index) in notes" :key="index">{{ note }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { QuestionFilled, Search, CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  examples: {
    type: Array,
    default: () => []
  },
  properties: {
    type: Array,
    default: () => []
  },
  notes: {
    type: Array,
    default: () => []
  }
})

// 搜索查询
const searchQuery = ref('')

// 过滤属性
const filteredProperties = computed(() => {
  if (!searchQuery.value) {
    return props.properties
  }

  const query = searchQuery.value.toLowerCase()
  return props.properties.filter(prop => {
    return prop.name.toLowerCase().includes(query) ||
           prop.description.toLowerCase().includes(query) ||
           prop.type.toLowerCase().includes(query)
  })
})

// 属性分类
const propertyCategories = computed(() => {
  const categories = {}

  props.properties.forEach(prop => {
    // 从属性描述中提取分类信息
    let categoryName = '其他'

    // 检查属性是否已经有分类注释
    if (prop.description.includes('尺寸') || prop.name.includes('width') || prop.name.includes('height') ||
        prop.name.includes('size') || prop.name.includes('min') || prop.name.includes('max')) {
      categoryName = '尺寸和布局'
    } else if (prop.description.includes('边距') || prop.name.includes('margin') || prop.name.includes('padding')) {
      categoryName = '边距和填充'
    } else if (prop.description.includes('边框') || prop.name.includes('border') || prop.name.includes('radius')) {
      categoryName = '边框和圆角'
    } else if (prop.description.includes('颜色') || prop.name.includes('color') || prop.name.includes('background')) {
      categoryName = '颜色和背景'
    } else if (prop.description.includes('字体') || prop.name.includes('font') || prop.name.includes('text')) {
      categoryName = '字体和文本'
    } else if (prop.description.includes('定位') || prop.name.includes('position') || prop.name.includes('display')) {
      categoryName = '布局和定位'
    } else if (prop.description.includes('Flex') || prop.name.includes('flex') || prop.name.includes('justify') || prop.name.includes('align')) {
      categoryName = 'Flex布局'
    } else if (prop.description.includes('表单') || prop.description.includes('输入框')) {
      categoryName = '表单组件'
    } else if (prop.description.includes('按钮')) {
      categoryName = '按钮属性'
    }

    if (!categories[categoryName]) {
      categories[categoryName] = {
        name: categoryName,
        properties: []
      }
    }

    categories[categoryName].properties.push(prop)
  })

  return Object.values(categories)
})

// 是否有分类
const hasCategories = computed(() => propertyCategories.value.length > 1)

// 按分类过滤属性
const filterPropertiesByCategory = (categoryName) => {
  const category = propertyCategories.value.find(cat => cat.name === categoryName)
  if (!category) return []

  if (!searchQuery.value) {
    return category.properties
  }

  const query = searchQuery.value.toLowerCase()
  return category.properties.filter(prop => {
    return prop.name.toLowerCase().includes(query) ||
           prop.description.toLowerCase().includes(query) ||
           prop.type.toLowerCase().includes(query)
  })
}

// 复制到剪贴板
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text)
    .then(() => {
      ElMessage.success('已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}
</script>

<style scoped>
.format-guide {
  padding: 15px;
  border-left: 1px solid #ebeef5;
  height: 100%;
  overflow-y: auto;
}

.guide-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.guide-header h3 {
  margin: 0;
  margin-right: 8px;
  font-size: 16px;
  color: #303133;
}

.help-icon {
  color: #909399;
  cursor: pointer;
}

.guide-content {
  font-size: 14px;
  color: #606266;
}

.examples-section,
.properties-section,
.notes-section {
  margin-bottom: 20px;
}

h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  width: 200px;
}

.property-tabs {
  margin-top: 10px;
}

.property-tabs :deep(.el-tabs__content) {
  padding: 10px 0;
}

.example-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.copy-icon {
  opacity: 0;
  transition: opacity 0.3s;
}

.example-cell:hover .copy-icon {
  opacity: 1;
}

.example-item {
  margin-bottom: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
}

.example-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.example-code {
  position: relative;
  background-color: #f8f8f8;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 5px;
  overflow-x: auto;
}

.example-code pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

.copy-button {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.example-code:hover .copy-button {
  opacity: 1;
}

.example-description {
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.notes-section ul {
  margin: 0;
  padding-left: 20px;
}

.notes-section li {
  margin-bottom: 5px;
}
</style>
