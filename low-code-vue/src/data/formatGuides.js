// 属性格式说明
export const propsFormatGuide = {
  title: '属性格式说明',
  description: '组件属性是控制组件行为和内容的配置项，使用JSON格式定义',
  examples: [
    {
      title: '基础示例',
      code: `{
  "content": "这是一段文本",
  "size": "medium",
  "disabled": false,
  "items": ["选项1", "选项2", "选项3"]
}`,
      description: '基本的属性定义，包含字符串、数字、布尔值和数组'
    },
    {
      title: '嵌套对象示例',
      code: `{
  "title": "标题",
  "config": {
    "showIcon": true,
    "iconPosition": "left",
    "iconName": "Edit"
  },
  "options": {
    "allowClear": true,
    "placeholder": "请输入内容"
  }
}`,
      description: '包含嵌套对象的属性定义'
    },
    {
      title: '表单组件示例',
      code: `{
  "label": "用户名",
  "placeholder": "请输入用户名",
  "required": true,
  "rules": [
    { "required": true, "message": "用户名不能为空" },
    { "min": 3, "max": 20, "message": "长度在3到20个字符" }
  ],
  "defaultValue": ""
}`,
      description: '表单组件的属性定义示例'
    }
  ],
  properties: [
    // 通用属性
    { name: 'content', type: '字符串', description: '组件显示的文本内容，适用于文本、按钮等组件', example: '"Hello World"' },
    { name: 'disabled', type: '布尔值', description: '是否禁用组件，禁用后组件不可交互', example: 'true/false' },
    { name: 'size', type: '字符串', description: '组件大小，可选值通常有：large/default/small', example: '"small"' },
    { name: 'visible', type: '布尔值', description: '组件是否可见，可用于条件显示', example: 'true/false' },
    { name: 'name', type: '字符串', description: '组件名称，通常用于表单提交时的字段名', example: '"username"' },

    // 表单组件属性
    { name: 'placeholder', type: '字符串', description: '输入框占位文本，当输入框为空时显示', example: '"请输入用户名"' },
    { name: 'defaultValue', type: '任意类型', description: '组件默认值，可以是字符串、数字、布尔值等', example: '"默认值"' },
    { name: 'clearable', type: '布尔值', description: '是否可清空，适用于输入框类组件', example: 'true' },
    { name: 'maxlength', type: '数字', description: '最大输入长度，适用于输入框', example: '100' },
    { name: 'showWordLimit', type: '布尔值', description: '是否显示字数统计，需要设置maxlength', example: 'true' },
    { name: 'type', type: '字符串', description: '输入框类型，如text、password、number等', example: '"password"' },
    { name: 'rows', type: '数字', description: '文本域的行数，适用于textarea', example: '4' },
    { name: 'autosize', type: '布尔值/对象', description: '文本域自适应内容高度', example: 'true 或 {minRows:2,maxRows:6}' },
    { name: 'readonly', type: '布尔值', description: '是否只读，只读状态下不可编辑但可选中', example: 'true' },

    // 选择器属性
    { name: 'options', type: '数组', description: '选项配置，适用于select、radio、checkbox等', example: '[{label:"选项1",value:"1"}]' },
    { name: 'multiple', type: '布尔值', description: '是否多选，适用于select', example: 'true' },
    { name: 'filterable', type: '布尔值', description: '是否可搜索，适用于select', example: 'true' },
    { name: 'allowCreate', type: '布尔值', description: '是否允许创建新选项，适用于select', example: 'true' },

    // 数字输入框属性
    { name: 'min', type: '数字', description: '最小值，适用于数字输入框', example: '0' },
    { name: 'max', type: '数字', description: '最大值，适用于数字输入框', example: '100' },
    { name: 'step', type: '数字', description: '步长，适用于数字输入框', example: '1' },
    { name: 'precision', type: '数字', description: '精度，小数点后位数', example: '2' },
    { name: 'controls', type: '布尔值', description: '是否显示控制按钮，适用于数字输入框', example: 'true' },

    // 日期选择器属性
    { name: 'format', type: '字符串', description: '日期格式，如yyyy-MM-dd', example: '"yyyy-MM-dd"' },
    { name: 'valueFormat', type: '字符串', description: '绑定值的格式，如yyyy-MM-dd', example: '"yyyy-MM-dd"' },
    { name: 'rangeSeparator', type: '字符串', description: '范围选择器分隔符', example: '"至"' },
    { name: 'startPlaceholder', type: '字符串', description: '范围选择器开始占位符', example: '"开始日期"' },
    { name: 'endPlaceholder', type: '字符串', description: '范围选择器结束占位符', example: '"结束日期"' },

    // 表单验证属性
    { name: 'required', type: '布尔值', description: '是否必填，用于表单验证', example: 'true' },
    { name: 'rules', type: '数组', description: '验证规则数组，包含多个验证规则对象', example: '[{required:true,message:"必填"}]' },
    { name: 'validateOnRuleChange', type: '布尔值', description: '规则变化时是否触发验证', example: 'true' },

    // 按钮属性
    { name: 'type', type: '字符串', description: '按钮类型，如primary、success、warning、danger等', example: '"primary"' },
    { name: 'plain', type: '布尔值', description: '是否朴素按钮，背景色透明', example: 'true' },
    { name: 'round', type: '布尔值', description: '是否圆角按钮', example: 'true' },
    { name: 'circle', type: '布尔值', description: '是否圆形按钮', example: 'true' },
    { name: 'loading', type: '布尔值', description: '是否加载中状态', example: 'true' },
    { name: 'icon', type: '字符串', description: '按钮图标，使用Element Plus图标', example: '"Edit"' },

    // 图片属性
    { name: 'src', type: '字符串', description: '图片源地址', example: '"https://example.com/image.jpg"' },
    { name: 'alt', type: '字符串', description: '图片描述，用于无法显示图片时的替代文本', example: '"示例图片"' },
    { name: 'fit', type: '字符串', description: '图片如何适应容器，如fill、contain、cover等', example: '"cover"' },
    { name: 'lazy', type: '布尔值', description: '是否懒加载', example: 'true' },

    // 容器属性
    { name: 'direction', type: '字符串', description: '排列方向，如horizontal、vertical', example: '"vertical"' },
    { name: 'wrap', type: '布尔值', description: '是否换行，适用于flex布局', example: 'true' },
    { name: 'justify', type: '字符串', description: '水平排列方式，如start、end、center等', example: '"center"' },
    { name: 'align', type: '字符串', description: '垂直排列方式，如top、middle、bottom等', example: '"middle"' },
    { name: 'gutter', type: '数字', description: '栅格间隔', example: '20' },
    { name: 'span', type: '数字', description: '栅格占据的列数，最大24', example: '12' }
  ],
  notes: [
    'JSON格式必须符合规范，属性名和字符串值需要使用双引号',
    '布尔值(true/false)和数字不需要使用引号',
    '最后一个属性后面不要加逗号，否则会导致JSON解析错误',
    '可以使用在线JSON校验工具检查格式是否正确',
    '属性名称区分大小写，请确保与组件要求的属性名称完全一致'
  ]
}

// 样式格式说明
export const stylesFormatGuide = {
  title: '样式格式说明',
  description: '组件样式使用JSON格式定义，对应CSS样式属性',
  examples: [
    {
      title: '基础样式示例',
      code: `{
  "width": "100%",
  "height": "40px",
  "color": "#333333",
  "backgroundColor": "#f5f7fa",
  "fontSize": "14px",
  "fontWeight": "bold",
  "margin": "10px 0",
  "padding": "8px 12px"
}`,
      description: '基本的样式定义，包含尺寸、颜色、字体和边距'
    },
    {
      title: '边框和圆角示例',
      code: `{
  "border": "1px solid #dcdfe6",
  "borderRadius": "4px",
  "boxShadow": "0 2px 12px 0 rgba(0, 0, 0, 0.1)"
}`,
      description: '边框、圆角和阴影样式'
    },
    {
      title: '布局样式示例',
      code: `{
  "display": "flex",
  "flexDirection": "column",
  "justifyContent": "center",
  "alignItems": "center",
  "position": "relative",
  "zIndex": 10
}`,
      description: 'Flex布局和定位样式'
    }
  ],
  properties: [
    // 尺寸和布局
    { name: 'width', type: '字符串', description: '元素宽度，可以是像素值或百分比', example: '"100px"/"50%"/"auto"' },
    { name: 'height', type: '字符串', description: '元素高度，可以是像素值或百分比', example: '"40px"/"50%"/"auto"' },
    { name: 'minWidth', type: '字符串', description: '最小宽度，防止元素宽度小于该值', example: '"100px"' },
    { name: 'minHeight', type: '字符串', description: '最小高度，防止元素高度小于该值', example: '"50px"' },
    { name: 'maxWidth', type: '字符串', description: '最大宽度，防止元素宽度大于该值', example: '"500px"' },
    { name: 'maxHeight', type: '字符串', description: '最大高度，防止元素高度大于该值', example: '"300px"' },

    // 边距和填充
    { name: 'margin', type: '字符串', description: '外边距，元素与其他元素的间距', example: '"10px"/"10px 20px"/"10px 20px 10px 20px"' },
    { name: 'marginTop', type: '字符串', description: '上外边距', example: '"10px"' },
    { name: 'marginRight', type: '字符串', description: '右外边距', example: '"10px"' },
    { name: 'marginBottom', type: '字符串', description: '下外边距', example: '"10px"' },
    { name: 'marginLeft', type: '字符串', description: '左外边距', example: '"10px"' },
    { name: 'padding', type: '字符串', description: '内边距，元素内容与边框的间距', example: '"8px"/"8px 12px"/"8px 12px 8px 12px"' },
    { name: 'paddingTop', type: '字符串', description: '上内边距', example: '"8px"' },
    { name: 'paddingRight', type: '字符串', description: '右内边距', example: '"12px"' },
    { name: 'paddingBottom', type: '字符串', description: '下内边距', example: '"8px"' },
    { name: 'paddingLeft', type: '字符串', description: '左内边距', example: '"12px"' },

    // 边框和圆角
    { name: 'border', type: '字符串', description: '边框，包含宽度、样式和颜色', example: '"1px solid #dcdfe6"' },
    { name: 'borderWidth', type: '字符串', description: '边框宽度', example: '"1px"' },
    { name: 'borderStyle', type: '字符串', description: '边框样式，如solid、dashed、dotted等', example: '"solid"' },
    { name: 'borderColor', type: '字符串', description: '边框颜色', example: '"#dcdfe6"' },
    { name: 'borderRadius', type: '字符串', description: '边框圆角，可以是像素值或百分比', example: '"4px"/"50%"' },
    { name: 'borderTopLeftRadius', type: '字符串', description: '左上圆角', example: '"4px"' },
    { name: 'borderTopRightRadius', type: '字符串', description: '右上圆角', example: '"4px"' },
    { name: 'borderBottomLeftRadius', type: '字符串', description: '左下圆角', example: '"4px"' },
    { name: 'borderBottomRightRadius', type: '字符串', description: '右下圆角', example: '"4px"' },

    // 颜色和背景
    { name: 'color', type: '字符串', description: '文字颜色', example: '"#333"/"rgba(51,51,51,0.8)"' },
    { name: 'backgroundColor', type: '字符串', description: '背景颜色', example: '"#f5f7fa"/"rgba(245,247,250,0.9)"' },
    { name: 'backgroundImage', type: '字符串', description: '背景图片', example: '"url(image.jpg)"' },
    { name: 'backgroundSize', type: '字符串', description: '背景图片大小，如cover、contain等', example: '"cover"' },
    { name: 'backgroundPosition', type: '字符串', description: '背景图片位置', example: '"center"' },
    { name: 'backgroundRepeat', type: '字符串', description: '背景图片重复方式', example: '"no-repeat"' },
    { name: 'opacity', type: '数字', description: '不透明度，取值范0到1之间', example: '0.8' },

    // 字体样式
    { name: 'fontSize', type: '字符串', description: '字体大小', example: '"14px"/"1.2em"/"1.2rem"' },
    { name: 'fontWeight', type: '字符串/数字', description: '字体粗细，可以是数字或关键字', example: '"bold"/"normal"/400/700' },
    { name: 'fontFamily', type: '字符串', description: '字体系列', example: '"Arial, sans-serif"' },
    { name: 'lineHeight', type: '字符串/数字', description: '行高，控制文本行之间的间距', example: '"1.5"/"24px"' },
    { name: 'textAlign', type: '字符串', description: '文本对齐方式，如left、center、right', example: '"center"' },
    { name: 'textDecoration', type: '字符串', description: '文本装饰，如underline、line-through等', example: '"underline"' },
    { name: 'textOverflow', type: '字符串', description: '文本溢出处理方式，如ellipsis', example: '"ellipsis"' },

    // 布局和定位
    { name: 'display', type: '字符串', description: '显示类型，如flex、block、inline-block等', example: '"flex"' },
    { name: 'position', type: '字符串', description: '定位方式，如relative、absolute、fixed等', example: '"relative"' },
    { name: 'top', type: '字符串', description: '上定位位置', example: '"0"' },
    { name: 'right', type: '字符串', description: '右定位位置', example: '"0"' },
    { name: 'bottom', type: '字符串', description: '下定位位置', example: '"0"' },
    { name: 'left', type: '字符串', description: '左定位位置', example: '"0"' },
    { name: 'zIndex', type: '数字', description: '层叠顺序，值越大越上层', example: '10' },
    { name: 'overflow', type: '字符串', description: '内容溢出处理方式，如hidden、auto、scroll', example: '"hidden"' },

    // Flex布局
    { name: 'flexDirection', type: '字符串', description: 'Flex布局方向，如row、column', example: '"column"' },
    { name: 'justifyContent', type: '字符串', description: 'Flex主轴对齐方式', example: '"center"' },
    { name: 'alignItems', type: '字符串', description: 'Flex交叉轴对齐方式', example: '"center"' },
    { name: 'flexWrap', type: '字符串', description: 'Flex元素是否换行', example: '"wrap"' },
    { name: 'flex', type: '字符串', description: 'Flex元素如何增长和缩小', example: '"1"' },
    { name: 'gap', type: '字符串', description: 'Flex元素之间的间距', example: '"10px"' },

    // 其他样式
    { name: 'boxShadow', type: '字符串', description: '盒子阴影', example: '"0 2px 12px 0 rgba(0,0,0,0.1)"' },
    { name: 'transition', type: '字符串', description: '过渡效果，定义属性变化的速度', example: '"all 0.3s ease"' },
    { name: 'transform', type: '字符串', description: '变形，如旋转、缩放、移动等', example: '"scale(1.1)"' },
    { name: 'cursor', type: '字符串', description: '鼠标样式', example: '"pointer"' },
    { name: 'userSelect', type: '字符串', description: '是否可选中文本', example: '"none"' }
  ],
  notes: [
    'CSS属性名需要使用驼峰命名法，如backgroundColor而不是background-color',
    '所有的值都需要使用字符串格式，包括数字值（如："100px"而不是100px）',
    '颜色可以使用十六进制(#RRGGBB)、RGB(rgb(r,g,b))或RGBA(rgba(r,g,b,a))格式',
    '支持所有标准CSS属性，但某些复杂的CSS属性可能需要特殊处理',
    '单位不能省略，如需要写"100px"而不是"100"',
    '百分比值需要带引号，如："50%"'
  ]
}
