/**
 * 统一API导出文件
 * 集中导出所有API模块，方便统一引入
 */

// 导入所有API模块
import request from './index'
import { userApi } from './index'
import { projectApi } from './index'
import { pageApi } from './index'
import { documentApi } from './index'
import { componentApi } from './component'
import { componentCategoryApi } from './componentCategory'
import { componentEventApi } from './componentEvent'
import { componentEnableApi } from './componentEnable'
import { componentInitApi } from './componentInit'
import { codeApi } from './code'
import { iconApi } from './icon'

// 导出请求实例
export { request }

// 导出所有API模块
export {
  userApi,
  projectApi,
  pageApi,
  documentApi,
  componentApi,
  componentCategoryApi,
  componentEventApi,
  componentEnableApi,
  componentInitApi,
  codeApi,
  iconApi
}

// 默认导出所有API
export default {
  request,
  userApi,
  projectApi,
  pageApi,
  documentApi,
  componentApi,
  componentCategoryApi,
  componentEventApi,
  componentEnableApi,
  componentInitApi,
  codeApi,
  iconApi
}
