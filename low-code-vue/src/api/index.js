import axios from 'axios'
import { getToken } from '../utils/auth'
import router from '../router'
import { ElMessage } from 'element-plus'

// Import mock API for fallback
import { mockUserApi, mockProjectApi, mockPageApi } from './mock'

// Create axios instance
const request = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// Request interceptor
request.interceptors.request.use(
  config => {
    // Add token to request headers if available
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Response interceptor
request.interceptors.response.use(
  response => {
    // If the response type is blob, return the response directly
    if (response.config.responseType === 'blob') {
      return response.data
    }

    const res = response.data
    console.log('Response interceptor received:', res)

    // This check is now handled above

    // If the response doesn't have a code property, it might be a direct data response
    if (res && res.code === undefined) {
      console.log('Direct data response detected')
      return res
    }

    // If the status code is not 200, the API request has an error
    if (res.code !== 200) {
      // 401: Unauthorized or token expired
      if (res.code === 401) {
        ElMessage.error('Login expired, please login again')
        router.push('/login')
      } else {
        // Show error message
        ElMessage.error(res.message || 'Request failed')
      }

      return Promise.reject(new Error(res.message || 'Request failed'))
    } else {
      return res.data
    }
  },
  error => {
    // Handle network errors
    if (error.response) {
      // Server returned an error status code
      switch (error.response.status) {
        case 401:
          // Unauthorized, redirect to login
          ElMessage.error('Login expired, please login again')
          router.push('/login')
          break
        case 403:
          // Forbidden
          ElMessage.error('Insufficient permissions')
          break
        case 404:
          // Not found
          ElMessage.error('Resource not found')
          break
        case 500:
          // Server error
          console.error('Server error details:', error.response.data)
          ElMessage.error('Server error: ' + (error.response.data.message || 'Internal server error'))
          break
        default:
          // Other errors
          console.error('API error:', error.response)
          ElMessage.error(error.response.data.message || 'Request failed')
      }
    } else if (error.request) {
      // Request was made but no response received
      console.error('Network error:', error.request)
      ElMessage.error('Network error, please check your connection')
    } else {
      // Request configuration error
      console.error('Request error:', error.message)
      ElMessage.error(error.message || 'Request configuration error')
    }

    return Promise.reject(error)
  }
)

// User API
export const userApi = {
  // Login
  async login(data) {
    try {
      return await request({
        url: '/user/login',
        method: 'post',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for login')
      return mockUserApi.login(data)
    }
  },

  // Register
  async register(data) {
    try {
      return await request({
        url: '/user/register',
        method: 'post',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for register')
      return mockUserApi.register(data)
    }
  },

  // Get user info
  async getUserInfo() {
    try {
      return await request({
        url: '/user/info',
        method: 'get'
      })
    } catch (error) {
      console.warn('Falling back to mock data for getUserInfo')
      return mockUserApi.getUserInfo()
    }
  },

  // Update user info
  async updateUserInfo(data) {
    try {
      return await request({
        url: '/user/info',
        method: 'put',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for updateUserInfo')
      // Mock API might not have this method, so we'll just return the data
      return data
    }
  },

  // Change password
  async changePassword(data) {
    try {
      return await request({
        url: '/user/password',
        method: 'post',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for changePassword')
      // Mock API might not have this method, so we'll just return success
      return { success: true }
    }
  },

  // Logout
  async logout() {
    try {
      return await request({
        url: '/user/logout',
        method: 'post'
      })
    } catch (error) {
      console.warn('Falling back to mock data for logout')
      // Mock API might not have this method, so we'll just return success
      return { success: true }
    }
  }
}

// Project API
export const projectApi = {
  // Get project list
  async getProjectList(params) {
    try {
      return await request({
        url: '/project/page',
        method: 'get',
        params
      })
    } catch (error) {
      console.warn('Falling back to mock data for getProjectList')
      return mockProjectApi.getProjectList(params)
    }
  },

  // Get project detail
  async getProjectDetail(id) {
    try {
      return await request({
        url: `/project/${id}`,
        method: 'get'
      })
    } catch (error) {
      console.warn('Falling back to mock data for getProjectDetail')
      return mockProjectApi.getProjectDetail(id)
    }
  },

  // Create project
  async createProject(data) {
    try {
      return await request({
        url: '/project',
        method: 'post',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for createProject')
      return mockProjectApi.createProject(data)
    }
  },

  // Update project
  async updateProject(id, data) {
    try {
      return await request({
        url: `/project/${id}`,
        method: 'put',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for updateProject')
      return mockProjectApi.updateProject(id, data)
    }
  },

  // Delete project
  async deleteProject(id) {
    try {
      return await request({
        url: `/project/${id}`,
        method: 'delete'
      })
    } catch (error) {
      console.warn('Falling back to mock data for deleteProject')
      return mockProjectApi.deleteProject(id)
    }
  }
}

// Page API
export const pageApi = {
  // Get page list
  async getPageList(projectId) {
    try {
      return await request({
        url: '/page',
        method: 'get',
        params: { projectId }
      })
    } catch (error) {
      console.warn('Falling back to mock data for getPageList')
      return mockPageApi.getPageList(projectId)
    }
  },

  // Get page detail
  async getPageDetail(id) {
    try {
      return await request({
        url: `/page/${id}`,
        method: 'get'
      })
    } catch (error) {
      console.warn('Falling back to mock data for getPageDetail')
      return mockPageApi.getPageDetail(id)
    }
  },

  // Create page
  async createPage(data) {
    try {
      return await request({
        url: '/page',
        method: 'post',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for createPage')
      return mockPageApi.createPage(data)
    }
  },

  // Update page
  async updatePage(id, data) {
    try {
      return await request({
        url: `/page/${id}`,
        method: 'put',
        data
      })
    } catch (error) {
      console.warn('Falling back to mock data for updatePage')
      return mockPageApi.updatePage(id, data)
    }
  },

  // Delete page
  async deletePage(id) {
    try {
      return await request({
        url: `/page/${id}`,
        method: 'delete'
      })
    } catch (error) {
      console.warn('Falling back to mock data for deletePage')
      return mockPageApi.deletePage(id)
    }
  },

  // Update page sort
  async updatePageSort(id, sort) {
    try {
      return await request({
        url: `/page/${id}/sort`,
        method: 'put',
        params: { sort }
      })
    } catch (error) {
      console.warn('Falling back to mock data for updatePageSort')
      return mockPageApi.updatePageSort(id, sort)
    }
  }
}

// Document API
export const documentApi = {
  // Export project document
  exportProjectDocument(projectId, format) {
    return request({
      url: `/document/project/${projectId}`,
      method: 'get',
      params: { format },
      responseType: 'blob'
    })
  },

  // Export page document
  exportPageDocument(pageId, format) {
    return request({
      url: `/document/page/${pageId}`,
      method: 'get',
      params: { format },
      responseType: 'blob'
    })
  }
}

export default request
