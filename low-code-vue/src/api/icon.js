import request from './index'
import { ElMessage } from 'element-plus'

// Base URL for API requests
const API_URL = '/api'

// Icon API
export const iconApi = {
  /**
   * Upload an icon
   * 
   * @param {File} file - The icon file to upload
   * @returns {Promise<Object>} - The uploaded icon information
   */
  async uploadIcon(file) {
    try {
      // Create form data
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await request({
        url: `${API_URL}/icons/upload`,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      if (response && response.code === 200 && response.data) {
        ElMessage.success('Icon uploaded successfully')
        return response.data
      }
      
      ElMessage.error(response?.message || 'Failed to upload icon')
      return null
    } catch (error) {
      console.error('Failed to upload icon:', error)
      ElMessage.error(error.response?.data?.message || 'Failed to upload icon')
      return null
    }
  },
  
  /**
   * Get the full URL for an icon
   * 
   * @param {string} filename - The icon filename
   * @returns {string} - The full URL for the icon
   */
  getIconUrl(filename) {
    if (!filename) return ''
    
    // If the filename is already a full URL, return it as is
    if (filename.startsWith('http://') || filename.startsWith('https://')) {
      return filename
    }
    
    // If the filename is a path, return the full URL
    if (filename.startsWith('/')) {
      return `${window.location.origin}${filename}`
    }
    
    // Otherwise, assume it's just a filename and construct the URL
    return `${window.location.origin}${API_URL}/icons/${filename}`
  }
}

export default iconApi
