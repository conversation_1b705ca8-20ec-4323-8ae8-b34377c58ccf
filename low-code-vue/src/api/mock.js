// Mock API services for development

// Mock data
const mockProjects = [
  {
    id: '1',
    name: 'E-commerce Website',
    description: 'Online shopping platform with product catalog and checkout',
    createTime: '2023-06-01T10:00:00Z',
    updateTime: '2023-06-10T15:30:00Z'
  },
  {
    id: '2',
    name: 'Company Portfolio',
    description: 'Corporate website with about us, services and contact pages',
    createTime: '2023-05-15T09:00:00Z',
    updateTime: '2023-06-05T11:20:00Z'
  }
];

const mockPages = [
  {
    id: '101',
    projectId: '1',
    name: 'Home Page',
    title: 'Welcome to Our Store',
    path: '/home',
    config: JSON.stringify({
      components: [
        {
          id: 'text-1',
          type: 'text',
          props: { content: 'Welcome to our online store!' },
          styles: { fontSize: '24px', textAlign: 'center', margin: '20px 0' }
        },
        {
          id: 'image-1',
          type: 'image',
          props: { src: 'https://via.placeholder.com/800x400', alt: 'Hero Image' },
          styles: { width: '100%', height: 'auto', margin: '10px 0' }
        },
        {
          id: 'button-1',
          type: 'button',
          props: { text: 'Shop Now', type: 'primary' },
          styles: { margin: '20px auto', display: 'block' },
          events: [
            {
              type: 'click',
              action: {
                type: 'navigate',
                navigationType: 'page',
                pageId: '102',
                projectId: '1'
              }
            }
          ]
        }
      ]
    }),
    createTime: '2023-06-01T10:30:00Z',
    updateTime: '2023-06-10T16:00:00Z'
  },
  {
    id: '102',
    projectId: '1',
    name: 'Product List',
    title: 'Our Products',
    path: '/products',
    config: JSON.stringify({
      components: [
        {
          id: 'text-1',
          type: 'text',
          props: { content: 'Our Products' },
          styles: { fontSize: '24px', textAlign: 'center', margin: '20px 0' }
        }
      ]
    }),
    createTime: '2023-06-02T11:00:00Z',
    updateTime: '2023-06-09T14:20:00Z'
  }
];

// Helper function to simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Project API
export const mockProjectApi = {
  // Get all projects
  async getProjectList() {
    await delay(300);
    return {
      list: [...mockProjects],
      total: mockProjects.length
    };
  },

  // Get project by ID
  async getProjectDetail(id) {
    await delay(200);
    return mockProjects.find(p => p.id === id) || null;
  },

  // Create new project
  async createProject(project) {
    await delay(400);
    const newProject = {
      id: Date.now().toString(),
      ...project,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
    mockProjects.push(newProject);
    return newProject;
  },

  // Update project
  async updateProject(id, project) {
    await delay(300);
    const index = mockProjects.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error('Project not found');
    }

    mockProjects[index] = {
      ...mockProjects[index],
      ...project,
      updateTime: new Date().toISOString()
    };

    return mockProjects[index];
  },

  // Delete project
  async deleteProject(id) {
    await delay(300);
    const index = mockProjects.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error('Project not found');
    }

    mockProjects.splice(index, 1);
    return { success: true };
  }
};

// Page API
export const mockPageApi = {
  // Get pages by project ID
  async getPageList(projectId) {
    await delay(300);
    const pages = mockPages.filter(p => p.projectId === projectId);
    return {
      list: pages,
      total: pages.length
    };
  },

  // Get page by ID
  async getPageDetail(id) {
    await delay(200);
    return mockPages.find(p => p.id === id) || null;
  },

  // Create new page
  async createPage(page) {
    await delay(400);
    const newPage = {
      id: Date.now().toString(),
      ...page,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
    mockPages.push(newPage);
    return newPage;
  },

  // Update page
  async updatePage(id, page) {
    await delay(300);
    console.log('Mock API: Updating page with ID:', id);
    console.log('Mock API: Page data received:', page);

    const index = mockPages.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error('Page not found');
    }

    // 保存原始配置
    const originalConfig = mockPages[index].config;
    console.log('Mock API: Original config:', originalConfig);

    // 先保存原始页面对象
    const originalPage = { ...mockPages[index] };

    // 更新页面对象
    mockPages[index] = {
      ...originalPage,
      ...page,
      updateTime: new Date().toISOString()
    };

    // 检查新配置
    console.log('Mock API: Updated page:', mockPages[index]);
    console.log('Mock API: New config:', mockPages[index].config);

    // 如果新配置是字符串，尝试解析并检查按钮组件
    if (typeof mockPages[index].config === 'string') {
      try {
        const config = JSON.parse(mockPages[index].config);
        console.log('Mock API: Parsed config:', config);

        if (config.components) {
          const buttonComponents = config.components.filter(c => c.type === 'button');
          if (buttonComponents.length > 0) {
            console.log('Mock API: Button components in config:', buttonComponents);
            buttonComponents.forEach((btn, i) => {
              console.log(`Mock API: Button ${i} events:`, btn.events);

              // 确保按钮组件的事件配置被保存
              if (btn.events) {
                console.log(`Mock API: Button ${i} has events:`, JSON.stringify(btn.events));
              } else {
                console.warn(`Mock API: Button ${i} has no events, initializing empty array`);
                btn.events = [];
              }
            });
          }
        }

        // 将解析后的配置重新序列化并保存
        mockPages[index].config = JSON.stringify(config);
        console.log('Mock API: Re-serialized config:', mockPages[index].config);
      } catch (e) {
        console.error('Mock API: Failed to parse config:', e);
      }
    }

    return mockPages[index];
  },

  // Delete page
  async deletePage(id) {
    await delay(300);
    const index = mockPages.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error('Page not found');
    }

    mockPages.splice(index, 1);
    return { success: true };
  },

  // Update page sort
  async updatePageSort(id, sort) {
    await delay(200);
    const index = mockPages.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error('Page not found');
    }

    mockPages[index].sort = sort;
    return { success: true };
  }
};

// User API
export const mockUserApi = {
  // Login
  async login(credentials) {
    await delay(500);

    // Mock login logic
    if (credentials.username === 'admin' && credentials.password === 'admin123') {
      return {
        token: 'mock-jwt-token',
        user: {
          id: '1',
          username: 'admin',
          nickname: 'Administrator',
          role: 'admin'
        }
      };
    }

    throw new Error('Invalid credentials');
  },

  // Register
  async register(userData) {
    await delay(600);

    // Mock register logic
    return {
      token: 'mock-jwt-token',
      user: {
        id: Date.now().toString(),
        username: userData.username,
        nickname: userData.nickname || userData.username,
        role: 'user'
      }
    };
  },

  // Get user info
  async getUserInfo() {
    await delay(200);

    return {
      id: '1',
      username: 'admin',
      nickname: 'Administrator',
      role: 'admin'
    };
  }
};
