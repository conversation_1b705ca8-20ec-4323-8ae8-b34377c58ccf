import request from './index'

// Code generation API
export const codeApi = {
  // Generate Vue code
  generateVueCode(pageConfig) {
    return request({
      url: '/code/vue',
      method: 'post',
      data: pageConfig
    })
  },

  // Generate UniApp code
  generateUniAppCode(pageConfig) {
    return request({
      url: '/code/uniapp',
      method: 'post',
      data: pageConfig
    })
  },

  // Export project code
  exportProjectCode(projectId, framework = 'vue') {
    console.log('API call - exportProjectCode:', { projectId, framework })
    return request({
      url: '/code/export',
      method: 'post',
      data: { projectId, framework },
      responseType: 'blob'
    })
  },

  // Generate preview code
  generatePreviewCode(pageConfig) {
    return request({
      url: '/code/preview',
      method: 'post',
      data: pageConfig
    })
  }
}

export default codeApi
