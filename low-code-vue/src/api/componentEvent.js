import request from './index'
import { ElMessage } from 'element-plus'

/**
 * 组件事件 API
 */
export const componentEventApi = {
  /**
   * 获取组件事件配置
   * @param {number} id 组件ID
   * @returns {Promise<Object>} 组件事件配置
   */
  async getComponentEvents(id) {
    try {
      const response = await request({
        url: `/component-events/components/${id}`,
        method: 'get'
      })
      
      return response
    } catch (error) {
      console.error(`Failed to get component events for component ${id}:`, error)
      throw error
    }
  },
  
  /**
   * 更新组件事件配置
   * @param {number} id 组件ID
   * @param {Array} events 事件配置数组
   * @returns {Promise<Object>} 更新后的组件
   */
  async updateComponentEvents(id, events) {
    try {
      const response = await request({
        url: `/component-events/components/${id}`,
        method: 'put',
        data: { events: JSON.stringify(events) }
      })
      
      ElMessage.success('组件事件配置已更新')
      return response
    } catch (error) {
      console.error(`Failed to update component events for component ${id}:`, error)
      ElMessage.error('更新组件事件配置失败')
      throw error
    }
  },
  
  /**
   * 清空组件事件配置
   * @param {number} id 组件ID
   * @returns {Promise<Object>} 更新后的组件
   */
  async clearComponentEvents(id) {
    try {
      const response = await request({
        url: `/component-events/components/${id}`,
        method: 'delete'
      })
      
      ElMessage.success('组件事件配置已清空')
      return response
    } catch (error) {
      console.error(`Failed to clear component events for component ${id}:`, error)
      ElMessage.error('清空组件事件配置失败')
      throw error
    }
  },
  
  /**
   * 批量更新组件事件配置
   * @param {Array<Object>} batchData 批量更新数据，每项包含id和events
   * @returns {Promise<Object>} 批量更新结果
   */
  async batchUpdateComponentEvents(batchData) {
    try {
      const response = await request({
        url: `/component-events/components/batch`,
        method: 'put',
        data: batchData.map(item => ({
          id: item.id,
          events: JSON.stringify(item.events)
        }))
      })
      
      ElMessage.success('批量更新组件事件配置成功')
      return response
    } catch (error) {
      console.error('Failed to batch update component events:', error)
      ElMessage.error('批量更新组件事件配置失败')
      throw error
    }
  },
  
  /**
   * 获取可用事件类型
   * @returns {Promise<Array>} 事件类型列表
   */
  async getEventTypes() {
    try {
      const response = await request({
        url: `/component-events/event-types`,
        method: 'get'
      })
      
      return response
    } catch (error) {
      console.error('Failed to get event types:', error)
      throw error
    }
  },
  
  /**
   * 获取可用动作类型
   * @returns {Promise<Array>} 动作类型列表
   */
  async getActionTypes() {
    try {
      const response = await request({
        url: `/component-events/action-types`,
        method: 'get'
      })
      
      return response
    } catch (error) {
      console.error('Failed to get action types:', error)
      throw error
    }
  }
}
