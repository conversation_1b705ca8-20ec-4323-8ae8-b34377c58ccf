import request from './index'
import { ElMessage } from 'element-plus'

/**
 * 组件初始化 API
 */
export const componentInitApi = {
  /**
   * 初始化容器组件
   * @returns {Promise<Object>} 初始化结果
   */
  async initContainers() {
    try {
      const response = await request({
        url: '/component-init/containers',
        method: 'post'
      })
      
      ElMessage.success('容器组件初始化成功')
      return response
    } catch (error) {
      console.error('Failed to initialize container components:', error)
      ElMessage.error('容器组件初始化失败: ' + (error.message || '未知错误'))
      throw error
    }
  },
  
  /**
   * 更新间距组件
   * @returns {Promise<Object>} 更新结果
   */
  async updateSpace() {
    try {
      const response = await request({
        url: '/component-init/update-space',
        method: 'post'
      })
      
      ElMessage.success('间距组件更新成功')
      return response
    } catch (error) {
      console.error('Failed to update space component:', error)
      ElMessage.error('间距组件更新失败: ' + (error.message || '未知错误'))
      throw error
    }
  }
}

export default componentInitApi
