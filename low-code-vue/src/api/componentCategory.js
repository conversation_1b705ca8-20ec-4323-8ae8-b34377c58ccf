import request from './index'
import { ElMessage } from 'element-plus'

// Base URL for API requests is already set in the request instance

// Component Category API
export const componentCategoryApi = {
  // Get all component categories
  async getAllCategories() {
    try {
      console.log('Fetching component categories from API...')
      const response = await request({
        url: `/component-categories`,
        method: 'get'
      })
      console.log('API response for categories:', response)

      // Check if response is an array (already processed by interceptor)
      if (Array.isArray(response)) {
        return response
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        return response.data
      }
      // If response has a code and data property (original format)
      else if (response && response.code === 200 && response.data) {
        return response.data
      } else {
        console.warn('Unexpected API response format:', response)
        return []
      }
    } catch (error) {
      console.error('Failed to get component categories:', error)
      ElMessage.error('Failed to get component categories')
      return []
    }
  },

  // Get component category by code
  async getCategoryByCode(code) {
    try {
      const response = await request({
        url: `/component-categories/${code}`,
        method: 'get'
      })

      // Check if response is an object (already processed by interceptor)
      if (response && !response.data && !response.code) {
        return response
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        return response.data
      }
      // If response has a code and data property (original format)
      else if (response && response.code === 200 && response.data) {
        return response.data
      } else {
        console.warn('Unexpected API response format:', response)
        return null
      }
    } catch (error) {
      console.error(`Failed to get component category by code ${code}:`, error)
      return null
    }
  },

  // Create component category
  async createCategory(category) {
    try {
      const response = await request({
        url: `/component-categories`,
        method: 'post',
        data: category
      })

      // Check if response is an object (already processed by interceptor)
      if (response && !response.data && !response.code) {
        ElMessage.success('Component category created successfully')
        return response
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        ElMessage.success('Component category created successfully')
        return response.data
      }
      // If response has a code and data property (original format)
      else if (response && response.code === 200 && response.data) {
        ElMessage.success('Component category created successfully')
        return response.data
      } else {
        console.warn('Unexpected API response format:', response)
        ElMessage.error('Failed to create component category')
        return null
      }
    } catch (error) {
      console.error('Failed to create component category:', error)
      ElMessage.error(error.response?.data?.message || 'Failed to create component category')
      throw error
    }
  },

  // Update component category
  async updateCategory(id, category) {
    try {
      const response = await request({
        url: `/component-categories/${id}`,
        method: 'put',
        data: category
      })

      // Check if response is an object (already processed by interceptor)
      if (response && !response.data && !response.code) {
        ElMessage.success('Component category updated successfully')
        return response
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        ElMessage.success('Component category updated successfully')
        return response.data
      }
      // If response has a code and data property (original format)
      else if (response && response.code === 200 && response.data) {
        ElMessage.success('Component category updated successfully')
        return response.data
      } else {
        console.warn('Unexpected API response format:', response)
        ElMessage.error('Failed to update component category')
        return null
      }
    } catch (error) {
      console.error(`Failed to update component category ${id}:`, error)
      ElMessage.error(error.response?.data?.message || 'Failed to update component category')
      throw error
    }
  },

  // Delete component category
  async deleteCategory(id) {
    try {
      const response = await request({
        url: `/component-categories/${id}`,
        method: 'delete'
      })

      // Log the response for debugging
      console.log('Delete category response:', response)

      ElMessage.success('Component category deleted successfully')
      return true
    } catch (error) {
      console.error(`Failed to delete component category ${id}:`, error)
      ElMessage.error(error.response?.data?.message || 'Failed to delete component category')
      throw error
    }
  }
}

export default componentCategoryApi
