import { request } from './index'
import { ElMessage } from 'element-plus'

/**
 * 组件启用/禁用 API
 */
export const componentEnableApi = {
  /**
   * 启用组件
   * @param {number} id 组件ID
   * @returns {Promise<Object>} 启用后的组件
   */
  async enableComponent(id) {
    try {
      const response = await request({
        url: `/component-enable/components/${id}/enable`,
        method: 'put'
      })
      
      ElMessage.success('组件已启用')
      return response
    } catch (error) {
      console.error(`Failed to enable component ${id}:`, error)
      ElMessage.error('启用组件失败')
      throw error
    }
  },
  
  /**
   * 禁用组件
   * @param {number} id 组件ID
   * @returns {Promise<Object>} 禁用后的组件
   */
  async disableComponent(id) {
    try {
      const response = await request({
        url: `/component-enable/components/${id}/disable`,
        method: 'put'
      })
      
      ElMessage.success('组件已禁用')
      return response
    } catch (error) {
      console.error(`Failed to disable component ${id}:`, error)
      ElMessage.error('禁用组件失败')
      throw error
    }
  },
  
  /**
   * 批量启用组件
   * @param {Array<number>} ids 组件ID数组
   * @returns {Promise<Object>} 批量启用结果
   */
  async batchEnableComponents(ids) {
    try {
      const response = await request({
        url: `/component-enable/components/batch-enable`,
        method: 'put',
        data: ids
      })
      
      ElMessage.success('批量启用组件成功')
      return response
    } catch (error) {
      console.error('Failed to batch enable components:', error)
      ElMessage.error('批量启用组件失败')
      throw error
    }
  },
  
  /**
   * 批量禁用组件
   * @param {Array<number>} ids 组件ID数组
   * @returns {Promise<Object>} 批量禁用结果
   */
  async batchDisableComponents(ids) {
    try {
      const response = await request({
        url: `/component-enable/components/batch-disable`,
        method: 'put',
        data: ids
      })
      
      ElMessage.success('批量禁用组件成功')
      return response
    } catch (error) {
      console.error('Failed to batch disable components:', error)
      ElMessage.error('批量禁用组件失败')
      throw error
    }
  },
  
  /**
   * 启用组件分类
   * @param {number} id 分类ID
   * @returns {Promise<Object>} 启用后的分类
   */
  async enableCategory(id) {
    try {
      const response = await request({
        url: `/component-enable/categories/${id}/enable`,
        method: 'put'
      })
      
      ElMessage.success('分类已启用')
      return response
    } catch (error) {
      console.error(`Failed to enable category ${id}:`, error)
      ElMessage.error('启用分类失败')
      throw error
    }
  },
  
  /**
   * 禁用组件分类
   * @param {number} id 分类ID
   * @returns {Promise<Object>} 禁用后的分类
   */
  async disableCategory(id) {
    try {
      const response = await request({
        url: `/component-enable/categories/${id}/disable`,
        method: 'put'
      })
      
      ElMessage.success('分类已禁用')
      return response
    } catch (error) {
      console.error(`Failed to disable category ${id}:`, error)
      ElMessage.error('禁用分类失败')
      throw error
    }
  },
  
  /**
   * 批量启用组件分类
   * @param {Array<number>} ids 分类ID数组
   * @returns {Promise<Object>} 批量启用结果
   */
  async batchEnableCategories(ids) {
    try {
      const response = await request({
        url: `/component-enable/categories/batch-enable`,
        method: 'put',
        data: ids
      })
      
      ElMessage.success('批量启用分类成功')
      return response
    } catch (error) {
      console.error('Failed to batch enable categories:', error)
      ElMessage.error('批量启用分类失败')
      throw error
    }
  },
  
  /**
   * 批量禁用组件分类
   * @param {Array<number>} ids 分类ID数组
   * @returns {Promise<Object>} 批量禁用结果
   */
  async batchDisableCategories(ids) {
    try {
      const response = await request({
        url: `/component-enable/categories/batch-disable`,
        method: 'put',
        data: ids
      })
      
      ElMessage.success('批量禁用分类成功')
      return response
    } catch (error) {
      console.error('Failed to batch disable categories:', error)
      ElMessage.error('批量禁用分类失败')
      throw error
    }
  }
}
