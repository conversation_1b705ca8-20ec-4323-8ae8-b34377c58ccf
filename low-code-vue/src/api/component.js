import request from './index'
import { ElMessage } from 'element-plus'

// Mock component data for fallback
const mockComponents = [
  {
    id: 1,
    name: '文本',
    type: 'text',
    category: 'basic',
    icon: 'Document',
    props: JSON.stringify({
      content: '文本内容'
    }),
    styles: JSON.stringify({
      fontSize: '16px',
      color: '#333',
      textAlign: 'left',
      margin: '0'
    }),
    isSystem: 1,
    createTime: '2023-01-01T00:00:00',
    updateTime: '2023-01-01T00:00:00'
  },
  {
    id: 2,
    name: '图片',
    type: 'image',
    category: 'basic',
    icon: 'Picture',
    props: JSON.stringify({
      src: 'https://via.placeholder.com/300x200',
      alt: '图片'
    }),
    styles: JSON.stringify({
      width: '100%',
      height: 'auto',
      margin: '10px 0'
    }),
    isSystem: 1,
    createTime: '2023-01-01T00:00:00',
    updateTime: '2023-01-01T00:00:00'
  },
  {
    id: 3,
    name: '按钮',
    type: 'button',
    category: 'basic',
    icon: 'Pointer',
    props: JSON.stringify({
      text: '按钮',
      type: 'primary'
    }),
    styles: JSON.stringify({
      margin: '10px 0'
    }),
    isSystem: 1,
    createTime: '2023-01-01T00:00:00',
    updateTime: '2023-01-01T00:00:00'
  }
]

// Component API
export const componentApi = {
  // Get all components
  async getAllComponents() {
    try {
      console.log('Fetching components from API...')
      const response = await request({
        url: '/components',
        method: 'get'
      })
      console.log('API response:', response)

      // Check if response is an array (already processed by interceptor)
      if (Array.isArray(response)) {
        return response.map(component => transformComponent(component))
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        return response.data.map(component => transformComponent(component))
      } else {
        console.warn('Unexpected API response format:', response)
        throw new Error('Unexpected API response format')
      }
    } catch (error) {
      console.warn('Falling back to mock data for getAllComponents:', error)
      // Return empty array instead of mock data
      return []
    }
  },

  // Get components by category
  async getComponentsByCategory(category) {
    try {
      const response = await request({
        url: '/components',
        method: 'get',
        params: { category }
      })

      // Check if response is an array (already processed by interceptor)
      if (Array.isArray(response)) {
        return response.map(component => transformComponent(component))
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        return response.data.map(component => transformComponent(component))
      } else {
        console.warn('Unexpected API response format:', response)
        throw new Error('Unexpected API response format')
      }
    } catch (error) {
      console.warn(`Falling back to mock data for getComponentsByCategory: ${category}`, error)
      return mockComponents
        .filter(c => c.category === category)
        .map(component => transformComponent(component))
    }
  },

  // Get component by type
  async getComponentByType(type) {
    try {
      const response = await request({
        url: '/components',
        method: 'get',
        params: { type }
      })

      // Check if response is an array (already processed by interceptor)
      if (Array.isArray(response)) {
        if (response.length > 0) {
          return transformComponent(response[0])
        }
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data && response.data.length > 0) {
        return transformComponent(response.data[0])
      }

      return null
    } catch (error) {
      console.warn(`Falling back to mock data for getComponentByType: ${type}`, error)
      const mockComponent = mockComponents.find(c => c.type === type)
      return mockComponent ? transformComponent(mockComponent) : null
    }
  },

  // Create component
  async createComponent(component) {
    try {
      // Transform the component to match the backend format
      const backendComponent = transformComponentToBackend(component)

      const response = await request({
        url: '/components',
        method: 'post',
        data: backendComponent
      })

      // Check if response is an object (already processed by interceptor)
      if (response && !response.data) {
        return transformComponent(response)
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        return transformComponent(response.data)
      } else {
        console.warn('Unexpected API response format:', response)
        throw new Error('Unexpected API response format')
      }
    } catch (error) {
      console.error('Failed to create component:', error)
      ElMessage.error('Failed to create component')
      throw error
    }
  },

  // Update component
  async updateComponent(component) {
    try {
      // Transform the component to match the backend format
      const backendComponent = transformComponentToBackend(component)

      const response = await request({
        url: `/components/${component.id}`,
        method: 'put',
        data: backendComponent
      })

      // Check if response is an object (already processed by interceptor)
      if (response && !response.data) {
        return transformComponent(response)
      }
      // If response has a data property (not processed by interceptor)
      else if (response && response.data) {
        return transformComponent(response.data)
      } else {
        console.warn('Unexpected API response format:', response)
        throw new Error('Unexpected API response format')
      }
    } catch (error) {
      console.error('Failed to update component:', error)
      ElMessage.error('Failed to update component')
      throw error
    }
  },

  // Delete component
  async deleteComponent(id) {
    try {
      const response = await request({
        url: `/components/${id}`,
        method: 'delete'
      })

      // Log the response for debugging
      console.log('Delete component response:', response)

      ElMessage.success('Component deleted successfully')
      return true
    } catch (error) {
      console.error('Failed to delete component:', error)
      ElMessage.error('Failed to delete component')
      throw error
    }
  }
}

// Helper function to transform a backend component to frontend format
function transformComponent(backendComponent) {
  console.log('Transforming backend component:', backendComponent)
  if (!backendComponent) return null

  try {
    // Parse JSON strings
    const props = backendComponent.props ? JSON.parse(backendComponent.props) : {}
    const styles = backendComponent.styles ? JSON.parse(backendComponent.styles) : {}

    // Create frontend component
    return {
      id: backendComponent.id,
      type: backendComponent.type,
      name: backendComponent.name,
      category: backendComponent.category || 'custom',
      icon: backendComponent.icon || 'Document',
      defaultProps: props,
      defaultStyles: styles,
      isSystem: backendComponent.isSystem === 1,
      enabled: backendComponent.enabled // Preserve enabled status
    }
  } catch (error) {
    console.error('Error transforming component:', error)
    return {
      id: backendComponent.id,
      type: backendComponent.type,
      name: backendComponent.name,
      category: backendComponent.category || 'custom',
      icon: backendComponent.icon || 'Document',
      defaultProps: {},
      defaultStyles: {},
      isSystem: backendComponent.isSystem === 1,
      enabled: backendComponent.enabled // Preserve enabled status
    }
  }
}

// Helper function to transform a frontend component to backend format
function transformComponentToBackend(frontendComponent) {
  return {
    id: frontendComponent.id,
    name: frontendComponent.name,
    type: frontendComponent.type,
    category: frontendComponent.category || 'custom',
    icon: frontendComponent.icon || 'Document',
    props: JSON.stringify(frontendComponent.defaultProps || {}),
    styles: JSON.stringify(frontendComponent.defaultStyles || {}),
    isSystem: frontendComponent.isSystem ? 1 : 0,
    enabled: frontendComponent.enabled // Preserve enabled status
  }
}

export default componentApi
