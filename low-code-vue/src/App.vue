<script setup>
import { onMounted } from 'vue'
import { useEditorStore } from './store'
import { ElLoading, ElMessage } from 'element-plus'

const editorStore = useEditorStore()

// Load components from database when app starts
onMounted(async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: 'Loading component library...',
    background: 'rgba(255, 255, 255, 0.7)'
  })

  try {
    // First, load components from the API
    const loaded = await editorStore.loadComponentsFromDb()

    // If no components were loaded, initialize with default components
    if (!loaded) {
      console.log('No components found in database, initializing with defaults')
      await editorStore.initializeDefaultComponents()
    }
  } catch (error) {
    console.error('Failed to load component library:', error)
    ElMessage.error('Failed to load component library')
  } finally {
    loadingInstance.close()
  }
})
</script>

<template>
  <div class="app-container">
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<style>
.app-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Global styles */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  color: #333;
}

* {
  box-sizing: border-box;
}
</style>
