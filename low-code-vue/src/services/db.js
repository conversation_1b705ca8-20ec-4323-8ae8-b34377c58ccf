import { ElMessage } from 'element-plus'
import { sanitizeComponent, sanitizeForIndexedDB } from '../utils/sanitize'

// IndexedDB database configuration
const DB_NAME = 'low-code-db'
const DB_VERSION = 2 // Increased version for new store
const PROJECTS_STORE = 'projects'
const PAGES_STORE = 'pages'
const COMPONENTS_STORE = 'components'
const COMPONENT_LIBRARY_STORE = 'componentLibrary'

// Initialize the database
const initDB = () => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION)

    request.onerror = (event) => {
      console.error('Database error:', event.target.error)
      reject(event.target.error)
    }

    request.onsuccess = (event) => {
      const db = event.target.result
      resolve(db)
    }

    request.onupgradeneeded = (event) => {
      const db = event.target.result
      const oldVersion = event.oldVersion

      // Create projects store
      if (!db.objectStoreNames.contains(PROJECTS_STORE)) {
        const projectsStore = db.createObjectStore(PROJECTS_STORE, { keyPath: 'id' })
        projectsStore.createIndex('name', 'name', { unique: false })
        projectsStore.createIndex('createdAt', 'createdAt', { unique: false })
      }

      // Create pages store
      if (!db.objectStoreNames.contains(PAGES_STORE)) {
        const pagesStore = db.createObjectStore(PAGES_STORE, { keyPath: 'id' })
        pagesStore.createIndex('projectId', 'projectId', { unique: false })
        pagesStore.createIndex('name', 'name', { unique: false })
        pagesStore.createIndex('updatedAt', 'updatedAt', { unique: false })
      }

      // Create components store
      if (!db.objectStoreNames.contains(COMPONENTS_STORE)) {
        const componentsStore = db.createObjectStore(COMPONENTS_STORE, { keyPath: 'id' })
        componentsStore.createIndex('pageId', 'pageId', { unique: false })
        componentsStore.createIndex('updatedAt', 'updatedAt', { unique: false })
      }

      // Create component library store (added in version 2)
      if (oldVersion < 2 && !db.objectStoreNames.contains(COMPONENT_LIBRARY_STORE)) {
        const componentLibraryStore = db.createObjectStore(COMPONENT_LIBRARY_STORE, { keyPath: 'type' })
        componentLibraryStore.createIndex('category', 'category', { unique: false })
        componentLibraryStore.createIndex('name', 'name', { unique: false })
        componentLibraryStore.createIndex('updatedAt', 'updatedAt', { unique: false })
      }
    }
  })
}

// Get database connection
const getDB = async () => {
  try {
    return await initDB()
  } catch (error) {
    console.error('Failed to initialize database:', error)
    ElMessage.error('数据库初始化失败')
    throw error
  }
}

// Project operations
export const projectService = {
  // Create a new project
  async createProject(project) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PROJECTS_STORE], 'readwrite')
      const store = transaction.objectStore(PROJECTS_STORE)

      // Add created timestamp
      const projectWithTimestamp = {
        ...project,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const request = store.add(projectWithTimestamp)

      request.onsuccess = () => {
        resolve(projectWithTimestamp)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Get all projects
  async getAllProjects() {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PROJECTS_STORE], 'readonly')
      const store = transaction.objectStore(PROJECTS_STORE)
      const request = store.getAll()

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Get project by ID
  async getProjectById(id) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PROJECTS_STORE], 'readonly')
      const store = transaction.objectStore(PROJECTS_STORE)
      const request = store.get(id)

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Update project
  async updateProject(project) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PROJECTS_STORE], 'readwrite')
      const store = transaction.objectStore(PROJECTS_STORE)

      // Add updated timestamp
      const projectWithTimestamp = {
        ...project,
        updatedAt: new Date().toISOString()
      }

      const request = store.put(projectWithTimestamp)

      request.onsuccess = () => {
        resolve(projectWithTimestamp)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Delete project
  async deleteProject(id) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PROJECTS_STORE, PAGES_STORE, COMPONENTS_STORE], 'readwrite')
      const projectStore = transaction.objectStore(PROJECTS_STORE)
      const pagesStore = transaction.objectStore(PAGES_STORE)
      const componentsStore = transaction.objectStore(COMPONENTS_STORE)

      // Get all pages for this project
      const pagesIndex = pagesStore.index('projectId')
      const pagesRequest = pagesIndex.getAll(id)

      pagesRequest.onsuccess = () => {
        const pages = pagesRequest.result

        // Delete all components for each page
        pages.forEach(page => {
          const componentsIndex = componentsStore.index('pageId')
          const componentsRequest = componentsIndex.getAll(page.id)

          componentsRequest.onsuccess = () => {
            const components = componentsRequest.result
            components.forEach(component => {
              componentsStore.delete(component.id)
            })
          }

          // Delete the page
          pagesStore.delete(page.id)
        })

        // Delete the project
        const request = projectStore.delete(id)

        request.onsuccess = () => {
          resolve(true)
        }

        request.onerror = (event) => {
          reject(event.target.error)
        }
      }

      pagesRequest.onerror = (event) => {
        reject(event.target.error)
      }
    })
  }
}

// Page operations
export const pageService = {
  // Create a new page
  async createPage(page) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PAGES_STORE], 'readwrite')
      const store = transaction.objectStore(PAGES_STORE)

      // Add created timestamp
      const pageWithTimestamp = {
        ...page,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const request = store.add(pageWithTimestamp)

      request.onsuccess = () => {
        resolve(pageWithTimestamp)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Get all pages for a project
  async getPagesByProjectId(projectId) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PAGES_STORE], 'readonly')
      const store = transaction.objectStore(PAGES_STORE)
      const index = store.index('projectId')
      const request = index.getAll(projectId)

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Get page by ID
  async getPageById(id) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PAGES_STORE], 'readonly')
      const store = transaction.objectStore(PAGES_STORE)
      const request = store.get(id)

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Update page
  async updatePage(page) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PAGES_STORE], 'readwrite')
      const store = transaction.objectStore(PAGES_STORE)

      // Add updated timestamp
      const pageWithTimestamp = {
        ...page,
        updatedAt: new Date().toISOString()
      }

      const request = store.put(pageWithTimestamp)

      request.onsuccess = () => {
        resolve(pageWithTimestamp)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Delete page
  async deletePage(id) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([PAGES_STORE, COMPONENTS_STORE], 'readwrite')
      const pagesStore = transaction.objectStore(PAGES_STORE)
      const componentsStore = transaction.objectStore(COMPONENTS_STORE)

      // Delete all components for this page
      const componentsIndex = componentsStore.index('pageId')
      const componentsRequest = componentsIndex.getAll(id)

      componentsRequest.onsuccess = () => {
        const components = componentsRequest.result
        components.forEach(component => {
          componentsStore.delete(component.id)
        })

        // Delete the page
        const request = pagesStore.delete(id)

        request.onsuccess = () => {
          resolve(true)
        }

        request.onerror = (event) => {
          reject(event.target.error)
        }
      }

      componentsRequest.onerror = (event) => {
        reject(event.target.error)
      }
    })
  }
}

// Component operations
export const componentService = {
  // Save components for a page
  async savePageComponents(pageId, components) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([COMPONENTS_STORE], 'readwrite')
      const store = transaction.objectStore(COMPONENTS_STORE)

      // First, delete all existing components for this page
      const index = store.index('pageId')
      const getAllRequest = index.getAll(pageId)

      getAllRequest.onsuccess = () => {
        const existingComponents = getAllRequest.result

        // Delete all existing components
        const deletePromises = existingComponents.map(component => {
          return new Promise((resolveDelete, rejectDelete) => {
            const deleteRequest = store.delete(component.id)
            deleteRequest.onsuccess = () => resolveDelete()
            deleteRequest.onerror = (event) => rejectDelete(event.target.error)
          })
        })

        // After deleting, add all new components
        Promise.all(deletePromises)
          .then(() => {
            const timestamp = new Date().toISOString()

            // Sanitize components before saving
            const sanitizedComponents = components.map(component => {
              // Sanitize the component to make it serializable
              const sanitizedComponent = sanitizeComponent(component);

              // Add metadata
              return {
                ...sanitizedComponent,
                pageId,
                updatedAt: timestamp
              };
            });

            const addPromises = sanitizedComponents.map(component => {
              return new Promise((resolveAdd, rejectAdd) => {
                try {
                  const addRequest = store.add(component)
                  addRequest.onsuccess = () => resolveAdd(component)
                  addRequest.onerror = (event) => {
                    console.error('Error adding component:', event.target.error);
                    console.error('Component that caused error:', JSON.stringify(component));
                    rejectAdd(event.target.error);
                  }
                } catch (error) {
                  console.error('Exception while adding component:', error);
                  console.error('Component that caused exception:', JSON.stringify(component));
                  rejectAdd(error);
                }
              })
            })

            Promise.all(addPromises)
              .then(savedComponents => {
                resolve(savedComponents)
              })
              .catch(error => {
                console.error('Failed to save components:', error);
                reject(error)
              })
          })
          .catch(error => {
            console.error('Failed to delete existing components:', error);
            reject(error)
          })
      }

      getAllRequest.onerror = (event) => {
        console.error('Failed to get existing components:', event.target.error);
        reject(event.target.error)
      }
    })
  },

  // Get all components for a page
  async getComponentsByPageId(pageId) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([COMPONENTS_STORE], 'readonly')
      const store = transaction.objectStore(COMPONENTS_STORE)
      const index = store.index('pageId')
      const request = index.getAll(pageId)

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  }
}

// Component Library operations
export const componentLibraryService = {
  // Get all components in the library
  async getAllComponents() {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([COMPONENT_LIBRARY_STORE], 'readonly')
      const store = transaction.objectStore(COMPONENT_LIBRARY_STORE)
      const request = store.getAll()

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Get components by category
  async getComponentsByCategory(category) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([COMPONENT_LIBRARY_STORE], 'readonly')
      const store = transaction.objectStore(COMPONENT_LIBRARY_STORE)
      const index = store.index('category')
      const request = index.getAll(category)

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Get component by type
  async getComponentByType(type) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([COMPONENT_LIBRARY_STORE], 'readonly')
      const store = transaction.objectStore(COMPONENT_LIBRARY_STORE)
      const request = store.get(type)

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Add or update component in library
  async saveComponent(component) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([COMPONENT_LIBRARY_STORE], 'readwrite')
      const store = transaction.objectStore(COMPONENT_LIBRARY_STORE)

      try {
        // Sanitize the component to make it serializable
        const sanitizedComponent = sanitizeForIndexedDB(component);

        // Add updated timestamp
        const componentWithTimestamp = {
          ...sanitizedComponent,
          updatedAt: new Date().toISOString()
        }

        const request = store.put(componentWithTimestamp)

        request.onsuccess = () => {
          resolve(componentWithTimestamp)
        }

        request.onerror = (event) => {
          console.error('Error saving component to library:', event.target.error);
          console.error('Component that caused error:', JSON.stringify(componentWithTimestamp));
          reject(event.target.error)
        }
      } catch (error) {
        console.error('Exception while saving component to library:', error);
        console.error('Component that caused exception:', JSON.stringify(component));
        reject(error);
      }
    })
  },

  // Delete component from library
  async deleteComponent(type) {
    const db = await getDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([COMPONENT_LIBRARY_STORE], 'readwrite')
      const store = transaction.objectStore(COMPONENT_LIBRARY_STORE)
      const request = store.delete(type)

      request.onsuccess = () => {
        resolve(true)
      }

      request.onerror = (event) => {
        reject(event.target.error)
      }
    })
  },

  // Initialize default components if library is empty
  async initializeDefaultComponents(defaultComponents) {
    try {
      const existingComponents = await this.getAllComponents()

      // Only initialize if the library is empty
      if (existingComponents.length === 0) {
        // Process components in batches to avoid overwhelming IndexedDB
        const batchSize = 10;
        const batches = [];

        // Split components into batches
        for (let i = 0; i < defaultComponents.length; i += batchSize) {
          batches.push(defaultComponents.slice(i, i + batchSize));
        }

        // Process each batch sequentially
        for (const batch of batches) {
          const savePromises = batch.map(component => {
            // Sanitize the component before saving
            const sanitizedComponent = sanitizeForIndexedDB(component);
            return this.saveComponent({
              ...sanitizedComponent,
              updatedAt: new Date().toISOString()
            });
          });

          await Promise.all(savePromises);
        }

        console.log('Default components initialized')
        return true
      }

      return false
    } catch (error) {
      console.error('Failed to initialize default components:', error)
      return false
    }
  }
}

// Export a default object with all services
export default {
  projectService,
  pageService,
  componentService,
  componentLibraryService
}
