import { createRouter, createWebHistory } from 'vue-router'
import { isLoggedIn } from '../utils/auth'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/project',
    name: 'ProjectList',
    component: () => import('../views/project/ProjectList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/project/:id',
    name: 'ProjectDetail',
    component: () => import('../views/project/ProjectDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/editor/:projectId/:pageId',
    name: 'Editor',
    component: () => import('../views/editor/Editor.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/preview/:projectId/:pageId',
    name: 'Preview',
    component: () => import('../views/preview/Preview.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/component-library',
    name: 'ComponentLibrary',
    component: () => import('../views/component-library/ComponentLibrary.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/component-enable',
    name: 'ComponentEnable',
    component: () => import('../views/ComponentEnableView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/init-containers',
    name: 'InitContainers',
    component: () => import('../views/component-library/InitContainers.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/add-containers',
    name: 'AddContainers',
    component: () => import('../views/component-library/AddContainers.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/update-space',
    name: 'UpdateSpace',
    component: () => import('../views/component-library/UpdateSpace.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/add-input-components',
    name: 'AddInputComponents',
    component: () => import('../views/component-library/AddInputComponents.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/component-designer',
    name: 'ComponentDesigner',
    component: () => import('../views/component-library/ComponentDesigner.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/image-management',
    name: 'ImageManagement',
    component: () => import('../views/ImageManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/test-upload',
    name: 'TestImageUpload',
    component: () => import('../views/TestImageUpload.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard
router.beforeEach((to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  if (requiresAuth && !isLoggedIn()) {
    // Save the intended destination for redirect after login
    localStorage.setItem('redirect', to.fullPath)
    next('/login')
  } else {
    next()
  }
})

export default router
