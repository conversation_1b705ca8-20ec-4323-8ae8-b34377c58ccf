/**
 * dom-fix.js
 * 这个脚本使用MutationObserver直接修改DOM，为预览模式下的组件添加包装元素
 * 它可以直接在HTML中引入，不需要修改Vue代码
 */

(function() {
  // 配置
  const config = {
    // 要包装的组件选择器
    componentSelectors: [
      '.button-component',
      '.input-component',
      '.select-component'
      // 添加其他需要包装的组件选择器
    ],
    // 包装元素的样式
    wrapperStyle: 'display: contents;',
    // 调试模式
    debug: true
  };
  
  // 日志函数
  function log(message) {
    if (config.debug) {
      console.log(`[DOM-Fix] ${message}`);
    }
  }
  
  // 检查是否是预览模式
  function isPreviewMode() {
    return window.location.href.includes('preview') || 
           document.body.classList.contains('preview-mode') ||
           document.getElementById('preview') !== null;
  }
  
  // 检查元素是否匹配选择器
  function matchesSelector(element) {
    return config.componentSelectors.some(selector => {
      try {
        return element.matches(selector);
      } catch (e) {
        return false;
      }
    });
  }
  
  // 检查元素是否已经被包装
  function isAlreadyWrapped(element) {
    let parent = element.parentElement;
    let wrappedCount = 0;
    
    // 检查三层父元素
    for (let i = 0; i < 3; i++) {
      if (!parent || 
          (!parent.classList.contains('component-wrapper') && 
           !parent.classList.contains('event-handler-wrapper') &&
           !parent.classList.contains('preview-fix'))) {
        return false;
      }
      parent = parent.parentElement;
      wrappedCount++;
    }
    
    return wrappedCount >= 2; // 至少需要两层包装
  }
  
  // 为元素添加包装
  function wrapElement(element) {
    // 如果元素已经被包装，跳过
    if (isAlreadyWrapped(element)) {
      return;
    }
    
    log(`包装元素: ${element.className}`);
    
    try {
      // 创建包装元素
      const wrapper1 = document.createElement('div');
      wrapper1.className = 'component-wrapper preview-fix';
      wrapper1.setAttribute('style', config.wrapperStyle);
      
      const wrapper2 = document.createElement('div');
      wrapper2.className = 'component-wrapper preview-fix';
      wrapper2.setAttribute('style', config.wrapperStyle);
      
      const wrapper3 = document.createElement('div');
      wrapper3.className = 'event-handler-wrapper preview-fix';
      wrapper3.setAttribute('style', config.wrapperStyle);
      
      // 获取元素的父元素
      const parent = element.parentElement;
      if (!parent) return;
      
      // 替换元素
      parent.replaceChild(wrapper1, element);
      wrapper1.appendChild(wrapper2);
      wrapper2.appendChild(wrapper3);
      wrapper3.appendChild(element);
      
      log('包装成功');
    } catch (e) {
      console.error('[DOM-Fix] 包装元素时出错:', e);
    }
  }
  
  // 处理DOM树
  function processDomTree(root) {
    // 查找所有匹配的元素
    config.componentSelectors.forEach(selector => {
      try {
        const elements = root.querySelectorAll(selector);
        log(`找到 ${elements.length} 个匹配 "${selector}" 的元素`);
        
        elements.forEach(element => {
          wrapElement(element);
        });
      } catch (e) {
        console.error(`[DOM-Fix] 处理选择器 "${selector}" 时出错:`, e);
      }
    });
  }
  
  // 设置MutationObserver
  function setupMutationObserver() {
    // 创建一个观察器实例
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        // 检查是否有新节点被添加
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            // 检查添加的节点是否是元素节点
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查节点本身是否匹配选择器
              if (matchesSelector(node)) {
                wrapElement(node);
              }
              
              // 处理节点的子树
              processDomTree(node);
            }
          });
        }
      });
    });
    
    // 配置观察选项
    const observerConfig = { 
      childList: true, 
      subtree: true 
    };
    
    // 开始观察文档
    observer.observe(document.body, observerConfig);
    log('MutationObserver 已设置');
    
    return observer;
  }
  
  // 主函数
  function init() {
    // 检查是否是预览模式
    if (!isPreviewMode()) {
      log('不是预览模式，跳过修复');
      return;
    }
    
    log('开始应用DOM修复');
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        processDomTree(document);
        setupMutationObserver();
      });
    } else {
      processDomTree(document);
      setupMutationObserver();
    }
  }
  
  // 启动
  init();
})();
