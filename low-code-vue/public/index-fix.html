<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>低代码平台</title>
  
  <!-- 预览模式修复脚本 - 在页面加载前执行 -->
  <script>
    // 检查是否是预览模式
    if (window.location.href.includes('preview')) {
      // 创建样式元素
      var style = document.createElement('style');
      style.textContent = `
        /* 为预览模式下的组件添加必要的样式 */
        .button-component {
          /* 添加与编辑模式相同的样式 */
          margin: inherit;
          padding: inherit;
          position: relative;
          box-sizing: border-box;
        }
        
        /* 创建一个辅助函数，在DOM加载后修复组件结构 */
        function fixComponentStructure() {
          // 查找所有按钮组件
          var components = document.querySelectorAll('.button-component');
          
          // 为每个组件添加包装元素
          components.forEach(function(component) {
            // 创建包装元素
            var wrapper1 = document.createElement('div');
            wrapper1.className = 'component-wrapper preview-fix';
            wrapper1.style.display = 'contents';
            
            var wrapper2 = document.createElement('div');
            wrapper2.className = 'component-wrapper preview-fix';
            wrapper2.style.display = 'contents';
            
            var wrapper3 = document.createElement('div');
            wrapper3.className = 'event-handler-wrapper preview-fix';
            wrapper3.style.display = 'contents';
            
            // 获取组件的父元素
            var parent = component.parentElement;
            
            // 替换组件
            if (parent) {
              parent.replaceChild(wrapper1, component);
              wrapper1.appendChild(wrapper2);
              wrapper2.appendChild(wrapper3);
              wrapper3.appendChild(component);
            }
          });
          
          console.log('[PreviewFix] 已修复组件结构');
        }
        
        // 在DOM加载后执行修复
        document.addEventListener('DOMContentLoaded', fixComponentStructure);
        
        // 使用MutationObserver监听DOM变化
        var observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              fixComponentStructure();
            }
          });
        });
        
        // 在DOM加载后开始观察
        document.addEventListener('DOMContentLoaded', function() {
          observer.observe(document.body, { childList: true, subtree: true });
        });
      `;
      
      // 添加样式到文档头部
      document.head.appendChild(style);
      
      // 加载外部修复脚本
      var script = document.createElement('script');
      script.src = './dom-fix.js';
      document.head.appendChild(script);
      
      console.log('[PreviewFix] 已加载预览模式修复脚本');
    }
  </script>
  
  <!-- 其他资源 -->
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <!-- 其他CSS和JavaScript资源 -->
</head>
<body>
  <noscript>
    <strong>We're sorry but this app doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>
</html>
