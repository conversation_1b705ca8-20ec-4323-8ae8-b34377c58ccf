/**
 * previewFix.js
 * 这个脚本可以直接添加到HTML页面，用于修复预览模式下组件结构与编辑模式不一致的问题
 */

(function() {
  /**
   * 为预览模式下的组件添加包装元素
   * @param {string} selector - 要包装的组件选择器，例如 '.button-component'
   */
  function wrapComponentsInPreview(selector = '.button-component') {
    // 确保DOM已加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => doWrapComponents(selector));
    } else {
      doWrapComponents(selector);
    }
  }

  /**
   * 实际执行包装操作的函数
   * @param {string} selector - 要包装的组件选择器
   */
  function doWrapComponents(selector) {
    // 查找所有匹配的组件
    const components = document.querySelectorAll(selector);
    
    // 为每个组件添加包装元素
    components.forEach(component => {
      // 检查组件是否已经被包装
      if (!isAlreadyWrapped(component)) {
        wrapComponent(component);
      }
    });
    
    // 设置MutationObserver以处理动态添加的组件
    setupMutationObserver(selector);
  }

  /**
   * 检查组件是否已经被包装
   * @param {Element} component - 要检查的组件元素
   * @returns {boolean} - 如果已经被包装则返回true
   */
  function isAlreadyWrapped(component) {
    let parent = component.parentElement;
    
    // 检查三层父元素
    for (let i = 0; i < 3; i++) {
      if (!parent || !parent.classList.contains('component-wrapper') && !parent.classList.contains('event-handler-wrapper')) {
        return false;
      }
      parent = parent.parentElement;
    }
    
    return true;
  }

  /**
   * 为组件添加包装元素
   * @param {Element} component - 要包装的组件元素
   */
  function wrapComponent(component) {
    // 创建包装元素
    const wrapper1 = document.createElement('div');
    wrapper1.className = 'component-wrapper preview-fix';
    wrapper1.style.display = 'contents';
    
    const wrapper2 = document.createElement('div');
    wrapper2.className = 'component-wrapper preview-fix';
    wrapper2.style.display = 'contents';
    
    const wrapper3 = document.createElement('div');
    wrapper3.className = 'event-handler-wrapper preview-fix';
    wrapper3.style.display = 'contents';
    
    // 获取组件的父元素
    const parent = component.parentElement;
    
    // 替换组件
    parent.replaceChild(wrapper1, component);
    wrapper1.appendChild(wrapper2);
    wrapper2.appendChild(wrapper3);
    wrapper3.appendChild(component);
  }

  /**
   * 设置MutationObserver以处理动态添加的组件
   * @param {string} selector - 要包装的组件选择器
   */
  function setupMutationObserver(selector) {
    // 创建一个观察器实例
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        // 检查是否有新节点被添加
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            // 检查添加的节点是否是元素节点
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查节点本身是否匹配选择器
              if (node.matches && node.matches(selector) && !isAlreadyWrapped(node)) {
                wrapComponent(node);
              }
              
              // 检查节点的子元素是否匹配选择器
              const childComponents = node.querySelectorAll(selector);
              childComponents.forEach(component => {
                if (!isAlreadyWrapped(component)) {
                  wrapComponent(component);
                }
              });
            }
          });
        }
      });
    });
    
    // 配置观察选项
    const config = { childList: true, subtree: true };
    
    // 开始观察文档
    observer.observe(document.body, config);
    
    // 返回观察器实例，以便可以在需要时停止观察
    return observer;
  }

  // 在页面加载完成后应用修复
  window.addEventListener('load', () => {
    // 包装按钮组件
    wrapComponentsInPreview('.button-component');
    
    // 可以添加其他需要包装的组件类型
    // wrapComponentsInPreview('.input-component');
    // wrapComponentsInPreview('.select-component');
    // 等等...
    
    console.log('[PreviewFix] 已应用组件结构修复');
  });
})();
