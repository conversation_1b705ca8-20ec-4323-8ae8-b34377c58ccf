# 测试MinIO回退机制

## 🔧 问题修复

已修复的问题：
1. **MinIO可用性检测方法**：修复了 `isMinioAvailable()` 方法的逻辑
2. **回退机制**：增强了MinIO失败时的回退逻辑
3. **日志输出**：添加了详细的调试日志
4. **错误处理**：改进了异常处理和错误信息

## 🚀 测试步骤

### 1. 确保MinIO未启动

```bash
# 检查MinIO是否在运行
docker ps | grep minio

# 如果有MinIO容器在运行，停止它
docker stop <minio-container-id>
```

### 2. 重启后端服务

```bash
# 重启Spring Boot应用
mvn spring-boot:run
```

### 3. 查看启动日志

启动时应该看到类似的日志：
```log
INFO: MinIO可用性检测结果: false
INFO: MinIO不可用，选择本地存储
INFO: 本地存储基础目录创建成功: ./uploads
```

### 4. 测试图片上传

访问测试页面：`http://localhost:3000/test-upload`

1. **测试存储连接**：
   - 点击"测试存储连接"按钮
   - 应该显示：
     ```json
     {
       "currentStorageType": "local",
       "minioStatus": "UNAVAILABLE",
       "localStatus": "AVAILABLE",
       "fallbackToLocal": true
     }
     ```

2. **上传图片**：
   - 选择一张图片上传
   - 查看控制台日志，应该看到：
     ```log
     INFO: 开始上传文件，选择的存储类型: local, 文件路径: uploads/2024/01/15/xxx.png
     INFO: 使用本地存储上传文件: uploads/2024/01/15/xxx.png
     INFO: 本地文件上传成功: uploads/2024/01/15/xxx.png
     ```

3. **验证文件存储**：
   - 检查 `./uploads` 目录是否创建了文件
   - 访问返回的URL是否能正常显示图片

## 📊 预期结果

### 成功的日志输出

```log
# 启动时
INFO: MinIO可用性检测结果: false
INFO: MinIO不可用，选择本地存储
INFO: 本地存储基础目录创建成功: ./uploads

# 上传时
INFO: 接收到图片上传请求: 文件名=test.png, 大小=123456, 分类=2
INFO: 开始上传文件到存储服务: uploads/2024/01/15/xxx.png
INFO: 开始上传文件，选择的存储类型: local, 文件路径: uploads/2024/01/15/xxx.png
INFO: 使用本地存储上传文件: uploads/2024/01/15/xxx.png
INFO: 本地文件上传成功: uploads/2024/01/15/xxx.png
INFO: 文件上传成功，生成URL: http://localhost:8080/api/files/uploads/2024/01/15/xxx.png, 存储类型: local
INFO: 图片上传成功: ID=1, URL=http://localhost:8080/api/files/uploads/2024/01/15/xxx.png
```

### 成功的前端响应

```javascript
// 控制台输出
开始上传图片: {fileName: "test.png", fileSize: 123456, category: 2}
后端响应完整数据: {code: 200, message: "操作成功", data: {...}}
图片上传成功，返回数据: {id: 1, url: "http://localhost:8080/api/files/uploads/2024/01/15/xxx.png", ...}
图片URL: http://localhost:8080/api/files/uploads/2024/01/15/xxx.png
```

## 🔍 故障排除

### 如果仍然报错

1. **检查配置**：
   ```properties
   file.storage.type=auto
   file.storage.fallback-to-local=true
   ```

2. **检查目录权限**：
   ```bash
   # 确保应用有写权限
   chmod 755 ./uploads
   ```

3. **查看详细日志**：
   - 检查 `FileStorageServiceImpl` 的DEBUG日志
   - 确认存储类型选择过程

4. **手动测试本地存储**：
   ```bash
   # 创建测试目录
   mkdir -p ./uploads/test
   echo "test" > ./uploads/test/test.txt
   ```

## 🎯 测试MinIO回退机制

### 测试场景1：MinIO完全不可用

1. 确保MinIO未启动
2. 上传图片
3. 应该自动使用本地存储

### 测试场景2：MinIO启动后再停止

1. 启动MinIO：
   ```bash
   docker run -p 9000:9000 -p 9001:9001 \
     -e "MINIO_ROOT_USER=minioadmin" \
     -e "MINIO_ROOT_PASSWORD=minioadmin" \
     minio/minio server /data --console-address ":9001"
   ```

2. 上传一张图片（应该使用MinIO）

3. 停止MinIO：
   ```bash
   docker stop <minio-container-id>
   ```

4. 再上传一张图片（应该回退到本地存储）

### 测试场景3：强制使用本地存储

1. 修改配置：
   ```properties
   file.storage.type=local
   ```

2. 重启应用
3. 上传图片（应该直接使用本地存储）

## 📁 文件结构验证

上传成功后，检查文件结构：

```
./uploads/
└── uploads/
    └── 2024/
        └── 01/
            └── 15/
                └── abc123def456.png
```

访问URL：`http://localhost:8080/api/files/uploads/2024/01/15/abc123def456.png`

## ✅ 成功标志

1. **无MinIO错误**：不再出现MinIO连接失败的异常
2. **本地文件创建**：在 `./uploads` 目录中能看到上传的文件
3. **URL可访问**：返回的URL能正常显示图片
4. **数据库记录**：图片记录正确保存，备注中包含存储类型信息

---

**修复状态**: ✅ 已完成  
**测试方法**: 上述步骤  
**预期结果**: MinIO不可用时自动使用本地存储
