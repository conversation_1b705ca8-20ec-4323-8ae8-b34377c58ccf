需要基于uniapp开发的低代码可拖拽生成页面的平台，后端基于jdk17和springboot3.4.2版本搭建框架，mybaits-plus实现数据库查询，druild连接池连接mysql数据库，mysql数据库名称low-code，用户名root密码123456，swagger3作为后端接口文档，项目使用中文注释，后端是基于Maven构建的，不通过gradlew去构建项目，前端项目的根目录是C:\Users\<USER>\IdeaProjects\low-code\low-code-web，前端项目需求基础功能需求：
可视化拖拽编辑
组件库（按钮、表单、列表、轮播图等）
自由拖拽布局（网格、流式、绝对定位）
实时预览（PC/移动端多端适配）
组件属性配置
样式调整（颜色、尺寸、间距等）
数据绑定（静态数据、动态API接口）
事件交互（点击、滑动等触发动作）
多端适配与发布
一键生成H5、小程序、APP多端代码
自动处理跨端兼容性问题
页面管理与数据源
页面树形结构管理（增删改查）
数据源管理（本地JSON、REST API对接）
模板与扩展能力
预制模板（行业场景模板库）
自定义组件扩展（支持用户上传组件）
代码导出与协作
导出Vue/Uniapp标准代码
团队协作（权限管理、版本历史）

前端技术栈
框架：Vue3 + Uniapp（兼容多端）
拖拽库：VueDraggable、SortableJS 或自研拖拽引擎
状态管理：Pinia/Vuex
UI库：Uni-UI 或 Element Plus（适配管理后台）
核心功能实现
JSON Schema：存储页面配置（组件树、样式、数据绑定）
代码生成器：将JSON配置转为Uniapp代码（Vue模板+JS）
沙箱环境：实时预览（iframe或Web Worker隔离）
辅助工具
Monaco Editor：嵌入代码编辑器（高级用户自定义逻辑）
WebSocket：实现多人实时协作编辑