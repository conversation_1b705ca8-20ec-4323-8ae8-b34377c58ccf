<template>
  <div class="page-container">
    <!-- This is how the button is currently generated (with issue) -->
    <el-button type="primary" :style='{"margin":"10px 0"}' @click="() => {console.log('Button clicked!');console.log('Navigating to page ID: 12');navigateTo({ name: '12' });}" data-event-type="click" data-action-type="navigate" data-page-id="12">按钮</el-button>
    
    <!-- This is how it should be generated (fixed version) -->
    <el-button type="primary" :style='{"margin":"10px 0"}' @click="() => {
      console.log('Button clicked!');
      console.log('Navigating to page ID: 12');
      navigateTo({ name: '12' });
    }">按钮</el-button>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// Page: TestButton

const route = useRoute()
const router = useRouter()

const state = reactive({
  pageTitle: 'Test Button',
  pagePath: '/test-button',
})

const { pageTitle, pagePath } = toRefs(state)

const initPage = () => {
  console.log('Page initialized:', pageTitle.value)
}

// 全局错误处理函数
const handleError = (error, source) => {
  console.error(`Error in ${source}:`, error)
  ElMessage.error(`操作失败: ${error.message || error}`)
}

// 页面导航函数
const navigateTo = (target) => {
  console.log('Navigating to:', target)
  try {
    // 检查目标是否为字符串或对象
    const routeTarget = typeof target === 'string' ? target : target
    console.log('Route target:', routeTarget)
    
    // 尝试不同的导航方式
    router.push(routeTarget)
      .then(() => {
        console.log('Navigation successful')
      })
      .catch(err => {
        console.error('Navigation failed with router.push:', err)
        console.log('Trying alternative navigation method...')
        
        // 如果失败，尝试使用路径
        if (typeof target === 'object' && target.name) {
          const path = '/' + target.name
          console.log('Trying path-based navigation:', path)
          router.push(path)
            .then(() => console.log('Path-based navigation successful'))
            .catch(pathErr => {
              console.error('Path-based navigation also failed:', pathErr)
              ElMessage.error(`页面跳转失败: ${pathErr.message}`)
            })
        } else {
          ElMessage.error(`页面跳转失败: ${err.message}`)
        }
      })
  } catch (e) {
    handleError(e, 'navigateTo')
  }
}

onMounted(() => {
  console.log('Page mounted:', pageTitle.value)
  try {
    initPage()
  } catch (e) {
    handleError(e, 'onMounted')
  }
})
</script>

<style scoped>
.page-container {
  padding: 20px;
  min-height: 100vh;
  width: 100%;
}
</style>
