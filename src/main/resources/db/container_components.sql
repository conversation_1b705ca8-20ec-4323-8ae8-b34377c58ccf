-- 添加卡片容器组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '卡片容器', 'card-container', 'layout', 'Document', '{"title":"卡片标题","shadow":"always","children":[]}', '{"width":"100%","margin":"10px 0","padding":"0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'card-container');

-- 添加标签页容器组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '标签页容器', 'tab-container', 'layout', 'Menu', '{"tabs":[{"title":"标签页1","name":"tab1","children":[]},{"title":"标签页2","name":"tab2","children":[]}],"activeName":"tab1"}', '{"width":"100%","margin":"10px 0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'tab-container');

-- 添加折叠面板容器组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '折叠面板容器', 'collapse-container', 'layout', 'ArrowDown', '{"items":[{"title":"面板1","name":"panel1","children":[]},{"title":"面板2","name":"panel2","children":[]}],"activeNames":["panel1"]}', '{"width":"100%","margin":"10px 0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'collapse-container');

-- 添加网格容器组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '网格容器', 'grid-container', 'layout', 'Grid', '{"cols":3,"gutter":20,"children":[]}', '{"width":"100%","margin":"10px 0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'grid-container');
