-- 添加数字输入框组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '数字输入框', 'number-input', 'form', 'Odometer', '{"defaultValue":0,"min":-Infinity,"max":Infinity,"step":1,"precision":0,"stepStrictly":false,"controls":true,"disabled":false,"placeholder":"请输入数字"}', '{"width":"100%","margin":"0 0 15px 0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'number-input');

-- 添加密码输入框组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '密码输入框', 'password-input', 'form', 'Lock', '{"defaultValue":"","placeholder":"请输入密码","showPasswordToggle":true,"clearable":true,"disabled":false}', '{"width":"100%","margin":"0 0 15px 0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'password-input');

-- 添加搜索输入框组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '搜索输入框', 'search-input', 'form', 'Search', '{"defaultValue":"","placeholder":"请输入搜索内容","showPrefix":true,"showButton":true,"buttonText":"搜索","buttonType":"primary","clearable":true,"disabled":false}', '{"width":"100%","margin":"0 0 15px 0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'search-input');

-- 更新文本域组件
UPDATE `component` 
SET `props` = '{"defaultValue":"","placeholder":"请输入内容","rows":4,"autosize":false,"minRows":2,"maxRows":6,"maxlength":0,"showWordLimit":false,"clearable":true,"disabled":false}'
WHERE `type` = 'textarea' AND EXISTS (SELECT 1 FROM `component` WHERE `type` = 'textarea');

-- 如果文本域组件不存在，则添加
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '文本域', 'textarea', 'form', 'Document', '{"defaultValue":"","placeholder":"请输入内容","rows":4,"autosize":false,"minRows":2,"maxRows":6,"maxlength":0,"showWordLimit":false,"clearable":true,"disabled":false}', '{"width":"100%","margin":"0 0 15px 0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'textarea');
