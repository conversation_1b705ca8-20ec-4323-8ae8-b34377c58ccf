-- Create component table if not exists
CREATE TABLE IF NOT EXISTS `component` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '组件ID',
    `name` varchar(100) NOT NULL COMMENT '组件名称',
    `type` varchar(50) NOT NULL COMMENT '组件类型',
    `category` varchar(50) DEFAULT NULL COMMENT '组件分类',
    `icon` varchar(50) DEFAULT NULL COMMENT '组件图标',
    `props` text DEFAULT NULL COMMENT '组件属性配置（JSON格式）',
    `events` text DEFAULT NULL COMMENT '组件事件配置（JSON格式）',
    `styles` text DEFAULT NULL COMMENT '组件样式配置（JSON格式）',
    `template` text DEFAULT NULL COMMENT '组件模板代码',
    `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统内置（0-否，1-是）',
    `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用（0-禁用，1-启用）',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组件表';

-- Insert default components if not exists
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '文本', 'text', 'basic', 'Document', '{"content":"文本内容"}', '{"fontSize":"16px","color":"#333","textAlign":"left","margin":"0"}', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'text');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '标题', 'heading', 'basic', 'Reading', '{"content":"标题文本","level":2}', '{"fontSize":"24px","fontWeight":"bold","color":"#333","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'heading');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '段落', 'paragraph', 'basic', 'Document', '{"content":"这是一个段落文本，可以包含多行内容。"}', '{"fontSize":"16px","color":"#666","lineHeight":"1.5","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'paragraph');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '图片', 'image', 'basic', 'Picture', '{"src":"https://via.placeholder.com/300x200","alt":"图片"}', '{"width":"100%","height":"auto","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'image');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '按钮', 'button', 'basic', 'Pointer', '{"text":"按钮","type":"primary"}', '{"margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'button');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '链接', 'link', 'basic', 'Link', '{"text":"链接文本","href":"#","target":"_self"}', '{"color":"#409eff","textDecoration":"none"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'link');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '分割线', 'divider', 'basic', 'Minus', '{"direction":"horizontal"}', '{"margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'divider');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '图标', 'icon', 'icon', 'Star', '{"name":"Star","size":"24"}', '{"color":"#409eff"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'icon');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '容器', 'container', 'layout', 'Box', '{"children":[]}', '{"padding":"20px","border":"1px dashed #ccc","minHeight":"100px"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'container');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '单选框', 'radio', 'form', 'CircleCheck', '{"options":[{"label":"选项1","value":"1"},{"label":"选项2","value":"2"}],"disabled":false}', '{"margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'radio');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '复选框', 'checkbox', 'form', 'Check', '{"options":[{"label":"选项1","value":"1"},{"label":"选项2","value":"2"}],"disabled":false}', '{"margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'checkbox');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '轮播图', 'carousel', 'advanced', 'PictureFilled', '{"height":200,"autoplay":true,"interval":3000,"items":[{"url":"https://via.placeholder.com/800x200/409eff/ffffff?text=Slide+1"},{"url":"https://via.placeholder.com/800x200/67c23a/ffffff?text=Slide+2"},{"url":"https://via.placeholder.com/800x200/e6a23c/ffffff?text=Slide+3"}]}', '{"width":"100%","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'carousel');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '折叠面板', 'collapse', 'advanced', 'Fold', '{"accordion":false,"items":[{"title":"面板标题1","content":"面板内容1"},{"title":"面板标题2","content":"面板内容2"},{"title":"面板标题3","content":"面板内容3"}]}', '{"width":"100%","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'collapse');

-- Flow components
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '流程节点', 'flowNode', 'flow', 'Connection', '{"title":"节点标题","description":"节点描述"}', '{"backgroundColor":"#ffffff","borderColor":"#409eff","borderWidth":"2px","borderStyle":"solid","borderRadius":"4px","padding":"10px","width":"150px","boxShadow":"0 2px 12px 0 rgba(0, 0, 0, 0.1)"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'flowNode');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '流程连线', 'flowConnection', 'flow', 'Right', '{"label":"","orientation":"horizontal","type":"straight","style":"solid","source":null,"target":null}', '{"width":"100px","height":"2px","backgroundColor":"#409eff","margin":"10px","borderColor":"#409eff"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'flowConnection');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '开始节点', 'flowStart', 'flow', 'VideoPlay', '{"title":"开始"}', '{"backgroundColor":"#67c23a","color":"#ffffff","borderRadius":"50%","width":"60px","height":"60px","boxShadow":"0 2px 12px 0 rgba(0, 0, 0, 0.1)"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'flowStart');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '结束节点', 'flowEnd', 'flow', 'CircleClose', '{"title":"结束"}', '{"backgroundColor":"#f56c6c","color":"#ffffff","borderRadius":"50%","width":"60px","height":"60px","boxShadow":"0 2px 12px 0 rgba(0, 0, 0, 0.1)"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'flowEnd');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '决策节点', 'flowDecision', 'flow', 'SwitchButton', '{"title":"决策","description":"条件判断","branches":[{"label":"是"},{"label":"否"}]}', '{"backgroundColor":"#ffffff","borderColor":"#e6a23c","borderWidth":"2px","borderStyle":"solid","width":"120px","height":"120px","transform":"rotate(45deg)","boxShadow":"0 2px 12px 0 rgba(0, 0, 0, 0.1)"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'flowDecision');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '流程节点', 'flowProcess', 'flow', 'Operation', '{"title":"流程","description":"流程描述","icon":"Operation","processingTime":0,"assignee":""}', '{"backgroundColor":"#ffffff","borderColor":"#409eff","borderWidth":"2px","borderStyle":"solid","borderRadius":"4px","padding":"15px","width":"180px","boxShadow":"0 2px 12px 0 rgba(0, 0, 0, 0.1)"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'flowProcess');
