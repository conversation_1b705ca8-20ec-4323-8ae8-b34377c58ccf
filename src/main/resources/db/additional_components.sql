-- Add more components to the database

-- Additional Basic Components
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '标记', 'tag', 'basic', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABEElEQVR4nO2UMU7DQBBF3xYUFJyAA1BwAQQVZ+AQlJyAkoKKK1DTUEFJQcMRKCKlQHGUiAJRUCTyKEZaazP2eoNNQcGXRrLW/89/Z3ZtKPmnCsAYeAQ2wAewBR6AE9d+ZAzMgZ/AfAZMYuQj4CpCHtolMI4hPwfWkQlCWwFnbeSXwL6DPLQdcNFEPvUk/7UpMPgr+QA4eJLvgf5v5L0a+UPgGbgBzoAucJ15Zxc4L9rgvkZ+C3QyBLrAXWbCm5IHHoFOA3lVMuFDyQNPQNVCXgGvmQmfSx54ASrP5Ks2B94Ckz8Al8Cb58RvwEXJA+8ekz+VkAMcAc+OyZ9LyYODdg28/rLmwFHJf1X9AgedXLYpVXsrAAAAAElFTkSuQmCC', '{"content":"标签内容","type":"success","effect":"light","closable":false}', '{"margin":"5px"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'tag');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '徽章', 'badge', 'basic', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABP0lEQVR4nO2UMU7DQBCGP4JoOAEHoOACCCrOwCEoOQElBRVXoKahgpKChiNQREqB4igRBaKgSORRjLTWZuz1BpuCgi+NZK3/n/9/s2tDyT9VAMbAI7ABPoAt8ACcuPYjY2AOHALzGTCJkY+Aqwh5aJfAOIb8HFhHJghtBZy1kV8C+w7y0HbARRP51JP81abA4K/kA+DgSb4H+r+R92rkD4Fn4AY4A7rAdead3eC8aIP7GvktUGUIdIE7z4Q3JQ88ApUHeVUy4UPJA09A1UJeAa+eCZ9LHngBKs/kqzYH3gKTP4BL4M1z4jfgouSBd4/Jn0rIAY6AZ8fkz6XkwUG7Bl5/WXPgqOS/qn4AXQZdtlVVXQsAAAAASUVORK5CYII=', '{"value":1,"max":99,"isDot":false,"hidden":false}', '{"margin":"5px"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'badge');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '警告', 'alert', 'basic', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABRklEQVR4nO2UMU7DQBCGP4JoOAEHoOACCCrOwCEoOQElBRVXoKahgpKChiNQREqB4igRBaKgSORRjLTWZuz1BpuCgi+NZK3/n/9/s2tDyT9VAMbAI7ABPoAt8ACcuPYjY2AOHALzGTCJkY+Aqwh5aJfAOIb8HFhHJghtBZy1kV8C+w7y0HbARRP51JP81abA4K/kA+DgSb4H+r+R92rkD4Fn4AY4A7rAdead3eC8aIP7GvktUGUIdIE7z4Q3JQ88ApUHeVUy4UPJA09A1UJeAa+eCZ9LHngBKs/kqzYH3gKTP4BL4M1z4jfgouSBd4/Jn0rIAY6AZ8fkz6XkwUG7Bl5/WXPgqOS/qn4AXQZdtlVVXQsAAAAASUVORK5CYII=', '{"title":"提示","description":"这是一条警告提示","type":"warning","closable":true,"showIcon":true}', '{"margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'alert');

-- Additional Form Components
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '日期选择器', 'date-picker', 'form', 'Calendar', '{"type":"date","placeholder":"选择日期","format":"YYYY-MM-DD","clearable":true,"disabled":false}', '{"width":"100%","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'date-picker');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '时间选择器', 'time-picker', 'form', 'Timer', '{"placeholder":"选择时间","clearable":true,"disabled":false}', '{"width":"100%","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'time-picker');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '颜色选择器', 'color-picker', 'form', 'Brush', '{"showAlpha":true,"colorFormat":"hex"}', '{"margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'color-picker');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '上传', 'upload', 'form', 'Upload', '{"action":"","multiple":false,"accept":"","listType":"text","autoUpload":true,"limit":5}', '{"width":"100%","margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'upload');

-- Additional Layout Components
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '分割线', 'divider', 'layout', 'DCaret', '{"direction":"horizontal","contentPosition":"center","content":""}', '{"margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'divider');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '间距', 'space', 'layout', 'Aim', '{"direction":"horizontal","size":"medium","wrap":false,"alignment":"center"}', '{"margin":"10px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'space');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '抽屉', 'drawer', 'layout', 'Right', '{"title":"抽屉标题","direction":"rtl","size":"30%","withHeader":true,"destroyOnClose":false,"modal":true,"showClose":true,"closeOnClickModal":true,"closeOnPressEscape":true}', '{}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'drawer');

-- Additional Advanced Components
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '步骤条', 'steps', 'advanced', 'Guide', '{"active":0,"direction":"horizontal","simple":false,"items":[{"title":"步骤1","description":"步骤1描述"},{"title":"步骤2","description":"步骤2描述"},{"title":"步骤3","description":"步骤3描述"}]}', '{"margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'steps');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '结果', 'result', 'advanced', 'CircleCheck', '{"title":"操作成功","subTitle":"请根据提示进行操作","icon":"success","status":"success"}', '{"margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'result');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '空状态', 'empty', 'advanced', 'Delete', '{"description":"暂无数据","image":""}', '{"margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'empty');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '描述列表', 'descriptions', 'advanced', 'Document', '{"title":"用户信息","column":3,"border":true,"direction":"horizontal","size":"default","items":[{"label":"用户名","value":"张三"},{"label":"手机号","value":"13800138000"},{"label":"地址","value":"北京市朝阳区"}]}', '{"margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'descriptions');

-- Additional Data Components
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '统计数值', 'statistic', 'data', 'DataLine', '{"title":"访问量","value":112893,"precision":0,"prefix":"","suffix":"","valueStyle":{"color":"#3f8600"}}', '{"margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'statistic');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '折线图', 'line-chart', 'data', 'TrendCharts', '{"title":"销售趋势","xAxis":["周一","周二","周三","周四","周五","周六","周日"],"series":[{"name":"本周","data":[120,132,101,134,90,230,210]},{"name":"上周","data":[220,182,191,234,290,330,310]}]}', '{"height":"300px","margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'line-chart');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '柱状图', 'bar-chart', 'data', 'Histogram', '{"title":"销售额","xAxis":["一月","二月","三月","四月","五月","六月"],"series":[{"name":"2022年","data":[320,302,301,334,390,330]},{"name":"2023年","data":[120,132,101,134,90,230]}]}', '{"height":"300px","margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'bar-chart');

INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT '饼图', 'pie-chart', 'data', 'PieChart', '{"title":"访问来源","series":[{"name":"访问来源","data":[{"value":1048,"name":"搜索引擎"},{"value":735,"name":"直接访问"},{"value":580,"name":"邮件营销"},{"value":484,"name":"联盟广告"},{"value":300,"name":"视频广告"}]}]}', '{"height":"300px","margin":"20px 0"}', 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'pie-chart');

-- Add a new category for data visualization components if it doesn't exist
INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'data', '数据组件', 'DataLine', 5, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'data');

-- Update the sort order of existing categories
UPDATE `component_category` SET `sort` = 6 WHERE `code` = 'flow';
UPDATE `component_category` SET `sort` = 7 WHERE `code` = 'icon';
UPDATE `component_category` SET `sort` = 8 WHERE `code` = 'custom';
