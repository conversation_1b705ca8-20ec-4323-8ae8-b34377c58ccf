-- Create component_category table if not exists
CREATE TABLE IF NOT EXISTS `component_category` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `code` varchar(50) NOT NULL COMMENT '分类编码',
    `name` varchar(100) NOT NULL COMMENT '分类名称',
    `icon` varchar(50) DEFAULT NULL COMMENT '分类图标',
    `sort` int(11) DEFAULT 0 COMMENT '排序',
    `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统内置（0-否，1-是）',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组件分类表';

-- Insert default categories if not exists
INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'basic', '基础组件', 'Document', 1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'basic');

INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'form', '表单组件', 'Edit', 2, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'form');

INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'layout', '布局组件', 'Grid', 3, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'layout');

INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'advanced', '高级组件', 'Star', 4, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'advanced');

INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'flow', '流程组件', 'Connection', 5, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'flow');

INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'icon', '图标组件', 'PictureRounded', 6, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'icon');

INSERT INTO `component_category` (`code`, `name`, `icon`, `sort`, `is_system`, `creator_id`, `create_time`, `update_time`)
SELECT 'custom', '自定义组件', 'SetUp', 7, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component_category` WHERE `code` = 'custom');
