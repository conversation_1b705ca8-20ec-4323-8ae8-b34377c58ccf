-- 创建数据库
CREATE DATABASE IF NOT EXISTS `lowcode` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE `lowcode`;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态（0-禁用，1-正常）',
  `role` varchar(50) DEFAULT 'ROLE_USER' COMMENT '角色',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 项目表
CREATE TABLE IF NOT EXISTS `project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `description` varchar(500) DEFAULT NULL COMMENT '项目描述',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '项目状态（0-草稿，1-发布）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';

-- 页面表
CREATE TABLE IF NOT EXISTS `page` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '页面ID',
  `project_id` bigint(20) NOT NULL COMMENT '所属项目ID',
  `name` varchar(100) NOT NULL COMMENT '页面名称',
  `title` varchar(100) DEFAULT NULL COMMENT '页面标题',
  `path` varchar(200) DEFAULT NULL COMMENT '页面路径',
  `config` longtext DEFAULT NULL COMMENT '页面配置（JSON格式）',
  `sort` int(11) DEFAULT 0 COMMENT '页面排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面表';

-- 组件表
CREATE TABLE IF NOT EXISTS `component` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '组件ID',
  `name` varchar(100) NOT NULL COMMENT '组件名称',
  `type` varchar(50) NOT NULL COMMENT '组件类型',
  `category` varchar(50) DEFAULT NULL COMMENT '组件分类',
  `icon` varchar(200) DEFAULT NULL COMMENT '组件图标',
  `props` text DEFAULT NULL COMMENT '组件属性配置（JSON格式）',
  `events` text DEFAULT NULL COMMENT '组件事件配置（JSON格式）',
  `styles` text DEFAULT NULL COMMENT '组件样式配置（JSON格式）',
  `template` text DEFAULT NULL COMMENT '组件模板代码',
  `is_system` tinyint(1) DEFAULT 1 COMMENT '是否系统内置（0-否，1-是）',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组件表';

-- 模板表
CREATE TABLE IF NOT EXISTS `template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `description` varchar(500) DEFAULT NULL COMMENT '模板描述',
  `category` varchar(50) DEFAULT NULL COMMENT '模板分类',
  `thumbnail` varchar(200) DEFAULT NULL COMMENT '模板缩略图',
  `config` longtext DEFAULT NULL COMMENT '模板配置（JSON格式）',
  `is_system` tinyint(1) DEFAULT 1 COMMENT '是否系统内置（0-否，1-是）',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板表';

-- 数据源表
CREATE TABLE IF NOT EXISTS `data_source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源ID',
  `project_id` bigint(20) NOT NULL COMMENT '所属项目ID',
  `name` varchar(100) NOT NULL COMMENT '数据源名称',
  `type` varchar(20) NOT NULL COMMENT '数据源类型（static-静态数据，api-接口数据）',
  `config` text DEFAULT NULL COMMENT '数据源配置（JSON格式）',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源表';

-- 初始化管理员用户
INSERT INTO `user` (`username`, `password`, `nickname`, `status`, `role`) VALUES ('admin', '$2a$10$EuWPZHzz32dJN7jexM34MOot3R9HbXVdK/xF3KSKuiZ.jN.w6xjgK', '管理员', 1, 'ROLE_ADMIN');

-- 初始化测试用户
INSERT INTO `user` (`username`, `password`, `nickname`, `status`, `role`) VALUES ('test', '$2a$10$EuWPZHzz32dJN7jexM34MOot3R9HbXVdK/xF3KSKuiZ.jN.w6xjgK', '测试用户', 1, 'ROLE_USER');

-- 初始化开发者用户
INSERT INTO `user` (`username`, `password`, `nickname`, `status`, `role`) VALUES ('dev', '$2a$10$EuWPZHzz32dJN7jexM34MOot3R9HbXVdK/xF3KSKuiZ.jN.w6xjgK', '开发者', 1, 'ROLE_USER');

-- 初始化设计师用户
INSERT INTO `user` (`username`, `password`, `nickname`, `status`, `role`) VALUES ('designer', '$2a$10$EuWPZHzz32dJN7jexM34MOot3R9HbXVdK/xF3KSKuiZ.jN.w6xjgK', '设计师', 1, 'ROLE_USER');

-- 初始化项目数据
INSERT INTO `project` (`name`, `description`, `creator_id`, `status`, `create_time`, `update_time`) VALUES ('示例项目1', '这是一个示例项目', 1, 1, '2023-06-01 10:00:00', '2023-06-01 10:00:00');
INSERT INTO `project` (`name`, `description`, `creator_id`, `status`, `create_time`, `update_time`) VALUES ('示例项目2', '这是另一个示例项目', 1, 1, '2023-06-02 10:00:00', '2023-06-02 10:00:00');
INSERT INTO `project` (`name`, `description`, `creator_id`, `status`, `create_time`, `update_time`) VALUES ('测试项目', '这是测试用户的项目', 2, 1, '2023-06-03 10:00:00', '2023-06-03 10:00:00');

-- 初始化页面数据
INSERT INTO `page` (`project_id`, `name`, `title`, `path`, `config`, `sort`, `create_time`, `update_time`) VALUES (1, '首页', '首页', '/index', '{\"components\":[{\"type\":\"text\",\"id\":\"text1\",\"name\":\"文本\",\"props\":{\"content\":\"欢迎来到示例项目1\"},\"styles\":{\"fontSize\":\"24px\",\"textAlign\":\"center\",\"margin\":\"20px 0\"}}]}', 0, '2023-06-01 11:00:00', '2023-06-01 11:00:00');
INSERT INTO `page` (`project_id`, `name`, `title`, `path`, `config`, `sort`, `create_time`, `update_time`) VALUES (1, '关于我们', '关于我们', '/about', '{"components":[{"type":"text","id":"text1","name":"文本","props":{"content":"这是关于我们页面"},"styles":{"fontSize":"18px","margin":"20px 0"}}]}', 1, '2023-06-01 11:30:00', '2023-06-01 11:30:00');
INSERT INTO `page` (`project_id`, `name`, `title`, `path`, `config`, `sort`, `create_time`, `update_time`) VALUES (2, '首页', '首页', '/index', '{"components":[{"type":"text","id":"text1","name":"文本","props":{"content":"欢迎来到示例项目2"},"styles":{"fontSize":"24px","textAlign":"center","margin":"20px 0"}}]}', 0, '2023-06-02 11:00:00', '2023-06-02 11:00:00');
INSERT INTO `page` (`project_id`, `name`, `title`, `path`, `config`, `sort`, `create_time`, `update_time`) VALUES (3, '测试页面', '测试页面', '/test', '{"components":[{"type":"text","id":"text1","name":"文本","props":{"content":"这是测试用户的页面"},"styles":{"fontSize":"18px","margin":"20px 0"}}]}', 0, '2023-06-03 11:00:00', '2023-06-03 11:00:00');
