<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.web.lowcode.mapper.ImageMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.web.lowcode.entity.ImageEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="original_name" property="originalName" jdbcType="VARCHAR"/>
        <result column="extension" property="extension" jdbcType="VARCHAR"/>
        <result column="size" property="size" jdbcType="BIGINT"/>
        <result column="content_type" property="contentType" jdbcType="VARCHAR"/>
        <result column="minio_path" property="minioPath" jdbcType="VARCHAR"/>
        <result column="bucket_name" property="bucketName" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="width" property="width" jdbcType="INTEGER"/>
        <result column="height" property="height" jdbcType="INTEGER"/>
        <result column="md5" property="md5" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="INTEGER"/>
        <result column="business_id" property="businessId" jdbcType="BIGINT"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="uploader_id" property="uploaderId" jdbcType="BIGINT"/>
        <result column="uploader_name" property="uploaderName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, original_name, extension, size, content_type, minio_path, bucket_name, url,
        width, height, md5, category, business_id, business_type, status, uploader_id, 
        uploader_name, create_time, update_time, delete_time, remark
    </sql>

    <!-- 分页查询图片列表 -->
    <select id="selectImagePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image
        <where>
            <if test="category != null">
                AND category = #{category}
            </if>
            <if test="businessType != null and businessType != ''">
                AND business_type = #{businessType}
            </if>
            <if test="uploaderId != null">
                AND uploader_id = #{uploaderId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                     OR original_name LIKE CONCAT('%', #{keyword}, '%')
                     OR remark LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据MD5查询图片 -->
    <select id="selectByMd5" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image
        WHERE md5 = #{md5}
        LIMIT 1
    </select>

    <!-- 根据业务信息查询图片列表 -->
    <select id="selectByBusiness" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image
        WHERE business_type = #{businessType} 
          AND business_id = #{businessId}
          AND status = 0
        ORDER BY create_time DESC
    </select>

    <!-- 统计图片数量 -->
    <select id="countByCategory" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM image
        WHERE category = #{category}
          AND status = 0
    </select>

    <!-- 统计存储大小 -->
    <select id="sumSizeByCategory" resultType="java.lang.Long">
        SELECT COALESCE(SUM(size), 0)
        FROM image
        WHERE category = #{category}
          AND status = 0
    </select>

    <!-- 批量更新状态 -->
    <update id="updateStatusBatch">
        UPDATE image
        SET status = #{status}, update_time = #{updateTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
