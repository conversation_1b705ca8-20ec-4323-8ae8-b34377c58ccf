<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.web.lowcode.mapper.ImageLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.web.lowcode.entity.ImageLogEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="image_id" property="imageId" jdbcType="BIGINT"/>
        <result column="operation_type" property="operationType" jdbcType="VARCHAR"/>
        <result column="operation_desc" property="operationDesc" jdbcType="VARCHAR"/>
        <result column="before_data" property="beforeData" jdbcType="LONGVARCHAR"/>
        <result column="after_data" property="afterData" jdbcType="LONGVARCHAR"/>
        <result column="operator_id" property="operatorId" jdbcType="BIGINT"/>
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="result" property="result" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, image_id, operation_type, operation_desc, before_data, after_data, operator_id,
        operator_name, ip_address, user_agent, result, error_message, duration, create_time
    </sql>

    <!-- 分页查询操作日志 -->
    <select id="selectLogPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_log
        <where>
            <if test="imageId != null">
                AND image_id = #{imageId}
            </if>
            <if test="operationType != null and operationType != ''">
                AND operation_type = #{operationType}
            </if>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="result != null and result != ''">
                AND result = #{result}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据图片ID查询操作日志 -->
    <select id="selectByImageId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_log
        WHERE image_id = #{imageId}
        ORDER BY create_time DESC
    </select>

    <!-- 统计操作次数 -->
    <select id="countByOperationType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM image_log
        WHERE operation_type = #{operationType}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 清理过期日志 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM image_log
        WHERE create_time &lt; #{expireTime}
    </delete>

</mapper>
