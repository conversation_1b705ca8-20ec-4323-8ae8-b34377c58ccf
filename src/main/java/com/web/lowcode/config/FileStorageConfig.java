package com.web.lowcode.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 文件存储配置类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Configuration
public class FileStorageConfig {

    /**
     * 文件存储配置属性
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "file.storage")
    public static class FileStorageProperties {
        
        /**
         * 存储类型：auto(自动选择), minio(MinIO), local(本地存储)
         */
        private String type = "auto";
        
        /**
         * 是否在MinIO不可用时回退到本地存储
         */
        private boolean fallbackToLocal = true;
        
        /**
         * 本地存储配置
         */
        private Local local = new Local();
        
        @Data
        public static class Local {
            /**
             * 本地存储基础路径
             */
            private String basePath = "./uploads";
            
            /**
             * 访问URL前缀
             */
            private String urlPrefix = "http://localhost:8080/api/files";
        }
    }
    
    /**
     * 存储类型枚举
     */
    public enum StorageType {
        /**
         * 自动选择（优先MinIO，失败时使用本地）
         */
        AUTO,
        
        /**
         * MinIO存储
         */
        MINIO,
        
        /**
         * 本地存储
         */
        LOCAL
    }
}
