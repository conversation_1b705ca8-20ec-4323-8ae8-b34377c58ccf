package com.web.lowcode.config;

import com.web.lowcode.generator.CodeGenerator;
import com.web.lowcode.generator.VueCodeGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 代码生成器配置类
 * 用于注册和配置不同的代码生成器实现
 */
@Configuration
public class CodeGeneratorConfig {

    private final VueCodeGenerator vueCodeGenerator;

    @Autowired
    public CodeGeneratorConfig(VueCodeGenerator vueCodeGenerator) {
        this.vueCodeGenerator = vueCodeGenerator;
    }

    /**
     * 注册原始Vue代码生成器
     * 作为备用实现
     */
    @Bean(name = "originalVueCodeGenerator")
    public CodeGenerator originalVueCodeGeneratorBean() {
        return vueCodeGenerator;
    }
}
