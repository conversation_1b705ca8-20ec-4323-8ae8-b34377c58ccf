package com.web.lowcode.config;

import io.minio.MinioClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * MinIO配置类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Configuration
public class MinioConfig {

    /**
     * MinIO配置属性
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "minio")
    public static class MinioProperties {
        /**
         * MinIO服务端点
         */
        private String endpoint;
        
        /**
         * 访问密钥
         */
        private String accessKey;
        
        /**
         * 秘密密钥
         */
        private String secretKey;
        
        /**
         * 存储桶名称
         */
        private String bucketName;
        
        /**
         * 图片大小限制（字节）
         */
        private Long imageSizeLimit = 10485760L; // 默认10MB
        
        /**
         * 允许的内容类型
         */
        private String allowedContentTypes = "image/jpeg,image/jpg,image/png,image/gif,image/bmp,image/webp";
    }

    /**
     * 创建MinIO客户端
     */
    @Bean
    public MinioClient minioClient(MinioProperties minioProperties) {
        return MinioClient.builder()
                .endpoint(minioProperties.getEndpoint())
                .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                .build();
    }
}
