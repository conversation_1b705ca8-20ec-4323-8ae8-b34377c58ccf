package com.web.lowcode.generator;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.generator.component.ComponentRenderer;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 页面代码构建器
 * 负责构建页面代码
 */
@Component
public class PageCodeBuilder {
    private static final Logger logger = LoggerFactory.getLogger(PageCodeBuilder.class);

    // 存储页面ID到路径的映射
    private Map<String, String> pageIdToPathMap = new HashMap<>();

    // 当前处理的所有页面列表
    private List<PageEntity> allPages;

    // 组件渲染器
    private final ComponentRenderer componentRenderer = new ComponentRenderer();

    /**
     * 构建Vue页面代码
     * @param page 页面实体
     * @param pages 项目中的所有页面列表
     * @return 生成的Vue页面代码
     */
    public String buildVuePage(PageEntity page, List<PageEntity> pages) {
        // 初始化页面ID到路径的映射
        this.allPages = pages;
        initPageIdToPathMap(pages);
        logger.info("构建Vue页面代码: {}", page.getName());

        try {
            StringBuilder sb = new StringBuilder();

            // 解析页面配置
            JSONObject pageConfig = parsePageConfig(page);

            // 构建模板部分
            sb.append("<template>\n");
            sb.append("  <div class=\"page-container\">\n");
            // 生成组件模板
            if (pageConfig.has("components")) {
                sb.append(generateComponentsTemplate(pageConfig.getJSONArray("components")));
            } else {
                sb.append("    <h1>").append(page.getTitle()).append("</h1>\n");
                sb.append("    <div class=\"page-content\">\n");
                sb.append("      <!-- 页面内容 -->\n");
                sb.append("      <p>这是").append(page.getTitle()).append("页面的内容</p>\n");
                sb.append("    </div>\n");
            }
            sb.append("  </div>\n");
            sb.append("</template>\n\n");

            // 构建脚本部分
            sb.append("<script>\n");
            sb.append("export default {\n");
            sb.append("  name: '").append(getSafeComponentName(page.getName())).append("',\n");
            sb.append("  data() {\n");
            sb.append("    return {\n");

            // 生成数据属性
            if (pageConfig.has("data")) {
                sb.append(generateDataProperties(pageConfig.getJSONObject("data")));
            } else {
                sb.append("      // 页面数据\n");
            }

            sb.append("    }\n");
            sb.append("  },\n");
            sb.append("  methods: {\n");

            // 生成事件处理方法
            if (pageConfig.has("components")) {
                sb.append(generateEventMethods(pageConfig.getJSONArray("components")));
            } else if (pageConfig.has("events")) {
                sb.append(generateEventMethods(pageConfig.getJSONArray("events")));
            } else {
                sb.append("    // 页面方法\n");
            }

            sb.append("  },\n");
            sb.append("  mounted() {\n");
            sb.append("    // 页面加载完成后执行\n");
            sb.append("    console.log('").append(page.getTitle()).append(" 页面加载完成')\n");
            sb.append("  }\n");
            sb.append("}\n");
            sb.append("</script>\n\n");

            // 构建样式部分
            sb.append("<style scoped>\n");
            sb.append(".page-container {\n");
            sb.append("  padding: 20px;\n");
            sb.append("}\n\n");
            sb.append(".page-content {\n");
            sb.append("  margin-top: 20px;\n");
            sb.append("}\n\n");

            // 添加组件容器样式
            sb.append(".page-components {\n");
            sb.append("  display: flex;\n");
            sb.append("  flex-direction: column;\n");
            sb.append("  width: 100%;\n");
            sb.append("}\n\n");

            sb.append(".component-container {\n");
            sb.append("  width: 100%;\n");
            sb.append("}\n\n");

            // 生成组件样式
            if (pageConfig.has("styles")) {
                sb.append(generateStyles(pageConfig.getJSONObject("styles")));
            }

            sb.append("</style>\n");

            return sb.toString();
        } catch (Exception e) {
            logger.error("构建Vue页面代码失败: {}", e.getMessage(), e);
            // 如果解析失败，返回默认页面
            return generateDefaultVuePage(page);
        }
    }

    /**
     * 构建UniApp页面代码
     * @param page 页面实体
     * @param pages 项目中的所有页面列表
     * @return 生成的UniApp页面代码
     */
    public String buildUniAppPage(PageEntity page, List<PageEntity> pages) {
        logger.info("构建UniApp页面代码: {}", page.getName());

        // 初始化页面ID到路径的映射
        this.allPages = pages;
        initPageIdToPathMap(pages);

        try {
            StringBuilder sb = new StringBuilder();

            // 解析页面配置
            JSONObject pageConfig = parsePageConfig(page);

            // 构建模板部分
            sb.append("<template>\n");
            sb.append("  <view class=\"page-container\">\n");
            sb.append("    <text class=\"page-title\">").append(page.getTitle()).append("</text>\n");
            sb.append("    <view class=\"page-content\">\n");

            sb.append("    </view>\n");
            sb.append("  </view>\n");
            sb.append("</template>\n\n");

            // 构建脚本部分
            sb.append("<script>\n");
            sb.append("export default {\n");
            sb.append("  data() {\n");
            sb.append("    return {\n");

            // 生成数据属性
            if (pageConfig.has("data")) {
                sb.append(generateDataProperties(pageConfig.getJSONObject("data")));
            } else {
                sb.append("      // 页面数据\n");
            }

            sb.append("    }\n");
            sb.append("  },\n");
            sb.append("  methods: {\n");

            // 生成事件处理方法
            if (pageConfig.has("events")) {
                sb.append(generateUniAppEventMethods(pageConfig.getJSONArray("events")));
            } else {
                sb.append("    // 页面方法\n");
            }

            sb.append("  },\n");
            sb.append("  onLoad() {\n");
            sb.append("    // 页面加载时执行\n");
            sb.append("    console.log('").append(page.getTitle()).append(" 页面加载')\n");
            sb.append("  },\n");
            sb.append("  onShow() {\n");
            sb.append("    // 页面显示时执行\n");
            sb.append("  },\n");
            sb.append("  onHide() {\n");
            sb.append("    // 页面隐藏时执行\n");
            sb.append("  }\n");
            sb.append("}\n");
            sb.append("</script>\n\n");

            // 构建样式部分
            sb.append("<style>\n");
            sb.append(".page-container {\n");
            sb.append("  padding: 20px;\n");
            sb.append("}\n\n");
            sb.append(".page-title {\n");
            sb.append("  font-size: 18px;\n");
            sb.append("  font-weight: bold;\n");
            sb.append("  margin-bottom: 20px;\n");
            sb.append("}\n\n");
            sb.append(".page-content {\n");
            sb.append("  margin-top: 20px;\n");
            sb.append("}\n");

            // 生成组件样式
            if (pageConfig.has("styles")) {
                sb.append(generateStyles(pageConfig.getJSONObject("styles")));
            }

            sb.append("</style>\n");

            return sb.toString();
        } catch (Exception e) {
            logger.error("构建UniApp页面代码失败: {}", e.getMessage(), e);
            // 如果解析失败，返回默认页面
            return generateDefaultUniAppPage(page);
        }
    }

    /**
     * 解析页面配置
     * @param page 页面实体
     * @return 页面配置对象
     */
    private JSONObject parsePageConfig(PageEntity page) {
        try {
            String config = page.getConfig();
            if (config != null && !config.isEmpty()) {
                logger.info("解析页面配置: {}", config);
                JSONObject jsonConfig = new JSONObject(config);

                // 输出配置的所有键
                logger.info("页面配置的键: {}", jsonConfig.keySet());

                // 检查是否有嵌套的components字段
                if (!jsonConfig.has("components") && jsonConfig.has("config")) {
                    String innerConfig = jsonConfig.getString("config");
                    if (innerConfig != null && !innerConfig.isEmpty()) {
                        try {
                            JSONObject innerJsonConfig = new JSONObject(innerConfig);
                            logger.info("嵌套配置的键: {}", innerJsonConfig.keySet());

                            if (innerJsonConfig.has("components")) {
                                logger.info("使用嵌套的components配置");
                                return innerJsonConfig;
                            }
                        } catch (Exception e) {
                            logger.warn("解析嵌套配置失败: {}", e.getMessage());
                        }
                    }
                }

                // 如果有components字段，输出组件数量
                if (jsonConfig.has("components")) {
                    JSONArray components = jsonConfig.getJSONArray("components");
                    logger.info("页面包含 {} 个组件", components.length());

                    // 输出第一个组件的信息（如果有）
                    if (components.length() > 0) {
                        JSONObject firstComponent = components.getJSONObject(0);
                        logger.info("第一个组件类型: {}, 键: {}",
                                firstComponent.optString("type"), firstComponent.keySet());

                        // 检查是否有事件
                        if (firstComponent.has("events")) {
                            logger.info("第一个组件有事件配置");
                        }
                    }
                }

                return jsonConfig;
            }
        } catch (Exception e) {
            logger.error("解析页面配置失败: {}", e.getMessage(), e);
        }
        return new JSONObject();
    }

    /**
     * 生成组件模板
     * @param components 组件数组
     * @return 生成的组件模板代码
     */
    private String generateComponentsTemplate(JSONArray components) {
        // 使用组件渲染器渲染组件
        return componentRenderer.renderComponents(components);
    }

    /**
     * 生成数据属性
     * @param data 数据对象
     * @return 生成的数据属性代码
     */
    private String generateDataProperties(JSONObject data) {
        StringBuilder sb = new StringBuilder();

        for (String key : data.keySet()) {
            Object value = data.opt(key);
            sb.append("      ").append(key).append(": ");

            if (value instanceof JSONObject || value instanceof JSONArray) {
                sb.append(value.toString()).append(",\n");
            } else if (value instanceof String) {
                sb.append("'").append(value).append("',\n");
            } else if (value instanceof Boolean || value instanceof Number) {
                sb.append(value).append(",\n");
            } else if (value == null) {
                sb.append("null,\n");
            } else {
                sb.append("'").append(value).append("',\n");
            }
        }

        return sb.toString();
    }

    /**
     * 生成事件处理方法
     * @param components 组件数组
     * @return 生成的事件处理方法代码
     */
    private String generateEventMethods(JSONArray components) {
        StringBuilder sb = new StringBuilder();

        // 遍历所有组件，收集事件处理方法
        for (int i = 0; i < components.length(); i++) {
            JSONObject component = components.getJSONObject(i);

            // 处理当前组件的事件处理方法
            if (component.has("eventHandlers")) {
                JSONArray eventHandlers = component.getJSONArray("eventHandlers");
                for (int j = 0; j < eventHandlers.length(); j++) {
                    JSONObject handler = eventHandlers.getJSONObject(j);
                    String methodName = handler.optString("name", "");
                    String eventType = handler.optString("eventType", "");

                    if (!methodName.isEmpty()) {
                        sb.append("    ").append(methodName).append("(event) {\n");

                        // 如果有直接的代码
                        if (handler.has("code")) {
                            String code = handler.optString("code", "");
                            sb.append("      ").append(code).append("\n");
                        }
                        // 如果有动作对象
                        else if (handler.has("action")) {
                            JSONObject action = handler.getJSONObject("action");
                            String actionType = action.optString("type", "");

                            // 根据动作类型生成代码
                            if ("navigate".equals(actionType)) {
                                String navigationType = action.optString("navigationType", "");

                                if ("page".equals(navigationType) || "页面".equals(navigationType)) {
                                    // 内部页面跳转
                                    String pageId = action.optString("pageId", "");
                                    if (!pageId.isEmpty()) {
                                        // 查找目标页面的路径
                                        String targetPath = findPagePathById(pageId);
                                        sb.append("      // 导航到指定页面\n");
                                        sb.append("      console.log('导航到页面ID: " + pageId + ", 路径: " + targetPath + "');\n");
                                        sb.append("      this.$router.push('").append(targetPath).append("');\n");
                                    } else {
                                        sb.append("      console.error('缺少目标页面ID');\n");
                                    }
                                } else if ("url".equals(navigationType) || "外部链接".equals(navigationType)) {
                                    // 外部URL跳转
                                    String url = action.optString("url", "");
                                    sb.append("      // 导航到外部链接\n");
                                    sb.append("      window.open('").append(url).append("', '_blank');\n");
                                } else {
                                    // 默认处理
                                    String url = action.optString("url", "");
                                    sb.append("      // 导航到指定页面\n");
                                    sb.append("      this.$router.push('").append(url).append("');\n");
                                }
                            } else if ("alert".equals(actionType)) {
                                String message = action.optString("message", "");
                                sb.append("      // 显示提示信息\n");
                                sb.append("      alert('").append(message).append("');\n");
                            } else if ("confirm".equals(actionType)) {
                                String message = action.optString("message", "");
                                sb.append("      // 显示确认对话框\n");
                                sb.append("      if (confirm('").append(message).append("')) {\n");
                                sb.append("        // 用户点击确认\n");
                                sb.append("        console.log('用户确认');\n");
                                sb.append("      } else {\n");
                                sb.append("        // 用户点击取消\n");
                                sb.append("        console.log('用户取消');\n");
                                sb.append("      }\n");
                            } else if ("submit".equals(actionType)) {
                                sb.append("      // 提交表单\n");
                                sb.append("      console.log('提交表单', event);\n");
                                sb.append("      // TODO: 实现表单提交逻辑\n");
                            } else if ("api".equals(actionType)) {
                                String url = action.optString("url", "");
                                String method = action.optString("method", "GET");
                                sb.append("      // 调用API\n");
                                sb.append("      fetch('").append(url).append("', {\n");
                                sb.append("        method: '").append(method).append("',\n");
                                sb.append("        headers: {\n");
                                sb.append("          'Content-Type': 'application/json'\n");
                                sb.append("        }\n");
                                sb.append("      })\n");
                                sb.append("        .then(response => response.json())\n");
                                sb.append("        .then(data => {\n");
                                sb.append("          console.log('API调用成功:', data);\n");
                                sb.append("        })\n");
                                sb.append("        .catch(error => {\n");
                                sb.append("          console.error('API调用失败:', error);\n");
                                sb.append("        });\n");
                            } else {
                                sb.append("      console.log('处理").append(eventType).append(" 事件');\n");
                            }
                        } else {
                            sb.append("      console.log('处理").append(eventType).append(" 事件', event);\n");
                        }

                        sb.append("    },\n\n");
                    }
                }
            }

            // 递归处理子组件
            if (component.has("children") && component.getJSONArray("children").length() > 0) {
                sb.append(generateEventMethods(component.getJSONArray("children")));
            }
        }

        return sb.toString();
    }

    /**
     * 生成UniApp事件处理方法
     * @param events 事件数组
     * @return 生成的UniApp事件处理方法代码
     */
    private String generateUniAppEventMethods(JSONArray events) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < events.length(); i++) {
            JSONObject event = events.getJSONObject(i);
            String name = event.optString("name", "");
            String params = event.optString("params", "");
            String code = event.optString("code", "");

            if (!name.isEmpty()) {
                sb.append("    ").append(name).append("(").append(params).append(") {\n");
                sb.append("      ").append(code).append("\n");
                sb.append("    },\n\n");
            }
        }

        return sb.toString();
    }

    /**
     * 生成样式
     * @param styles 样式对象
     * @return 生成的样式代码
     */
    private String generateStyles(JSONObject styles) {
        StringBuilder sb = new StringBuilder();

        for (String selector : styles.keySet()) {
            JSONObject style = styles.getJSONObject(selector);
            sb.append(selector).append(" {\n");

            for (String property : style.keySet()) {
                String value = style.optString(property);
                sb.append("  ").append(property).append(": ").append(value).append(";\n");
            }

            sb.append("}\n\n");
        }

        return sb.toString();
    }

    /**
     * 获取安全的组件名称（确保是合法的JavaScript变量名）
     * @param name 原始名称
     * @return 安全的组件名称
     */
    private String getSafeComponentName(String name) {
        // 如果名称以数字开头，添加前缀
        if (name.matches("^\\d.*")) {
            return "Page" + name;
        }
        // 替换其他不合法字符
        return name.replaceAll("[^a-zA-Z0-9_$]", "");
    }

    /**
     * 初始化页面ID到路径的映射
     * @param pages 项目中的所有页面列表
     */
    private void initPageIdToPathMap(List<PageEntity> pages) {
        pageIdToPathMap.clear();

        if (pages != null) {
            for (PageEntity page : pages) {
                String pageId = String.valueOf(page.getId());
                String pagePath = page.getPath();

                // 确保路径以/开头
                if (pagePath != null && !pagePath.startsWith("/")) {
                    pagePath = "/" + pagePath;
                }

                pageIdToPathMap.put(pageId, pagePath);
                logger.info("页面ID映射: {} -> {}", pageId, pagePath);
            }
        }

        logger.info("初始化页面ID映射完成，共 {} 个页面", pageIdToPathMap.size());
    }

    /**
     * 根据页面ID查找页面路径
     * @param pageId 页面ID
     * @return 页面路径，如果找不到则返回默认路径
     */
    private String findPagePathById(String pageId) {
        String path = pageIdToPathMap.get(pageId);

        if (path != null) {
            return path;
        }

        // 如果没有在映射中找到，尝试直接从页面列表中查找
        if (allPages != null) {
            for (PageEntity page : allPages) {
                if (String.valueOf(page.getId()).equals(pageId)) {
                    String pagePath = page.getPath();
                    if (pagePath != null && !pagePath.startsWith("/")) {
                        pagePath = "/" + pagePath;
                    }

                    // 更新映射
                    pageIdToPathMap.put(pageId, pagePath);

                    return pagePath;
                }
            }
        }

        // 如果还是找不到，返回默认路径
        logger.warn("找不到页面ID {} 对应的路径，使用默认路径", pageId);
        return "/";
    }

    /**
     * 生成默认Vue页面
     * @param page 页面实体
     * @return 生成的默认Vue页面代码
     */
    private String generateDefaultVuePage(PageEntity page) {
        StringBuilder sb = new StringBuilder();

        // 构建模板部分
        sb.append("<template>\n");
        sb.append("  <div class=\"page-container\">\n");
        sb.append("    <h1>").append(page.getTitle()).append("</h1>\n");
        sb.append("    <div class=\"page-content\">\n");
        sb.append("      <!-- 页面内容 -->\n");
        sb.append("      <p>这是").append(page.getTitle()).append("页面的内容</p>\n");
        sb.append("    </div>\n");
        sb.append("  </div>\n");
        sb.append("</template>\n\n");

        // 构建脚本部分
        sb.append("<script>\n");
        sb.append("export default {\n");
        sb.append("  name: '").append(getSafeComponentName(page.getName())).append("',\n");
        sb.append("  data() {\n");
        sb.append("    return {\n");
        sb.append("      // 页面数据\n");
        sb.append("    }\n");
        sb.append("  },\n");
        sb.append("  methods: {\n");
        sb.append("    // 页面方法\n");
        sb.append("  },\n");
        sb.append("  mounted() {\n");
        sb.append("    // 页面加载完成后执行\n");
        sb.append("    console.log('").append(page.getTitle()).append(" 页面加载完成')\n");
        sb.append("  }\n");
        sb.append("}\n");
        sb.append("</script>\n\n");

        // 构建样式部分
        sb.append("<style scoped>\n");
        sb.append(".page-container {\n");
        sb.append("  padding: 20px;\n");
        sb.append("}\n\n");
        sb.append(".page-content {\n");
        sb.append("  margin-top: 20px;\n");
        sb.append("}\n\n");

        // 添加组件容器样式
        sb.append(".page-components {\n");
        sb.append("  display: flex;\n");
        sb.append("  flex-direction: column;\n");
        sb.append("  width: 100%;\n");
        sb.append("}\n\n");

        sb.append(".component-container {\n");
        sb.append("  width: 100%;\n");
        sb.append("}\n");
        sb.append("</style>\n");

        return sb.toString();
    }

    /**
     * 生成默认UniApp页面
     * @param page 页面实体
     * @return 生成的默认UniApp页面代码
     */
    private String generateDefaultUniAppPage(PageEntity page) {
        StringBuilder sb = new StringBuilder();

        // 构建模板部分
        sb.append("<template>\n");
        sb.append("  <view class=\"page-container\">\n");
        sb.append("    <text class=\"page-title\">").append(page.getTitle()).append("</text>\n");
        sb.append("    <view class=\"page-content\">\n");
        sb.append("      <!-- 页面内容 -->\n");
        sb.append("      <text>这是").append(page.getTitle()).append("页面的内容</text>\n");
        sb.append("    </view>\n");
        sb.append("  </view>\n");
        sb.append("</template>\n\n");

        // 构建脚本部分
        sb.append("<script>\n");
        sb.append("export default {\n");
        sb.append("  data() {\n");
        sb.append("    return {\n");
        sb.append("      // 页面数据\n");
        sb.append("    }\n");
        sb.append("  },\n");
        sb.append("  methods: {\n");
        sb.append("    // 页面方法\n");
        sb.append("  },\n");
        sb.append("  onLoad() {\n");
        sb.append("    // 页面加载时执行\n");
        sb.append("    console.log('").append(page.getTitle()).append(" 页面加载')\n");
        sb.append("  },\n");
        sb.append("  onShow() {\n");
        sb.append("    // 页面显示时执行\n");
        sb.append("  },\n");
        sb.append("  onHide() {\n");
        sb.append("    // 页面隐藏时执行\n");
        sb.append("  }\n");
        sb.append("}\n");
        sb.append("</script>\n\n");

        // 构建样式部分
        sb.append("<style>\n");
        sb.append(".page-container {\n");
        sb.append("  padding: 20px;\n");
        sb.append("}\n\n");
        sb.append(".page-title {\n");
        sb.append("  font-size: 18px;\n");
        sb.append("  font-weight: bold;\n");
        sb.append("  margin-bottom: 20px;\n");
        sb.append("}\n\n");
        sb.append(".page-content {\n");
        sb.append("  margin-top: 20px;\n");
        sb.append("}\n\n");

        // 添加组件容器样式
        sb.append(".page-components {\n");
        sb.append("  display: flex;\n");
        sb.append("  flex-direction: column;\n");
        sb.append("  width: 100%;\n");
        sb.append("}\n\n");

        sb.append(".component-container {\n");
        sb.append("  width: 100%;\n");
        sb.append("}\n");
        sb.append("</style>\n");

        return sb.toString();
    }
}
