package com.web.lowcode.generator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 代码生成器工厂类
 * 负责创建和管理不同框架的代码生成器实例
 */
@Component
public class CodeGeneratorFactory {
    private final Map<String, CodeGenerator> generators;
    private static final Logger logger = LoggerFactory.getLogger(CodeGeneratorFactory.class);

    /**
     * 构造函数，注入所有支持的代码生成器
     * 使用依赖注入模式获取各种代码生成器实现
     *
     * @param vueGenerator Vue框架代码生成器
     * @param uniAppGenerator UniApp框架代码生成器
     * @param legacyVueGenerator 传统Vue框架代码生成器
     */
    public CodeGeneratorFactory(@Qualifier("vueCodeGenerator") CodeGenerator vueGenerator,
                               UniAppCodeGenerator uniAppGenerator,
                               @Qualifier("originalVueCodeGenerator") CodeGenerator legacyVueGenerator) {
        // 初始化生成器映射
        generators = new HashMap<>();

        // 注册各种代码生成器
        generators.put("vue", vueGenerator);        // Vue生成器
        generators.put("uniapp", uniAppGenerator);  // UniApp生成器
        generators.put("vue-legacy", legacyVueGenerator);   // 传统Vue生成器
        generators.put("preview", vueGenerator);    // 使用Vue生成器作为预览生成器

        // 记录初始化信息
        logger.info("代码生成器工厂初始化完成，支持的框架类型: {}", generators.keySet());
    }

    /**
     * 获取指定框架的代码生成器
     * @param framework 框架名称（vue/uniapp）
     * @return 对应框架的代码生成器实例
     * @throws IllegalArgumentException 当指定的框架不支持时抛出
     */
    public CodeGenerator getGenerator(String framework) {
        CodeGenerator generator = generators.get(framework.toLowerCase());
        if (generator == null) {
            logger.error("不支持的框架类型: {}", framework);
            throw new IllegalArgumentException("不支持的框架类型: " + framework);
        }
        return generator;
    }
}
