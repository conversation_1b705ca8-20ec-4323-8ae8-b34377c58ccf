package com.web.lowcode.generator.decorator;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.generator.CodeGenerator;

import java.util.List;
import java.util.Map;

/**
 * 代码生成器装饰器
 * 使用装饰器模式增强代码生成器功能
 */
public abstract class CodeGeneratorDecorator implements CodeGenerator {
    
    // 被装饰的代码生成器
    protected final CodeGenerator generator;
    
    /**
     * 构造函数
     * @param generator 被装饰的代码生成器
     */
    public CodeGeneratorDecorator(CodeGenerator generator) {
        this.generator = generator;
    }
    
    /**
     * 生成页面代码
     * 默认委托给被装饰的生成器
     */
    @Override
    public String generatePageCode(PageEntity page, List<PageEntity> pages) {
        return generator.generatePageCode(page, pages);
    }
    
    /**
     * 生成项目文件
     * 默认委托给被装饰的生成器
     */
    @Override
    public Map<String, String> generateProjectFiles(ProjectEntity project, List<PageEntity> pages) {
        return generator.generateProjectFiles(project, pages);
    }
    
    /**
     * 生成主入口文件
     * 默认委托给被装饰的生成器
     */
    @Override
    public String generateMainFile() {
        return generator.generateMainFile();
    }
    
    /**
     * 生成路由配置文件
     * 默认委托给被装饰的生成器
     */
    @Override
    public String generateRouterFile(List<PageEntity> pages) {
        return generator.generateRouterFile(pages);
    }
    
    /**
     * 生成应用配置文件
     * 默认委托给被装饰的生成器
     */
    @Override
    public String generateAppFile() {
        return generator.generateAppFile();
    }
}
