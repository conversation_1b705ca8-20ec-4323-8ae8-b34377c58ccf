package com.web.lowcode.generator.decorator;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.generator.CodeGenerator;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * UniApp导出装饰器
 * 为UniApp项目添加特定的导出功能
 */
@Slf4j
public class UniAppExportDecorator extends CodeGeneratorDecorator {
    
    /**
     * 构造函数
     * @param generator 被装饰的代码生成器
     */
    public UniAppExportDecorator(CodeGenerator generator) {
        super(generator);
    }
    
    /**
     * 增强的项目文件生成方法
     * 添加UniApp特定的文件
     */
    @Override
    public Map<String, String> generateProjectFiles(ProjectEntity project, List<PageEntity> pages) {
        log.info("使用UniApp导出装饰器生成项目文件，项目: {}", project.getName());
        
        // 获取原始生成器生成的文件
        Map<String, String> files = super.generateProjectFiles(project, pages);
        
        // 添加UniApp特定的文件
        files.putAll(generateUniAppSpecificFiles(project, pages));
        
        log.info("UniApp导出装饰器添加了特定的文件，总文件数: {}", files.size());
        return files;
    }
    
    /**
     * 生成UniApp特定的文件
     * @param project 项目实体
     * @param pages 项目页面列表
     * @return UniApp特定的文件
     */
    private Map<String, String> generateUniAppSpecificFiles(ProjectEntity project, List<PageEntity> pages) {
        Map<String, String> uniAppFiles = new HashMap<>();
        
        // 添加UniApp特定的README文件
        uniAppFiles.put("README.md", generateUniAppReadme(project, pages));
        
        // 添加UniApp开发指南
        uniAppFiles.put("docs/UniApp开发指南.md", generateUniAppDevGuide());
        
        // 添加UniApp部署指南
        uniAppFiles.put("docs/UniApp部署指南.md", generateUniAppDeployGuide());
        
        // 添加UniApp常见问题
        uniAppFiles.put("docs/UniApp常见问题.md", generateUniAppFAQ());
        
        return uniAppFiles;
    }
    
    /**
     * 生成UniApp README文件
     */
    private String generateUniAppReadme(ProjectEntity project, List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("# ").append(project.getName()).append(" (UniApp版本)\n\n");
        sb.append(project.getDescription()).append("\n\n");
        
        sb.append("## 项目结构\n\n");
        sb.append("本项目由低代码平台自动生成，基于UniApp框架开发，包含以下页面：\n\n");
        
        for (PageEntity page : pages) {
            sb.append("- ").append(page.getTitle()).append(" (").append(page.getName()).append(")\n");
        }
        
        sb.append("\n## 快速开始\n\n");
        sb.append("### 安装依赖\n\n");
        sb.append("```bash\nnpm install\n```\n\n");
        
        sb.append("### 开发模式\n\n");
        sb.append("```bash\nnpm run dev:h5\n```\n\n");
        
        sb.append("### 构建生产版本\n\n");
        sb.append("```bash\nnpm run build:h5\n```\n\n");
        
        sb.append("## 多平台支持\n\n");
        sb.append("UniApp支持多个平台，包括：\n\n");
        sb.append("- H5\n");
        sb.append("- 微信小程序\n");
        sb.append("- 支付宝小程序\n");
        sb.append("- App (Android/iOS)\n\n");
        
        sb.append("详细的开发和部署指南，请参阅 `docs` 目录下的文档。\n");
        
        return sb.toString();
    }
    
    /**
     * 生成UniApp开发指南
     */
    private String generateUniAppDevGuide() {
        return """
                # UniApp开发指南
                
                ## 简介
                
                UniApp是一个使用Vue.js开发所有前端应用的框架，开发者编写一套代码，可发布到iOS、Android、Web（响应式）、以及各种小程序（微信/支付宝/百度/头条/QQ/钉钉/淘宝）、快应用等多个平台。
                
                ## 开发环境搭建
                
                ### 安装开发工具
                
                推荐使用HBuilderX作为开发工具，它内置了uni-app的运行环境和各种调试工具。
                
                1. 下载安装HBuilderX：[https://www.dcloud.io/hbuilderx.html](https://www.dcloud.io/hbuilderx.html)
                2. 安装相关插件：uni-app编译器、scss/sass编译、es6转es5等
                
                ### 命令行开发
                
                如果你习惯使用命令行开发，可以按照以下步骤进行：
                
                1. 安装Vue CLI
                ```bash
                npm install -g @vue/cli
                ```
                
                2. 创建uni-app项目
                ```bash
                vue create -p dcloudio/uni-preset-vue my-project
                ```
                
                3. 运行项目
                ```bash
                npm run dev:h5
                ```
                
                ## 项目结构
                
                ```
                ├── src                 # 源代码目录
                │   ├── pages           # 页面文件目录
                │   ├── static          # 静态资源目录
                │   ├── components      # 组件目录
                │   ├── App.vue         # 应用配置，用来配置App全局样式以及监听应用生命周期
                │   ├── main.js         # Vue初始化入口文件
                │   ├── manifest.json   # 配置应用名称、appid、logo、版本等打包信息
                │   └── pages.json      # 配置页面路由、导航条、选项卡等页面类信息
                ├── public              # 公共资源目录
                ├── package.json        # 项目依赖配置文件
                └── README.md           # 项目说明文档
                ```
                
                ## 页面开发
                
                UniApp页面采用Vue单文件组件（SFC）规范，每个页面是一个.vue文件，由template、script、style三部分组成。
                
                ### 页面路由
                
                UniApp的页面路由需要在pages.json中进行配置：
                
                ```json
                {
                  "pages": [
                    {
                      "path": "pages/index/index",
                      "style": {
                        "navigationBarTitleText": "首页"
                      }
                    }
                  ]
                }
                ```
                
                ### 页面跳转
                
                ```javascript
                // 保留当前页面，跳转到应用内的某个页面
                uni.navigateTo({
                  url: '/pages/detail/detail?id=1'
                })
                
                // 关闭当前页面，跳转到应用内的某个页面
                uni.redirectTo({
                  url: '/pages/detail/detail?id=1'
                })
                ```
                
                ## 组件使用
                
                UniApp提供了丰富的内置组件，同时也支持使用第三方Vue组件。
                
                ### 内置组件
                
                ```html
                <template>
                  <view class="container">
                    <text>这是一个文本</text>
                    <button @click="handleClick">按钮</button>
                    <image src="/static/logo.png"></image>
                  </view>
                </template>
                ```
                
                ### 自定义组件
                
                ```html
                <!-- 自定义组件 MyComponent.vue -->
                <template>
                  <view class="my-component">
                    <text>{{ title }}</text>
                  </view>
                </template>
                
                <script>
                export default {
                  props: {
                    title: {
                      type: String,
                      default: ''
                    }
                  }
                }
                </script>
                
                <!-- 使用自定义组件 -->
                <template>
                  <view>
                    <my-component title="自定义组件"></my-component>
                  </view>
                </template>
                
                <script>
                import MyComponent from '@/components/MyComponent.vue'
                
                export default {
                  components: {
                    MyComponent
                  }
                }
                </script>
                ```
                
                ## API使用
                
                UniApp提供了丰富的API，可以调用设备功能、网络请求等。
                
                ### 网络请求
                
                ```javascript
                uni.request({
                  url: 'https://api.example.com/data',
                  method: 'GET',
                  data: {
                    id: 1
                  },
                  success: (res) => {
                    console.log(res.data)
                  },
                  fail: (err) => {
                    console.error(err)
                  }
                })
                ```
                
                ### 数据缓存
                
                ```javascript
                // 存储数据
                uni.setStorage({
                  key: 'userInfo',
                  data: {
                    name: '张三',
                    age: 18
                  }
                })
                
                // 获取数据
                uni.getStorage({
                  key: 'userInfo',
                  success: (res) => {
                    console.log(res.data)
                  }
                })
                ```
                
                ## 样式开发
                
                UniApp支持使用CSS、SCSS、LESS等预处理器编写样式。
                
                ```html
                <style lang="scss">
                .container {
                  display: flex;
                  flex-direction: column;
                  
                  .title {
                    font-size: 18px;
                    color: #333;
                  }
                }
                </style>
                ```
                
                ## 条件编译
                
                UniApp支持条件编译，可以针对不同平台编写特定代码。
                
                ```html
                <!-- #ifdef H5 -->
                <view>只在H5平台显示</view>
                <!-- #endif -->
                
                <!-- #ifdef MP-WEIXIN -->
                <view>只在微信小程序显示</view>
                <!-- #endif -->
                ```
                
                ```javascript
                // #ifdef H5
                console.log('只在H5平台执行')
                // #endif
                
                // #ifdef MP-WEIXIN
                console.log('只在微信小程序执行')
                // #endif
                ```
                
                ## 更多资源
                
                - [UniApp官方文档](https://uniapp.dcloud.io/)
                - [UniApp组件库](https://uniapp.dcloud.io/component/)
                - [UniApp API参考](https://uniapp.dcloud.io/api/)
                - [UniApp插件市场](https://ext.dcloud.net.cn/)
                """;
    }
    
    /**
     * 生成UniApp部署指南
     */
    private String generateUniAppDeployGuide() {
        return """
                # UniApp部署指南
                
                ## H5平台部署
                
                ### 构建H5版本
                
                ```bash
                # 进入项目目录
                cd your-project
                
                # 安装依赖
                npm install
                
                # 构建H5版本
                npm run build:h5
                ```
                
                构建完成后，会在项目根目录下生成 `dist/build/h5` 目录，里面包含了构建后的H5版本文件。
                
                ### 部署到Web服务器
                
                将 `dist/build/h5` 目录下的所有文件上传到Web服务器的根目录或者子目录即可。
                
                #### Nginx配置示例
                
                ```nginx
                server {
                    listen 80;
                    server_name your-domain.com;
                
                    root /path/to/dist/build/h5;
                    index index.html;
                
                    location / {
                        try_files $uri $uri/ /index.html;
                    }
                
                    # 如果有API请求需要代理
                    location /api {
                        proxy_pass http://your-api-server;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                    }
                }
                ```
                
                ## 微信小程序部署
                
                ### 构建微信小程序版本
                
                ```bash
                # 构建微信小程序版本
                npm run build:mp-weixin
                ```
                
                构建完成后，会在项目根目录下生成 `dist/build/mp-weixin` 目录，里面包含了构建后的微信小程序版本文件。
                
                ### 上传到微信小程序平台
                
                1. 下载并安装[微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
                2. 打开微信开发者工具，导入项目（选择 `dist/build/mp-weixin` 目录）
                3. 在微信开发者工具中，点击"上传"按钮，填写版本号和项目备注，上传代码
                4. 登录[微信公众平台](https://mp.weixin.qq.com/)，进入"版本管理"页面，提交审核
                5. 审核通过后，发布小程序
                
                ## App部署（Android/iOS）
                
                ### 使用HBuilderX云打包
                
                1. 使用HBuilderX打开项目
                2. 点击菜单"发行" -> "原生App-云打包"
                3. 配置应用名称、包名、图标等信息
                4. 选择打包平台（Android/iOS）
                5. 点击"打包"按钮
                6. 等待云打包完成，下载安装包
                
                ### 使用本地打包（高级）
                
                #### Android打包
                
                1. 构建App版本
                ```bash
                npm run build:app-plus
                ```
                
                2. 使用Android Studio导入项目
                ```
                # 进入项目目录
                cd your-project
                
                # 将项目转换为Android Studio项目
                npx @dcloudio/vue-cli-plugin-uni buildAndroidStudio
                ```
                
                3. 使用Android Studio打开生成的Android项目，进行打包
                
                #### iOS打包
                
                1. 构建App版本
                ```bash
                npm run build:app-plus
                ```
                
                2. 使用Xcode导入项目
                ```
                # 进入项目目录
                cd your-project
                
                # 将项目转换为Xcode项目
                npx @dcloudio/vue-cli-plugin-uni buildIOS
                ```
                
                3. 使用Xcode打开生成的iOS项目，进行打包
                
                ## 其他小程序平台部署
                
                UniApp支持多个小程序平台，部署流程类似：
                
                1. 构建对应平台的版本
                ```bash
                # 支付宝小程序
                npm run build:mp-alipay
                
                # 百度小程序
                npm run build:mp-baidu
                
                # 头条小程序
                npm run build:mp-toutiao
                
                # QQ小程序
                npm run build:mp-qq
                ```
                
                2. 使用对应平台的开发者工具导入项目
                3. 上传代码，提交审核，发布应用
                
                ## 环境变量配置
                
                UniApp支持使用环境变量来区分不同的环境（开发、测试、生产）。
                
                ### 创建环境变量文件
                
                在项目根目录下创建以下文件：
                
                - `.env`：所有环境通用的配置
                - `.env.development`：开发环境配置
                - `.env.production`：生产环境配置
                
                ### 环境变量格式
                
                ```
                # .env.development
                VUE_APP_API_URL=http://dev-api.example.com
                VUE_APP_DEBUG=true
                
                # .env.production
                VUE_APP_API_URL=https://api.example.com
                VUE_APP_DEBUG=false
                ```
                
                ### 使用环境变量
                
                ```javascript
                // 在代码中使用环境变量
                const apiUrl = process.env.VUE_APP_API_URL
                const debug = process.env.VUE_APP_DEBUG === 'true'
                
                console.log('API URL:', apiUrl)
                console.log('Debug mode:', debug)
                ```
                
                ## 常见问题排查
                
                ### H5版本部署后页面空白
                
                1. 检查是否配置了正确的publicPath
                2. 检查是否使用了history路由模式，如果是，需要配置服务器支持
                3. 检查控制台是否有报错信息
                
                ### 小程序上传失败
                
                1. 检查appid是否正确配置
                2. 检查项目是否超出大小限制
                3. 检查是否有使用不支持的API或组件
                
                ### App打包失败
                
                1. 检查manifest.json配置是否正确
                2. 检查是否有使用不支持的API或组件
                3. 检查证书配置（iOS）
                
                ## 更多资源
                
                - [UniApp官方部署文档](https://uniapp.dcloud.io/collocation/package)
                - [微信小程序发布流程](https://developers.weixin.qq.com/miniprogram/dev/framework/quickstart/release.html)
                - [支付宝小程序发布流程](https://opendocs.alipay.com/mini/introduce/release)
                - [HBuilderX打包文档](https://uniapp.dcloud.io/collocation/package)
                """;
    }
    
    /**
     * 生成UniApp常见问题
     */
    private String generateUniAppFAQ() {
        return """
                # UniApp常见问题解答
                
                ## 基础问题
                
                ### Q: UniApp是什么？
                
                A: UniApp是一个使用Vue.js开发所有前端应用的框架，开发者编写一套代码，可发布到iOS、Android、Web（响应式）、以及各种小程序（微信/支付宝/百度/头条/QQ/钉钉/淘宝）、快应用等多个平台。
                
                ### Q: UniApp与其他跨平台框架相比有什么优势？
                
                A: UniApp的主要优势包括：
                - 基于Vue.js，学习成本低
                - 支持平台最全面（小程序、App、H5等）
                - 丰富的组件和API
                - 优秀的性能
                - 完善的开发工具和生态
                
                ### Q: 如何开始一个UniApp项目？
                
                A: 有两种方式：
                1. 使用HBuilderX创建项目
                2. 使用Vue CLI创建项目：`vue create -p dcloudio/uni-preset-vue my-project`
                
                ## 开发问题
                
                ### Q: 如何在UniApp中使用第三方Vue组件？
                
                A: UniApp支持使用大部分第三方Vue组件，但需要注意以下几点：
                1. 组件不能使用浏览器或小程序特有的API
                2. 组件的DOM操作需要谨慎处理
                3. 推荐使用UniApp插件市场中的组件
                
                ### Q: 如何处理不同平台的差异？
                
                A: UniApp提供了条件编译功能，可以针对不同平台编写特定代码：
                
                ```html
                <!-- #ifdef H5 -->
                <view>只在H5平台显示</view>
                <!-- #endif -->
                
                <!-- #ifdef MP-WEIXIN -->
                <view>只在微信小程序显示</view>
                <!-- #endif -->
                ```
                
                ```javascript
                // #ifdef H5
                console.log('只在H5平台执行')
                // #endif
                ```
                
                ### Q: 如何处理页面跳转和参数传递？
                
                A: UniApp提供了统一的页面跳转API：
                
                ```javascript
                // 跳转到新页面
                uni.navigateTo({
                  url: '/pages/detail/detail?id=1&name=uniapp'
                })
                
                // 在目标页面获取参数
                export default {
                  onLoad(options) {
                    console.log(options.id) // 1
                    console.log(options.name) // uniapp
                  }
                }
                ```
                
                ### Q: 如何处理网络请求？
                
                A: UniApp提供了统一的网络请求API：
                
                ```javascript
                uni.request({
                  url: 'https://api.example.com/data',
                  method: 'GET',
                  data: {
                    id: 1
                  },
                  success: (res) => {
                    console.log(res.data)
                  },
                  fail: (err) => {
                    console.error(err)
                  }
                })
                ```
                
                也可以使用Promise方式：
                
                ```javascript
                uni.request({
                  url: 'https://api.example.com/data',
                  method: 'GET',
                  data: {
                    id: 1
                  }
                }).then(res => {
                  console.log(res[1].data)
                }).catch(err => {
                  console.error(err)
                })
                ```
                
                ## 样式问题
                
                ### Q: UniApp中如何适配不同屏幕尺寸？
                
                A: UniApp支持使用rpx（responsive pixel）单位，可以自动适配不同屏幕尺寸：
                
                ```css
                .container {
                  width: 750rpx; /* 等同于屏幕宽度 */
                  height: 100rpx;
                  font-size: 28rpx;
                }
                ```
                
                ### Q: 如何使用Sass/SCSS？
                
                A: 在style标签中添加lang="scss"属性，并安装相关依赖：
                
                ```bash
                npm install -D sass sass-loader
                ```
                
                ```html
                <style lang="scss">
                $primary-color: #007AFF;
                
                .container {
                  color: $primary-color;
                  
                  .title {
                    font-size: 28rpx;
                  }
                }
                </style>
                ```
                
                ### Q: 如何实现全局样式？
                
                A: 在App.vue中定义全局样式：
                
                ```html
                <style>
                /* 全局样式 */
                page {
                  background-color: #f8f8f8;
                  font-size: 16px;
                }
                
                .common-btn {
                  background-color: #007AFF;
                  color: #fff;
                }
                </style>
                ```
                
                ## 性能优化
                
                ### Q: 如何优化UniApp应用性能？
                
                A: 常见的性能优化方法包括：
                
                1. 减少不必要的组件嵌套
                2. 使用v-if代替v-show（在小程序中）
                3. 避免频繁操作DOM
                4. 合理使用生命周期函数
                5. 图片懒加载和压缩
                6. 使用分包加载（小程序）
                
                ### Q: 如何实现小程序分包加载？
                
                A: 在pages.json中配置分包：
                
                ```json
                {
                  "pages": [
                    {
                      "path": "pages/index/index",
                      "style": { ... }
                    }
                  ],
                  "subPackages": [
                    {
                      "root": "pagesA",
                      "pages": [
                        {
                          "path": "list/list",
                          "style": { ... }
                        }
                      ]
                    }
                  ]
                }
                ```
                
                ## 部署问题
                
                ### Q: 小程序包大小超出限制怎么办？
                
                A: 微信小程序限制主包大小为2MB，可以采取以下措施：
                
                1. 使用分包加载
                2. 压缩图片和资源
                3. 移除未使用的组件和代码
                4. 使用CDN加载大文件
                
                ### Q: H5版本部署后页面空白怎么办？
                
                A: 常见原因包括：
                
                1. 路径问题：检查publicPath配置
                2. 路由模式：如果使用history模式，需要配置服务器支持
                3. 跨域问题：检查API请求是否有跨域问题
                
                ### Q: App打包后无法访问网络怎么办？
                
                A: 检查以下几点：
                
                1. Android需要在manifest.json中配置网络权限
                2. iOS需要在info.plist中配置ATS设置
                3. 检查网络请求URL是否正确
                4. 检查是否使用了HTTP而非HTTPS（iOS默认禁止HTTP）
                
                ## 其他问题
                
                ### Q: 如何调试UniApp应用？
                
                A: 不同平台有不同的调试方法：
                
                1. H5：使用浏览器开发者工具
                2. 小程序：使用对应平台的开发者工具
                3. App：使用HBuilderX内置的调试工具或使用vConsole
                
                ### Q: 如何集成第三方SDK？
                
                A: UniApp支持通过原生插件集成第三方SDK：
                
                1. 使用UniApp插件市场中的插件
                2. 使用原生插件（需要HBuilderX）
                3. 使用uni.requireNativePlugin API调用原生插件
                
                ### Q: 如何实现推送通知？
                
                A: 可以使用UniPush或其他第三方推送服务：
                
                1. 在manifest.json中配置UniPush
                2. 使用uni.onPushMessage监听推送消息
                3. 使用uni.getPushClientId获取客户端推送标识
                
                ## 资源与支持
                
                ### Q: 哪里可以获取更多UniApp学习资源？
                
                A: 推荐以下资源：
                
                1. [UniApp官方文档](https://uniapp.dcloud.io/)
                2. [UniApp插件市场](https://ext.dcloud.net.cn/)
                3. [DCloud论坛](https://ask.dcloud.net.cn/explore/)
                4. [UniApp GitHub仓库](https://github.com/dcloudio/uni-app)
                
                ### Q: 如何获取技术支持？
                
                A: 可以通过以下渠道获取支持：
                
                1. DCloud官方论坛
                2. UniApp GitHub Issues
                3. DCloud官方QQ群
                4. 付费技术支持服务
                """;
    }
}
