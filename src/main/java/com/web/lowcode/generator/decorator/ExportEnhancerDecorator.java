package com.web.lowcode.generator.decorator;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.generator.CodeGenerator;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出增强装饰器
 * 增强代码生成器的导出功能
 */
@Slf4j
public class ExportEnhancerDecorator extends CodeGeneratorDecorator {
    
    /**
     * 构造函数
     * @param generator 被装饰的代码生成器
     */
    public ExportEnhancerDecorator(CodeGenerator generator) {
        super(generator);
    }
    
    /**
     * 增强的项目文件生成方法
     * 添加额外的导出相关文件
     */
    @Override
    public Map<String, String> generateProjectFiles(ProjectEntity project, List<PageEntity> pages) {
        log.info("使用导出增强装饰器生成项目文件，项目: {}", project.getName());
        
        // 获取原始生成器生成的文件
        Map<String, String> files = super.generateProjectFiles(project, pages);
        
        // 添加额外的导出相关文件
        files.putAll(generateExportFiles(project, pages));
        
        log.info("导出增强装饰器添加了额外的文件，总文件数: {}", files.size());
        return files;
    }
    
    /**
     * 生成导出相关文件
     * @param project 项目实体
     * @param pages 项目页面列表
     * @return 导出相关文件
     */
    private Map<String, String> generateExportFiles(ProjectEntity project, List<PageEntity> pages) {
        Map<String, String> exportFiles = new HashMap<>();
        
        // 添加README文件
        exportFiles.put("README.md", generateReadme(project, pages));
        
        // 添加项目说明文档
        exportFiles.put("docs/项目说明.md", generateProjectDoc(project));
        
        // 添加页面说明文档
        exportFiles.put("docs/页面说明.md", generatePagesDoc(pages));
        
        // 添加部署说明文档
        exportFiles.put("docs/部署说明.md", generateDeploymentDoc());
        
        return exportFiles;
    }
    
    /**
     * 生成README文件
     */
    private String generateReadme(ProjectEntity project, List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("# ").append(project.getName()).append("\n\n");
        sb.append(project.getDescription()).append("\n\n");
        
        sb.append("## 项目结构\n\n");
        sb.append("本项目由低代码平台自动生成，包含以下页面：\n\n");
        
        for (PageEntity page : pages) {
            sb.append("- ").append(page.getTitle()).append(" (").append(page.getName()).append(")\n");
        }
        
        sb.append("\n## 快速开始\n\n");
        sb.append("### 安装依赖\n\n");
        sb.append("```bash\nnpm install\n```\n\n");
        
        sb.append("### 开发模式\n\n");
        sb.append("```bash\nnpm run dev\n```\n\n");
        
        sb.append("### 构建生产版本\n\n");
        sb.append("```bash\nnpm run build\n```\n\n");
        
        sb.append("## 文档\n\n");
        sb.append("更多详细信息，请参阅 `docs` 目录下的文档。\n");
        
        return sb.toString();
    }
    
    /**
     * 生成项目说明文档
     */
    private String generateProjectDoc(ProjectEntity project) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("# 项目说明\n\n");
        sb.append("## 项目信息\n\n");
        sb.append("- 项目名称：").append(project.getName()).append("\n");
        sb.append("- 项目描述：").append(project.getDescription()).append("\n");
        sb.append("- 创建时间：").append(project.getCreateTime()).append("\n");
        sb.append("- 更新时间：").append(project.getUpdateTime()).append("\n\n");
        
        sb.append("## 项目配置\n\n");
        sb.append("项目配置文件位于项目根目录下的 `.env` 文件，可以根据需要进行修改。\n\n");
        
        sb.append("## 项目依赖\n\n");
        sb.append("项目依赖配置在 `package.json` 文件中，主要包括：\n\n");
        sb.append("- Vue.js：前端框架\n");
        sb.append("- Vite：构建工具\n");
        sb.append("- Axios：HTTP 客户端\n");
        sb.append("- Element Plus：UI 组件库\n");
        
        return sb.toString();
    }
    
    /**
     * 生成页面说明文档
     */
    private String generatePagesDoc(List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("# 页面说明\n\n");
        sb.append("本项目包含以下页面：\n\n");
        
        for (PageEntity page : pages) {
            sb.append("## ").append(page.getTitle()).append("\n\n");
            sb.append("- 页面名称：").append(page.getName()).append("\n");
            sb.append("- 页面路径：").append(page.getPath()).append("\n");
            sb.append("- 创建时间：").append(page.getCreateTime()).append("\n");
            sb.append("- 更新时间：").append(page.getUpdateTime()).append("\n\n");
            
            sb.append("### 页面组件\n\n");
            sb.append("页面组件配置详情请参考源代码。\n\n");
        }
        
        return sb.toString();
    }
    
    /**
     * 生成部署说明文档
     */
    private String generateDeploymentDoc() {
        return """
                # 部署说明
                
                ## 环境要求
                
                - Node.js 16+
                - npm 7+
                
                ## 开发环境部署
                
                1. 安装依赖
                
                ```bash
                npm install
                ```
                
                2. 启动开发服务器
                
                ```bash
                npm run dev
                ```
                
                3. 在浏览器中访问 http://localhost:3000
                
                ## 生产环境部署
                
                1. 安装依赖
                
                ```bash
                npm install
                ```
                
                2. 构建生产版本
                
                ```bash
                npm run build
                ```
                
                3. 部署 `dist` 目录下的文件到 Web 服务器
                
                ### Nginx 配置示例
                
                ```nginx
                server {
                    listen 80;
                    server_name your-domain.com;
                
                    root /path/to/dist;
                    index index.html;
                
                    location / {
                        try_files $uri $uri/ /index.html;
                    }
                
                    location /api {
                        proxy_pass http://your-backend-api;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                    }
                }
                ```
                
                ## 环境变量配置
                
                项目使用以下环境变量文件：
                
                - `.env`：所有环境通用的配置
                - `.env.development`：开发环境配置
                - `.env.production`：生产环境配置
                
                可以根据需要修改这些文件中的配置。
                """;
    }
}
