package com.web.lowcode.generator.decorator;

import com.web.lowcode.generator.CodeGenerator;
import com.web.lowcode.generator.VueCodeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 代码生成器装饰器工厂
 * 负责创建和管理代码生成器装饰器
 */
@Component
@Slf4j
public class CodeGeneratorDecoratorFactory {

    // 装饰器缓存
    private final Map<String, CodeGenerator> decoratorCache = new HashMap<>();

    /**
     * 根据框架类型创建适当的装饰器
     * @param generator 原始代码生成器
     * @param framework 框架类型
     * @return 增强后的代码生成器
     */
    public CodeGenerator createDecorator(CodeGenerator generator, String framework) {
        log.info("为框架 {} 创建装饰器", framework);

        // 默认使用导出增强装饰器
        CodeGenerator decoratedGenerator = new ExportEnhancerDecorator(generator);

        // 根据框架类型添加不同的装饰器
        if ("vue".equalsIgnoreCase(framework)) {
            // 应用 Vue 特定的装饰器
            log.info("应用 Vue 特定的装饰器");
            // 这里可以添加 Vue 特定的装饰器
            decoratedGenerator = new ExportEnhancerDecorator(decoratedGenerator);
        } else if ("uniapp".equalsIgnoreCase(framework)) {
            // 应用 UniApp 特定的装饰器
            log.info("应用 UniApp 特定的装饰器");
            decoratedGenerator = new UniAppExportDecorator(decoratedGenerator);
        }

        // 缓存装饰器实例
        String cacheKey = generator.getClass().getName() + "_" + framework;
        decoratorCache.put(cacheKey, decoratedGenerator);

        return decoratedGenerator;
    }
}
