package com.web.lowcode.generator;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;

import java.util.List;
import java.util.Map;

/**
 * 代码生成器接口
 * 定义了不同框架代码生成的标准接口
 */
public interface CodeGenerator {
    /**
     * 生成页面代码
     * @param page 当前页面实体
     * @param pages 项目中的所有页面列表，用于生成路由等关联信息
     * @return 生成的页面代码
     */
    String generatePageCode(PageEntity page, List<PageEntity> pages);

    /**
     * 生成项目文件
     * @param project 项目实体
     * @param pages 项目中的所有页面列表
     * @return 生成的文件映射，key为文件路径，value为文件内容
     */
    Map<String, String> generateProjectFiles(ProjectEntity project, List<PageEntity> pages);

    /**
     * 生成主入口文件
     * @return 生成的主入口文件代码
     */
    String generateMainFile();

    /**
     * 生成路由配置文件
     * @param pages 项目中的所有页面列表
     * @return 生成的路由配置代码
     */
    String generateRouterFile(List<PageEntity> pages);

    /**
     * 生成应用配置文件
     * @return 生成的应用配置代码
     */
    String generateAppFile();
}
