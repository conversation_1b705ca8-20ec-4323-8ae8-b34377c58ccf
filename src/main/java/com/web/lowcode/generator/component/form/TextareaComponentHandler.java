package com.web.lowcode.generator.component.form;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.FormProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;

/**
 * 文本区域组件处理器
 */
public class TextareaComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理文本区域特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 设置类型为textarea
            templateBuilder.addProp("type", "textarea");

            // 处理行数
            if (props.has(FormProps.ROWS)) {
                templateBuilder.addProp(":rows", props.optString(FormProps.ROWS));
            }

            // 处理自动调整高度
            if (props.has(FormProps.AUTOSIZE) && props.getBoolean(FormProps.AUTOSIZE)) {
                // 构建autosize对象
                StringBuilder autosizeValue = new StringBuilder("{");

                if (props.has(FormProps.MIN_ROWS)) {
                    autosizeValue.append("minRows: ").append(props.optInt(FormProps.MIN_ROWS, 2));
                }

                if (props.has(FormProps.MAX_ROWS)) {
                    if (props.has(FormProps.MIN_ROWS)) {
                        autosizeValue.append(", ");
                    }
                    autosizeValue.append("maxRows: ").append(props.optInt(FormProps.MAX_ROWS, 6));
                }

                autosizeValue.append("}");

                templateBuilder.addProp(":autosize", autosizeValue.toString());
            }
        }
    }

    @Override
    public void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理事件
        super.handleEvents(component, templateBuilder);
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 文本区域组件没有内容
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.TEXTAREA;
    }
}
