package com.web.lowcode.generator.component.style;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.constants.ComponentConstants.JsonProps;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 列组件处理器
 */
public class ColumnComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理列组件特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 处理列宽
            if (props.has("span")) {
                templateBuilder.addProp(":span", props.optString("span"));
            } else {
                // 默认列宽
                templateBuilder.addProp(":span", "12");
            }

            // 处理偏移
            if (props.has("offset")) {
                templateBuilder.addProp(":offset", props.optString("offset"));
            }

            // 处理子组件
            if (props.has(JsonProps.CHILDREN)) {
                JSONArray children = props.getJSONArray(JsonProps.CHILDREN);
                if (children.length() > 0) {
                    templateBuilder.setHasChildren(true);
                    component.put(JsonProps.CHILDREN, children);
                }
            }
        }
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 列组件的内容是子组件，已经在handleProps中处理了
        templateBuilder.setHasChildren(component.has(JsonProps.CHILDREN) ||
                                      (component.has(JsonProps.PROPS) &&
                                       component.getJSONObject(JsonProps.PROPS).has(JsonProps.CHILDREN)));
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.COL;
    }
}
