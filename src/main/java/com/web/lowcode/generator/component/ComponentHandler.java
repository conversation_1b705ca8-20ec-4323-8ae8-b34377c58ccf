package com.web.lowcode.generator.component;

import org.json.JSONObject;

/**
 * 组件处理器接口
 * 负责处理特定类型的组件
 */
public interface ComponentHandler {
    /**
     * 处理组件属性
     * @param component 组件对象
     * @param templateBuilder 模板构建器
     */
    void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder);
    
    /**
     * 处理组件事件
     * @param component 组件对象
     * @param templateBuilder 模板构建器
     */
    void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder);
    
    /**
     * 处理组件内容
     * @param component 组件对象
     * @param templateBuilder 模板构建器
     */
    void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder);
    
    /**
     * 获取组件类型
     * @return 组件类型
     */
    String getComponentType();
}
