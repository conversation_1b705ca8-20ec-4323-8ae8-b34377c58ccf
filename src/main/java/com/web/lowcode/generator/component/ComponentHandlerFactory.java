package com.web.lowcode.generator.component;

import com.web.lowcode.generator.component.ComponentHandler;
import com.web.lowcode.generator.component.style.*;
import com.web.lowcode.generator.component.form.*;
import com.web.lowcode.generator.component.custom.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 组件处理器工厂
 * 负责创建组件处理器
 */
public class ComponentHandlerFactory {
    private static final Logger logger = LoggerFactory.getLogger(ComponentHandlerFactory.class);
    private static final Map<String, ComponentHandler> handlers = new HashMap<>();
    private static final ComponentHandler defaultHandler = new DefaultComponentHandler();

    static {
        // 注册组件处理器
        registerHandler(new ButtonComponentHandler());
        registerHandler(new AlertComponentHandler());
        registerHandler(new ContainerComponentHandler());
        registerHandler(new GridContainerComponentHandler());
        registerHandler(new RowComponentHandler());
        registerHandler(new ColumnComponentHandler());

        // 注册表单组件处理器
        registerHandler(new TextareaComponentHandler());
        registerHandler(new NumberInputComponentHandler());
        registerHandler(new PasswordInputComponentHandler());
        registerHandler(new SearchInputComponentHandler());

        // 注册自定义组件处理器
        registerHandler(new CustomComponentHandler());
        // 可以在这里注册更多组件处理器
    }

    /**
     * 注册组件处理器
     * @param handler 组件处理器
     */
    public static void registerHandler(ComponentHandler handler) {
        handlers.put(handler.getComponentType(), handler);
        logger.info("注册组件处理器: {}", handler.getComponentType());
    }

    /**
     * 获取组件处理器
     * @param componentType 组件类型
     * @return 组件处理器
     */
    public static ComponentHandler getHandler(String componentType) {
        ComponentHandler handler = handlers.get(componentType);
        if (handler == null) {
            logger.debug("未找到组件处理器: {}, 使用默认处理器", componentType);
            return defaultHandler;
        }
        return handler;
    }
}
