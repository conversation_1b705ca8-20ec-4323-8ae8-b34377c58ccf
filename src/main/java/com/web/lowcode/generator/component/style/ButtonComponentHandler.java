package com.web.lowcode.generator.component.style;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.constants.ComponentConstants.JsonProps;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;

/**
 * 按钮组件处理器
 */
public class ButtonComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理按钮特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 处理按钮类型
            String buttonType = props.optString("type", "");
            if (!buttonType.isEmpty()) {
                templateBuilder.addProp("type", buttonType);
            }

            // 处理按钮文本
            String buttonText = props.optString("text", "按钮");
            templateBuilder.setContent(buttonText);
        }
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.BUTTON;
    }
}
