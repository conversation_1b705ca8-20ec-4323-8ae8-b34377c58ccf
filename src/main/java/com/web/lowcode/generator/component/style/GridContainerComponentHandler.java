package com.web.lowcode.generator.component.style;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.constants.ComponentConstants.JsonProps;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 网格容器组件处理器
 */
public class GridContainerComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理网格容器特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 处理列数和间距
            if (props.has("cols")) {
                templateBuilder.addProp(":cols", props.optString("cols"));
            }

            if (props.has("gutter")) {
                templateBuilder.addProp(":gutter", props.optString("gutter"));
            }

            // 处理子组件
            if (props.has(JsonProps.CHILDREN)) {
                JSONArray children = props.getJSONArray(JsonProps.CHILDREN);
                if (!children.isEmpty()) {
                    templateBuilder.setHasChildren(true);
                    component.put(JsonProps.CHILDREN, children);
                }
            }

            // 添加网格样式
            JSONObject gridStyles = new JSONObject();
            gridStyles.put("display", "grid");

            // 从props中获取列数和间距
            int cols = props.optInt("cols", 3); // 默认值
            int gutter = props.optInt("gutter", 20); // 默认值

            gridStyles.put("grid-template-columns", "repeat(" + cols + ", 1fr)");
            gridStyles.put("gap", gutter + "px");

            // 添加到组件样式
            for (String key : gridStyles.keySet()) {
                templateBuilder.addStyle(key, gridStyles.optString(key));
            }
        }
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 网格容器组件的内容是子组件，已经在handleProps中处理了
        templateBuilder.setHasChildren(component.has(JsonProps.CHILDREN) ||
                                      (component.has(JsonProps.PROPS) &&
                                       component.getJSONObject(JsonProps.PROPS).has(JsonProps.CHILDREN)));
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.GRID_CONTAINER;
    }
}
