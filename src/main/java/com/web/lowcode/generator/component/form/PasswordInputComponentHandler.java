package com.web.lowcode.generator.component.form;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.FormProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;

/**
 * 密码输入框组件处理器
 */
public class PasswordInputComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理密码输入框特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 处理类型
            templateBuilder.addProp("type", "password");

            // 处理显示密码切换按钮
            if (props.has(FormProps.SHOW_PASSWORD)) {
                templateBuilder.addProp(":show-password", props.optString(FormProps.SHOW_PASSWORD));
            }
        }
    }

    @Override
    public void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理事件
        super.handleEvents(component, templateBuilder);
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 密码输入框组件没有内容
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.PASSWORD_INPUT;
    }
}
