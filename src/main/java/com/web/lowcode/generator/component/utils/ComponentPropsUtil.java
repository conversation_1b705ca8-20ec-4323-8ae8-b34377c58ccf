package com.web.lowcode.generator.component.utils;

import com.web.lowcode.generator.component.constants.ComponentConstants.CommonProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.FormProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.JsonProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 组件属性工具类
 * 提供处理组件属性的通用方法
 */
public class ComponentPropsUtil {
    private static final Logger logger = LoggerFactory.getLogger(ComponentPropsUtil.class);
    
    /**
     * 获取组件属性
     * @param component 组件对象
     * @return 属性对象，如果不存在则返回空JSONObject
     */
    public static JSONObject getProps(JSONObject component) {
        if (component.has(JsonProps.PROPS)) {
            return component.getJSONObject(JsonProps.PROPS);
        }
        return new JSONObject();
    }
    
    /**
     * 获取组件样式
     * @param component 组件对象
     * @return 样式对象，如果不存在则返回空JSONObject
     */
    public static JSONObject getStyles(JSONObject component) {
        if (component.has(JsonProps.STYLES)) {
            return component.getJSONObject(JsonProps.STYLES);
        }
        return new JSONObject();
    }
    
    /**
     * 获取组件事件
     * @param component 组件对象
     * @return 事件对象，如果不存在则返回空JSONObject
     */
    public static JSONObject getEvents(JSONObject component) {
        if (component.has(JsonProps.EVENTS)) {
            Object eventsObj = component.get(JsonProps.EVENTS);
            if (eventsObj instanceof JSONObject) {
                return (JSONObject) eventsObj;
            }
        }
        return new JSONObject();
    }
    
    /**
     * 获取组件类型
     * @param component 组件对象
     * @return 组件类型
     */
    public static String getComponentType(JSONObject component) {
        return component.optString(JsonProps.TYPE, "");
    }
    
    /**
     * 判断是否为表单组件
     * @param componentType 组件类型
     * @return 是否为表单组件
     */
    public static boolean isFormComponent(String componentType) {
        return componentType.equals(ComponentTypes.INPUT) || 
               componentType.equals(ComponentTypes.TEXTAREA) || 
               componentType.equals(ComponentTypes.NUMBER_INPUT) || 
               componentType.equals(ComponentTypes.PASSWORD_INPUT) || 
               componentType.equals(ComponentTypes.SEARCH_INPUT) || 
               componentType.equals(ComponentTypes.RADIO) || 
               componentType.equals(ComponentTypes.CHECKBOX) || 
               componentType.equals(ComponentTypes.SELECT) || 
               componentType.equals(ComponentTypes.SWITCH) || 
               componentType.equals(ComponentTypes.SLIDER);
    }
    
    /**
     * 获取属性值
     * @param props 属性对象
     * @param propName 属性名
     * @param defaultValue 默认值
     * @return 属性值
     */
    public static String getPropValue(JSONObject props, String propName, String defaultValue) {
        return props.optString(propName, defaultValue);
    }
    
    /**
     * 获取布尔属性值
     * @param props 属性对象
     * @param propName 属性名
     * @param defaultValue 默认值
     * @return 布尔属性值
     */
    public static boolean getBooleanPropValue(JSONObject props, String propName, boolean defaultValue) {
        return props.optBoolean(propName, defaultValue);
    }
    
    /**
     * 获取数字属性值
     * @param props 属性对象
     * @param propName 属性名
     * @param defaultValue 默认值
     * @return 数字属性值
     */
    public static int getIntPropValue(JSONObject props, String propName, int defaultValue) {
        return props.optInt(propName, defaultValue);
    }
    
    /**
     * 获取浮点数属性值
     * @param props 属性对象
     * @param propName 属性名
     * @param defaultValue 默认值
     * @return 浮点数属性值
     */
    public static double getDoublePropValue(JSONObject props, String propName, double defaultValue) {
        return props.optDouble(propName, defaultValue);
    }
    
    /**
     * 处理通用属性
     * @param props 属性对象
     * @param propBuilder 属性构建器
     */
    public static void handleCommonProps(JSONObject props, StringBuilder propBuilder) {
        // 处理禁用状态
        if (props.has(CommonProps.DISABLED)) {
            boolean disabled = props.optBoolean(CommonProps.DISABLED);
            propBuilder.append(" :disabled=\"").append(disabled).append("\"");
        }
        
        // 处理标题
        if (props.has(CommonProps.TITLE)) {
            String title = props.optString(CommonProps.TITLE);
            propBuilder.append(" title=\"").append(title).append("\"");
        }
        
        // 处理类名
        if (props.has(CommonProps.CLASS)) {
            String className = props.optString(CommonProps.CLASS);
            propBuilder.append(" class=\"").append(className).append("\"");
        }
    }
    
    /**
     * 处理表单属性
     * @param props 属性对象
     * @param propBuilder 属性构建器
     */
    public static void handleFormProps(JSONObject props, StringBuilder propBuilder) {
        // 处理默认值
        if (props.has(FormProps.DEFAULT_VALUE)) {
            String defaultValue = props.optString(FormProps.DEFAULT_VALUE);
            propBuilder.append(" v-model=\"").append(defaultValue).append("\"");
        }
        
        // 处理占位文本
        if (props.has(FormProps.PLACEHOLDER)) {
            String placeholder = props.optString(FormProps.PLACEHOLDER);
            propBuilder.append(" placeholder=\"").append(placeholder).append("\"");
        }
        
        // 处理可清空
        if (props.has(FormProps.CLEARABLE)) {
            boolean clearable = props.optBoolean(FormProps.CLEARABLE);
            propBuilder.append(" :clearable=\"").append(clearable).append("\"");
        }
        
        // 处理禁用状态
        if (props.has(FormProps.DISABLED)) {
            boolean disabled = props.optBoolean(FormProps.DISABLED);
            propBuilder.append(" :disabled=\"").append(disabled).append("\"");
        }
        
        // 处理尺寸
        if (props.has(FormProps.SIZE)) {
            String size = props.optString(FormProps.SIZE);
            propBuilder.append(" size=\"").append(size).append("\"");
        }
        
        // 处理最大长度
        if (props.has(FormProps.MAXLENGTH) && props.getInt(FormProps.MAXLENGTH) > 0) {
            int maxlength = props.getInt(FormProps.MAXLENGTH);
            propBuilder.append(" :maxlength=\"").append(maxlength).append("\"");
        }
        
        // 处理显示字数统计
        if (props.has(FormProps.SHOW_WORD_LIMIT)) {
            boolean showWordLimit = props.optBoolean(FormProps.SHOW_WORD_LIMIT);
            propBuilder.append(" :show-word-limit=\"").append(showWordLimit).append("\"");
        }
    }
    
    /**
     * 处理样式属性
     * @param styles 样式对象
     * @param styleBuilder 样式构建器
     */
    public static void handleStyleProps(JSONObject styles, StringBuilder styleBuilder) {
        // 处理宽度
        if (styles.has(CommonProps.WIDTH)) {
            String width = styles.optString(CommonProps.WIDTH);
            styleBuilder.append("width:").append(width).append(";");
        }
        
        // 处理高度
        if (styles.has(CommonProps.HEIGHT)) {
            String height = styles.optString(CommonProps.HEIGHT);
            styleBuilder.append("height:").append(height).append(";");
        }
        
        // 处理外边距
        if (styles.has(CommonProps.MARGIN)) {
            String margin = styles.optString(CommonProps.MARGIN);
            styleBuilder.append("margin:").append(margin).append(";");
        }
        
        // 处理内边距
        if (styles.has(CommonProps.PADDING)) {
            String padding = styles.optString(CommonProps.PADDING);
            styleBuilder.append("padding:").append(padding).append(";");
        }
        
        // 处理字体大小
        if (styles.has(CommonProps.FONT_SIZE)) {
            String fontSize = styles.optString(CommonProps.FONT_SIZE);
            styleBuilder.append("font-size:").append(fontSize).append(";");
        }
        
        // 处理字体颜色
        if (styles.has(CommonProps.COLOR)) {
            String color = styles.optString(CommonProps.COLOR);
            styleBuilder.append("color:").append(color).append(";");
        }
        
        // 处理背景颜色
        if (styles.has(CommonProps.BACKGROUND_COLOR)) {
            String backgroundColor = styles.optString(CommonProps.BACKGROUND_COLOR);
            styleBuilder.append("background-color:").append(backgroundColor).append(";");
        }
        
        // 处理边框
        if (styles.has(CommonProps.BORDER)) {
            String border = styles.optString(CommonProps.BORDER);
            styleBuilder.append("border:").append(border).append(";");
        }
        
        // 处理边框圆角
        if (styles.has(CommonProps.BORDER_RADIUS)) {
            String borderRadius = styles.optString(CommonProps.BORDER_RADIUS);
            styleBuilder.append("border-radius:").append(borderRadius).append(";");
        }
        
        // 处理对齐方式
        if (styles.has(CommonProps.TEXT_ALIGN)) {
            String textAlign = styles.optString(CommonProps.TEXT_ALIGN);
            styleBuilder.append("text-align:").append(textAlign).append(";");
        }
        
        // 处理显示方式
        if (styles.has(CommonProps.DISPLAY)) {
            String display = styles.optString(CommonProps.DISPLAY);
            styleBuilder.append("display:").append(display).append(";");
        }
        
        // 处理定位相关的样式
        if (styles.has(CommonProps.POSITION)) {
            String position = styles.optString(CommonProps.POSITION);
            styleBuilder.append("position:").append(position).append(";");
            
            // 如果是绝对定位或相对定位，处理坐标
            if ("absolute".equals(position) || "relative".equals(position) || "fixed".equals(position)) {
                if (styles.has(CommonProps.TOP)) {
                    styleBuilder.append("top:").append(styles.optString(CommonProps.TOP)).append(";");
                }
                if (styles.has(CommonProps.RIGHT)) {
                    styleBuilder.append("right:").append(styles.optString(CommonProps.RIGHT)).append(";");
                }
                if (styles.has(CommonProps.BOTTOM)) {
                    styleBuilder.append("bottom:").append(styles.optString(CommonProps.BOTTOM)).append(";");
                }
                if (styles.has(CommonProps.LEFT)) {
                    styleBuilder.append("left:").append(styles.optString(CommonProps.LEFT)).append(";");
                }
                if (styles.has(CommonProps.Z_INDEX)) {
                    styleBuilder.append("z-index:").append(styles.optString(CommonProps.Z_INDEX)).append(";");
                }
            }
        }
    }
}
