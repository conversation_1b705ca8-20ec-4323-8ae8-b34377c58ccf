package com.web.lowcode.generator.component.form;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.FormProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;

/**
 * 数字输入框组件处理器
 */
public class NumberInputComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理数字输入框特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 处理最小值
            if (props.has(FormProps.MIN)) {
                templateBuilder.addProp(":min", props.optString(FormProps.MIN));
            }

            // 处理最大值
            if (props.has(FormProps.MAX)) {
                templateBuilder.addProp(":max", props.optString(FormProps.MAX));
            }

            // 处理步长
            if (props.has(FormProps.STEP)) {
                templateBuilder.addProp(":step", props.optString(FormProps.STEP));
            }

            // 处理精度
            if (props.has(FormProps.PRECISION)) {
                templateBuilder.addProp(":precision", props.optString(FormProps.PRECISION));
            }

            // 处理严格步长
            if (props.has(FormProps.STEP_STRICTLY)) {
                templateBuilder.addProp(":step-strictly", props.optString(FormProps.STEP_STRICTLY));
            }

            // 处理控制按钮
            if (props.has(FormProps.CONTROLS)) {
                templateBuilder.addProp(":controls", props.optString(FormProps.CONTROLS));
            }
        }
    }

    @Override
    public void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理事件
        super.handleEvents(component, templateBuilder);
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 数字输入框组件没有内容
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.NUMBER_INPUT;
    }
}
