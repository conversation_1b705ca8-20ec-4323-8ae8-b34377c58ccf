package com.web.lowcode.generator.component;

import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 组件渲染器
 * 负责渲染组件
 */
public class ComponentRenderer {
    private static final Logger logger = LoggerFactory.getLogger(ComponentRenderer.class);
    private final Map<String, String> componentTypeMap = new HashMap<>();

    /**
     * 构造函数
     */
    public ComponentRenderer() {
        initComponentTypeMap();
    }

    /**
     * 初始化组件类型映射
     */
    private void initComponentTypeMap() {
        // 基础组件
        componentTypeMap.put("button", "el-button");
        componentTypeMap.put("input", "el-input");
        componentTypeMap.put("textarea", "el-input");
        componentTypeMap.put("number-input", "el-input-number");
        componentTypeMap.put("password-input", "el-input");
        componentTypeMap.put("search-input", "el-input");
        componentTypeMap.put("custom", "custom-component");
        componentTypeMap.put("select", "el-select");
        componentTypeMap.put("option", "el-option");
        componentTypeMap.put("checkbox", "el-checkbox");
        componentTypeMap.put("radio", "el-radio");
        componentTypeMap.put("switch", "el-switch");
        componentTypeMap.put("slider", "el-slider");

        // 表单组件
        componentTypeMap.put("form", "el-form");
        componentTypeMap.put("form-item", "el-form-item");
        componentTypeMap.put("date-picker", "el-date-picker");
        componentTypeMap.put("time-picker", "el-time-picker");

        // 数据展示组件
        componentTypeMap.put("table", "el-table");
        componentTypeMap.put("pagination", "el-pagination");
        componentTypeMap.put("tag", "el-tag");
        componentTypeMap.put("progress", "el-progress");
        componentTypeMap.put("badge", "el-badge");
        componentTypeMap.put("avatar", "el-avatar");
        componentTypeMap.put("calendar", "el-calendar");
        componentTypeMap.put("image", "el-image");

        // 导航组件
        componentTypeMap.put("menu", "el-menu");
        componentTypeMap.put("tabs", "el-tabs");
        componentTypeMap.put("tab-pane", "el-tab-pane");
        componentTypeMap.put("breadcrumb", "el-breadcrumb");

        // 反馈组件
        componentTypeMap.put("alert", "el-alert");
        componentTypeMap.put("dialog", "el-dialog");
        componentTypeMap.put("tooltip", "el-tooltip");
        componentTypeMap.put("popover", "el-popover");
        componentTypeMap.put("card", "el-card");
        componentTypeMap.put("carousel", "el-carousel");
        componentTypeMap.put("carousel-item", "el-carousel-item");

        // 布局组件
        componentTypeMap.put("container", "div");
        componentTypeMap.put("row", "el-row");
        componentTypeMap.put("column", "el-col");
        componentTypeMap.put("col", "el-col");
        componentTypeMap.put("divider", "el-divider");

        // 容器组件
        componentTypeMap.put("card-container", "div");
        componentTypeMap.put("tab-container", "el-tabs");
        componentTypeMap.put("collapse-container", "el-collapse");
        componentTypeMap.put("grid-container", "div");
    }

    /**
     * 将组件类型映射为Vue组件类型
     * @param componentType 组件类型
     * @return Vue组件类型
     */
    public String mapToVueComponent(String componentType) {
        return componentTypeMap.getOrDefault(componentType.toLowerCase(), componentType);
    }

    /**
     * 渲染组件
     * @param component 组件对象
     * @return 渲染后的组件模板
     */
    public String renderComponent(JSONObject component) {
        String type = component.optString("type", "div");
        String vueComponentType = mapToVueComponent(type);

        // 创建组件模板构建器
        ComponentTemplateBuilder templateBuilder = new ComponentTemplateBuilder(component, vueComponentType);

        // 获取组件处理器
        ComponentHandler handler = ComponentHandlerFactory.getHandler(type);

        // 处理组件属性
        handler.handleProps(component, templateBuilder);

        // 处理组件事件
        handler.handleEvents(component, templateBuilder);

        // 处理组件内容
        handler.handleContent(component, templateBuilder);

        // 构建组件模板
        StringBuilder sb = new StringBuilder();

        // 构建开始标签
        templateBuilder.buildStartTag();

        // 构建内容
        if (templateBuilder.hasChildren()) {
            // 如果有子组件，递归渲染子组件
            sb.append(templateBuilder.build());

            JSONArray children = component.getJSONArray("children");

            // 特殊处理行组件，确保子组件被包裹在el-col中
            if ("row".equals(type)) {
                for (int i = 0; i < children.length(); i++) {
                    JSONObject child = children.getJSONObject(i);
                    // 计算每列的宽度，默认平均分配
                    int span = 24 / children.length();

                    // 添加列组件包装
                    sb.append("\n");
                    for (int j = 0; j < templateBuilder.getIndentLevel() + 2; j++) {
                        sb.append(" ");
                    }
                    sb.append("<el-col :span=\"" + span + "\">\n");

                    // 渲染子组件
                    sb.append(renderComponent(child));

                    // 关闭列组件
                    for (int j = 0; j < templateBuilder.getIndentLevel() + 2; j++) {
                        sb.append(" ");
                    }
                    sb.append("</el-col>\n");
                }
            } else {
                // 正常渲染子组件
                for (int i = 0; i < children.length(); i++) {
                    JSONObject child = children.getJSONObject(i);
                    sb.append(renderComponent(child));
                }
            }

            // 添加缩进
            for (int i = 0; i < templateBuilder.getIndentLevel(); i++) {
                sb.append(" ");
            }

            // 添加结束标签
            sb.append("</").append(templateBuilder.getVueComponentType()).append(">");
        } else if (templateBuilder.getContent() != null) {
            // 如果有内容，添加内容和结束标签
            sb.append(templateBuilder.build());
            sb.append(">").append(templateBuilder.getContent()).append("</")
              .append(templateBuilder.getVueComponentType()).append(">");
        } else {
            // 否则使用自闭合标签
            sb.append(templateBuilder.build());
            sb.append(" />");
        }

        return sb.toString();
    }

    /**
     * 渲染组件数组
     * @param components 组件数组
     * @return 渲染后的组件模板
     */
    public String renderComponents(JSONArray components) {
        StringBuilder sb = new StringBuilder();

        // 添加外层容器，确保组件正确排列
        sb.append("    <div class=\"page-components\">\n");

        for (int i = 0; i < components.length(); i++) {
            JSONObject component = components.getJSONObject(i);
            String type = component.optString("type", "div");

            // 如果不是行组件，则包裹在一个独立的div中确保换行
            if (!"row".equals(type)) {
                sb.append("      <div class=\"component-container\">\n");
            }

            String renderedComponent = renderComponent(component);
            // 添加换行符
            if (!renderedComponent.endsWith("\n")) {
                renderedComponent += "\n";
            }
            sb.append(renderedComponent);

            // 如果不是行组件，则关闭包裹的div
            if (!"row".equals(type)) {
                sb.append("      </div>\n");
            }
        }

        sb.append("    </div>\n");

        return sb.toString();
    }
}
