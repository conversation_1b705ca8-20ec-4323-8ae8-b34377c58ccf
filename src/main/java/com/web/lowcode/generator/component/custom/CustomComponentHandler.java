package com.web.lowcode.generator.component.custom;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.constants.ComponentConstants.Events;
import com.web.lowcode.generator.component.constants.ComponentConstants.JsonProps;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 自定义组件处理器
 */
public class CustomComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理自定义组件特有属性
        if (component.has("elements")) {
            JSONArray elements = component.getJSONArray("elements");
            templateBuilder.addProp(":elements", elements.toString());
        }
    }

    @Override
    public void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本事件
        super.handleEvents(component, templateBuilder);
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 自定义组件没有内容，内容由元素列表决定
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.CUSTOM;
    }
}
