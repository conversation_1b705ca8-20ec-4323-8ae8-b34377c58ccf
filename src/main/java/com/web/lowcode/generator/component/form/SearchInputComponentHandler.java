package com.web.lowcode.generator.component.form;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.FormProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.constants.ComponentConstants.Events;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;

/**
 * 搜索输入框组件处理器
 */
public class SearchInputComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理搜索输入框特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 设置前缀图标
            if (props.has(FormProps.SHOW_PREFIX) && props.getBoolean(FormProps.SHOW_PREFIX)) {
                templateBuilder.setHasChildren(true);
                templateBuilder.addSlot("prefix", "<el-icon><Search /></el-icon>");
            }

            // 设置搜索按钮
            if (props.has(FormProps.SHOW_BUTTON) && props.getBoolean(FormProps.SHOW_BUTTON)) {
                templateBuilder.setHasChildren(true);

                String buttonType = props.optString(FormProps.BUTTON_TYPE, "primary");
                String buttonText = props.optString(FormProps.BUTTON_TEXT, "搜索");

                templateBuilder.addSlot("append", "<el-button type=\"" + buttonType + "\" @click=\"search\">" + buttonText + "</el-button>");
            }
        }
    }

    @Override
    public void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本事件
        super.handleEvents(component, templateBuilder);

        // 处理搜索输入框特有事件
        JSONObject events = ComponentPropsUtil.getEvents(component);
        if (!events.isEmpty()) {
            // 处理clear事件
            if (events.has(Events.CLEAR)) {
                templateBuilder.addEvent("@clear", events.optString(Events.CLEAR));
            }

            // 处理search事件
            if (events.has(Events.SEARCH)) {
                templateBuilder.addEvent("@click", events.optString(Events.SEARCH));
            }
        }
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 搜索输入框组件没有内容
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.SEARCH_INPUT;
    }
}
