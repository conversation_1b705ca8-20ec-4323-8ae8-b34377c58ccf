package com.web.lowcode.generator.component.style;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.constants.ComponentConstants;

/**
 * 默认组件处理器
 */
public class DefaultComponentHandler extends AbstractComponentHandler {
    @Override
    public String getComponentType() {
        return ComponentConstants.ComponentTypes.DEFAULT;
    }
}
