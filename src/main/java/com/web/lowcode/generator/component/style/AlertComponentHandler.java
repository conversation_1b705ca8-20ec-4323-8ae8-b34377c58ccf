package com.web.lowcode.generator.component.style;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.constants.ComponentConstants.JsonProps;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;

/**
 * 警告提示组件处理器
 */
public class AlertComponentHandler extends AbstractComponentHandler {
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理警告提示组件特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 处理标题
            String title = props.optString("title", "");
            if (!title.isEmpty()) {
                templateBuilder.addProp("title", title);
            }

            // 处理类型
            String type = props.optString("type", "info");
            templateBuilder.addProp("type", type);

            // 处理是否可关闭
            boolean closable = props.optBoolean("closable", false);
            templateBuilder.addProp(":closable", String.valueOf(closable));

            // 处理是否显示图标
            boolean showIcon = props.optBoolean("showIcon", false);
            templateBuilder.addProp(":show-icon", String.valueOf(showIcon));

            // 处理描述
            String description = props.optString("description", "");
            if (!description.isEmpty()) {
                templateBuilder.setContent(description);
            }
        }
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            String description = props.optString("description", "");
            if (!description.isEmpty()) {
                templateBuilder.setContent(description);
            }
        }
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.ALERT;
    }
}
