package com.web.lowcode.generator.component;

import com.web.lowcode.generator.component.constants.ComponentConstants.JsonProps;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 抽象组件处理器
 * 提供组件处理的基本实现
 */
public abstract class AbstractComponentHandler implements ComponentHandler {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractComponentHandler.class);

    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 获取组件属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            logger.debug("处理组件属性: {}", props.toString());

            // 使用工具类处理通用属性
            StringBuilder propBuilder = new StringBuilder();
            ComponentPropsUtil.handleCommonProps(props, propBuilder);

            // 如果是表单组件，处理表单属性
            String componentType = getComponentType();
            if (ComponentPropsUtil.isFormComponent(componentType)) {
                ComponentPropsUtil.handleFormProps(props, propBuilder);
            }

            // 将属性添加到模板构建器
            for (String key : props.keySet()) {
                String value = props.optString(key);
                templateBuilder.addProp(key, value);
            }
        }

        // 处理样式
        JSONObject styles = ComponentPropsUtil.getStyles(component);
        if (!styles.isEmpty()) {
            logger.debug("处理组件样式: {}", styles.toString());

            // 使用工具类处理样式属性
            StringBuilder styleBuilder = new StringBuilder();
            ComponentPropsUtil.handleStyleProps(styles, styleBuilder);

            // 将样式添加到模板构建器
            for (String key : styles.keySet()) {
                String value = styles.optString(key);
                templateBuilder.addStyle(key, value);
            }
        }
    }

    @Override
    public void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 获取组件事件
        JSONObject events = ComponentPropsUtil.getEvents(component);
        if (!events.isEmpty()) {
            logger.debug("处理组件事件: {}", events.toString());

            // 将事件添加到模板构建器
            for (String eventType : events.keySet()) {
                String handler = events.optString(eventType);
                templateBuilder.addEvent(eventType, handler);
            }
        } else if (component.has(JsonProps.EVENTS) && component.get(JsonProps.EVENTS) instanceof org.json.JSONArray) {
            org.json.JSONArray eventsArray = component.getJSONArray(JsonProps.EVENTS);
            logger.debug("处理组件事件数组: {}", eventsArray.toString());

            for (int i = 0; i < eventsArray.length(); i++) {
                JSONObject event = eventsArray.getJSONObject(i);
                String eventType = event.optString("type", "");
                if (!eventType.isEmpty()) {
                    String handlerName = "handle" + templateBuilder.getComponentType() +
                                         templateBuilder.capitalize(eventType) + i;
                    templateBuilder.addEvent(eventType, handlerName);

                    // 保存事件处理方法信息
                    JSONObject handler = new JSONObject();
                    handler.put("name", handlerName);
                    handler.put("eventType", eventType);
                    handler.put("action", event.optJSONObject("action"));

                    if (!component.has("eventHandlers")) {
                        component.put("eventHandlers", new org.json.JSONArray());
                    }
                    component.getJSONArray("eventHandlers").put(handler);
                }
            }
        }
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        if (component.has(JsonProps.CHILDREN) && !component.getJSONArray(JsonProps.CHILDREN).isEmpty()) {
            templateBuilder.setHasChildren(true);
        } else if (component.has(JsonProps.TEXT)) {
            String text = component.optString(JsonProps.TEXT, "");
            templateBuilder.setContent(text);
        }
    }
}
