package com.web.lowcode.generator.component;

import lombok.Getter;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 组件模板构建器
 * 用于构建组件模板
 */
@Getter
public class ComponentTemplateBuilder {
    private static final Logger logger = LoggerFactory.getLogger(ComponentTemplateBuilder.class);

    private final StringBuilder sb = new StringBuilder();
    private final String originalType;
    private final String vueComponentType;
    private final String id;
    private final Map<String, String> props = new HashMap<>();
    private final Map<String, String> events = new HashMap<>();
    private final Map<String, String> styles = new HashMap<>();
    private final Map<String, String> slots = new HashMap<>();
    private String content = null;
    private boolean hasChildren = false;
    private int indentLevel = 6; // 默认缩进级别

    /**
     * 构造函数
     * @param component 组件对象
     * @param vueComponentType Vue组件类型
     */
    public ComponentTemplateBuilder(JSONObject component, String vueComponentType) {
        this.originalType = component.optString("type", "div");
        this.vueComponentType = vueComponentType;
        this.id = component.optString("id", "");

        // 处理样式
        if (component.has("styles")) {
            JSONObject stylesObj = component.getJSONObject("styles");
            for (String key : stylesObj.keySet()) {
                styles.put(key, stylesObj.optString(key));
            }
        }

        // 处理类名
        String className = component.optString("class", "");
        if (!className.isEmpty()) {
            props.put("class", className);
        }
    }

    /**
     * 添加属性
     * @param key 属性名
     * @param value 属性值
     * @return 构建器实例
     */
    public ComponentTemplateBuilder addProp(String key, String value) {
        props.put(key, value);
        return this;
    }

    /**
     * 添加样式
     * @param key 样式名
     * @param value 样式值
     * @return 构建器实例
     */
    public ComponentTemplateBuilder addStyle(String key, String value) {
        styles.put(key, value);
        return this;
    }

    /**
     * 添加事件
     * @param eventType 事件类型
     * @param handler 事件处理器
     * @return 构建器实例
     */
    public ComponentTemplateBuilder addEvent(String eventType, String handler) {
        events.put(eventType, handler);
        return this;
    }

    /**
     * 添加插槽
     * @param slotName 插槽名称
     * @param slotContent 插槽内容
     * @return 构建器实例
     */
    public ComponentTemplateBuilder addSlot(String slotName, String slotContent) {
        slots.put(slotName, slotContent);
        return this;
    }

    /**
     * 设置内容
     * @param content 内容
     * @return 构建器实例
     */
    public ComponentTemplateBuilder setContent(String content) {
        this.content = content;
        return this;
    }

    /**
     * 设置是否有子组件
     * @param hasChildren 是否有子组件
     * @return 构建器实例
     */
    public ComponentTemplateBuilder setHasChildren(boolean hasChildren) {
        this.hasChildren = hasChildren;
        return this;
    }

    /**
     * 设置缩进级别
     * @param indentLevel 缩进级别
     * @return 构建器实例
     */
    public ComponentTemplateBuilder setIndentLevel(int indentLevel) {
        this.indentLevel = indentLevel;
        return this;
    }

    /**
     * 获取组件类型
     * @return 组件类型
     */
    public String getComponentType() {
        return originalType;
    }

    /**
     * 是否有子组件
     * @return 是否有子组件
     */
    public boolean hasChildren() {
        return hasChildren;
    }

    /**
     * 构建开始标签
     * @return 构建器实例
     */
    public ComponentTemplateBuilder buildStartTag() {
        // 添加缩进
        for (int i = 0; i < indentLevel; i++) {
            sb.append(" ");
        }

        // 添加标签开始
        sb.append("<").append(vueComponentType);

        // 添加ID
        if (!id.isEmpty()) {
            sb.append(" id=\"").append(id).append("\"");
        }

        // 添加属性
        for (Map.Entry<String, String> entry : props.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 根据属性类型决定是否使用v-bind
            if (isNumeric(value) || "true".equals(value) || "false".equals(value)) {
                sb.append(" :").append(key).append("=\"").append(value).append("\"");
            } else {
                sb.append(" ").append(key).append("=\"").append(value).append("\"");
            }
        }

        // 添加样式
        if (!styles.isEmpty()) {
            sb.append(" :style=\"{");
            boolean first = true;
            for (Map.Entry<String, String> entry : styles.entrySet()) {
                if (!first) {
                    sb.append(", ");
                }
                first = false;

                String key = toCamelCase(entry.getKey());
                String value = entry.getValue();
                sb.append(key).append(": '").append(value).append("'");
            }
            sb.append("}\"");
        }

        // 添加事件
        for (Map.Entry<String, String> entry : events.entrySet()) {
            String eventType = entry.getKey();
            String handler = entry.getValue();
            sb.append(" @").append(eventType).append("=\"").append(handler).append("\"");
        }

        return this;
    }

    /**
     * 构建组件模板
     * @return 组件模板
     */
    public String build() {
        return sb.toString();
    }

    /**
     * 将字符串首字母大写
     * @param str 输入字符串
     * @return 首字母大写的字符串
     */
    public String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 将字符串转换为驼峰命名
     * @param str 输入字符串
     * @return 驼峰命名的字符串
     */
    private String toCamelCase(String str) {
        StringBuilder sb = new StringBuilder();
        boolean nextUpper = false;

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '-' || c == '_') {
                nextUpper = true;
            } else if (nextUpper) {
                sb.append(Character.toUpperCase(c));
                nextUpper = false;
            } else {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * 判断字符串是否为数字
     * @param str 输入字符串
     * @return 是否为数字
     */
    private boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
