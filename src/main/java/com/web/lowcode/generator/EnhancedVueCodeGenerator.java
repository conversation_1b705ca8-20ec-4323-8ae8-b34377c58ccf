package com.web.lowcode.generator;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.util.StaticResourceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强版Vue代码生成器
 * 使用多种设计模式优化代码结构
 */
@Component
@Lazy
public class EnhancedVueCodeGenerator implements CodeGenerator {
    private static final Logger logger = LoggerFactory.getLogger(EnhancedVueCodeGenerator.class);

    private final PageCodeBuilder pageCodeBuilder;

    @Autowired
    public EnhancedVueCodeGenerator(PageCodeBuilder pageCodeBuilder) {
        this.pageCodeBuilder = pageCodeBuilder;
    }

    @Override
    public String generatePageCode(PageEntity page, List<PageEntity> pages) {
        logger.info("生成增强版Vue页面代码: {}", page.getName());
        return pageCodeBuilder.buildVuePage(page, pages);
    }

    @Override
    public Map<String, String> generateProjectFiles(ProjectEntity project, List<PageEntity> pages) {
        logger.info("生成增强版Vue项目文件，项目: {}", project.getName());

        Map<String, String> files = new HashMap<>();

        // 生成页面文件
        for (PageEntity page : pages) {
            String pagePath = "src/views/" + page.getName() + ".vue";
            String pageCode = generatePageCode(page, pages);
            files.put(pagePath, pageCode);
        }

        // 生成NotFound页面
        files.put("src/views/NotFound.vue", generateNotFoundPage());

        // 生成主入口文件
        files.put("src/main.js", generateMainFile());

        // 生成路由文件
        files.put("src/router/index.js", generateRouterFile(pages));

        // 生成App.vue文件
        files.put("src/App.vue", generateAppFile());

        // 生成package.json文件
        files.put("package.json", generatePackageJson(project));

        // 生成README.md文件
        files.put("README.md", generateReadme(project));

        // 生成index.html文件
        files.put("index.html", generateIndexHtml(project));

        // 生成vite.config.js文件
        files.put("vite.config.js", generateViteConfig());

        // 生成静态资源目录
        byte[] faviconBytes = generateFavicon();
        if (faviconBytes.length > 0) {
            files.put("public/favicon.ico", new String(faviconBytes));
        }

        // 生成环境配置文件
        files.put(".env", generateEnvFile());
        files.put(".env.development", generateEnvDevelopment());
        files.put(".env.production", generateEnvProduction());

        // 生成API服务文件
        files.put("src/api/index.js", generateApiService());

        // 生成工具文件
        files.put("src/utils/index.js", generateUtils());

        // 生成组件文件
        files.put("src/components/HelloWorld.vue", generateHelloWorldComponent());

        // 生成静态资源目录
        byte[] logoBytes = generateLogo();
        if (logoBytes.length > 0) {
            files.put("src/assets/logo.png", new String(logoBytes));
        }

        // 生成文档文件
        files.put("docs/README.md", generateDocs(project, pages));
        files.put("docs/DEVELOPMENT.md", generateDevelopmentDocs());
        files.put("docs/API.md", generateApiDocs());

        return files;
    }

    @Override
    public String generateMainFile() {
        return """
                import { createApp } from 'vue'
                import App from './App.vue'
                import router from './router'
                import ElementPlus from 'element-plus'
                import 'element-plus/dist/index.css'

                const app = createApp(App)
                app.use(router)
                app.use(ElementPlus)
                app.mount('#app')
                """;
    }

    @Override
    public String generateRouterFile(List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();

        sb.append("import { createRouter, createWebHistory } from 'vue-router'\n\n");

        // 导入页面组件
        for (PageEntity page : pages) {
            String safeComponentName = getSafeComponentName(page.getName());
            sb.append("import ").append(safeComponentName).append(" from '../views/")
              .append(page.getName()).append(".vue'\n");
        }

        sb.append("\nconst routes = [\n");

        // 生成路由配置
        for (PageEntity page : pages) {
            String safeComponentName = getSafeComponentName(page.getName());
            sb.append("  {\n");
            sb.append("    path: '").append(page.getPath()).append("',\n");
            sb.append("    name: '").append(page.getName()).append("',\n");
            sb.append("    component: ").append(safeComponentName).append(",\n");
            sb.append("    meta: { title: '").append(page.getTitle()).append("' }\n");
            sb.append("  },\n");
        }

        // 添加根路径路由
        if (!pages.isEmpty()) {
            PageEntity firstPage = pages.get(0);
            sb.append("  {\n");
            sb.append("    path: '/',\n");
            sb.append("    redirect: '").append(firstPage.getPath()).append("'\n");
            sb.append("  },\n");
        }

        // 添加404路由
        sb.append("  {\n");
        sb.append("    path: '/:pathMatch(.*)*',\n");
        sb.append("    name: 'NotFound',\n");
        sb.append("    component: () => import('../views/NotFound.vue')\n");
        sb.append("  }\n");

        sb.append("]\n\n");
        sb.append("const router = createRouter({\n");
        sb.append("  history: createWebHistory(),\n");
        sb.append("  routes\n");
        sb.append("})\n\n");
        sb.append("export default router\n");

        return sb.toString();
    }

    /**
     * 获取安全的组件名称（确保是合法的JavaScript变量名）
     * @param name 原始名称
     * @return 安全的组件名称
     */
    private String getSafeComponentName(String name) {
        // 如果名称以数字开头，添加前缀
        if (name.matches("^\\d.*")) {
            return "Page" + name;
        }
        // 替换其他不合法字符
        return name.replaceAll("[^a-zA-Z0-9_$]", "");
    }

    @Override
    public String generateAppFile() {
        return """
                <template>
                  <router-view />
                </template>

                <style>
                body {
                  margin: 0;
                  padding: 0;
                  font-family: Arial, sans-serif;
                }

                #app {
                  width: 100%;
                  height: 100%;
                }
                </style>
                """;
    }

    /**
     * 生成package.json文件
     */
    private String generatePackageJson(ProjectEntity project) {
        return """
                {
                  "name": "%s",
                  "version": "0.1.0",
                  "private": true,
                  "scripts": {
                    "dev": "vite",
                    "build": "vite build",
                    "preview": "vite preview"
                  },
                  "dependencies": {
                    "axios": "^1.4.0",
                    "element-plus": "^2.3.8",
                    "vue": "^3.3.4",
                    "vue-router": "^4.2.4"
                  },
                  "devDependencies": {
                    "@vitejs/plugin-vue": "^4.2.3",
                    "sass": "^1.64.1",
                    "vite": "^4.4.7"
                  }
                }
                """.formatted(project.getName().toLowerCase().replace(" ", "-"));
    }

    /**
     * 生成README.md文件
     */
    private String generateReadme(ProjectEntity project) {
        return """
                # %s

                %s

                ## 项目设置

                ```
                npm install
                ```

                ### 开发模式

                ```
                npm run dev
                ```

                ### 生产构建

                ```
                npm run build
                ```

                ### 预览生产构建

                ```
                npm run preview
                ```
                """.formatted(project.getName(), project.getDescription());
    }

    /**
     * 生成index.html文件
     */
    private String generateIndexHtml(ProjectEntity project) {
        return """
                <!DOCTYPE html>
                <html lang="en">
                  <head>
                    <meta charset="UTF-8" />
                    <link rel="icon" href="/favicon.ico" />
                    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                    <title>%s</title>
                  </head>
                  <body>
                    <div id="app"></div>
                    <script type="module" src="/src/main.js"></script>
                  </body>
                </html>
                """.formatted(project.getName());
    }

    /**
     * 生成vite.config.js文件
     */
    private String generateViteConfig() {
        return """
                import { fileURLToPath, URL } from 'node:url'
                import { defineConfig } from 'vite'
                import vue from '@vitejs/plugin-vue'

                export default defineConfig({
                  plugins: [
                    vue(),
                  ],
                  resolve: {
                    alias: {
                      '@': fileURLToPath(new URL('./src', import.meta.url))
                    }
                  },
                  server: {
                    port: 3000,
                    proxy: {
                      '/api': {
                        target: 'http://localhost:8080',
                        changeOrigin: true
                      }
                    }
                  }
                })
                """;
    }

    /**
     * 生成环境配置文件
     */
    private String generateEnvFile() {
        return """
                # 默认环境变量
                VITE_APP_TITLE=Low Code Platform
                VITE_APP_API_BASE_URL=/api

                # 开发服务器配置
                VITE_PORT=3000
                VITE_ENABLE_MOCK=false

                # API配置
                VITE_API_TIMEOUT=10000
                VITE_ENABLE_API_LOG=true
                """;
    }

    /**
     * 生成开发环境配置文件
     */
    private String generateEnvDevelopment() {
        return """
                # 开发环境配置
                NODE_ENV=development

                VITE_APP_API_BASE_URL=/api
                VITE_ENABLE_MOCK=true
                VITE_ENABLE_API_LOG=true
                """;
    }

    /**
     * 生成生产环境配置文件
     */
    private String generateEnvProduction() {
        return """
                # 生产环境配置
                NODE_ENV=production

                VITE_APP_API_BASE_URL=/api
                VITE_ENABLE_MOCK=false
                VITE_ENABLE_API_LOG=false
                """;
    }

    /**
     * 生成API服务文件
     */
    private String generateApiService() {
        return """
                import axios from 'axios'

                // 创建axios实例
                const request = axios.create({
                  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
                  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || 10000)
                })

                // 请求拦截器
                request.interceptors.request.use(
                  config => {
                    // 可以在这里添加认证信息等
                    return config
                  },
                  error => {
                    return Promise.reject(error)
                  }
                )

                // 响应拦截器
                request.interceptors.response.use(
                  response => {
                    return response.data
                  },
                  error => {
                    return Promise.reject(error)
                  }
                )

                export default request
                """;
    }

    /**
     * 生成工具文件
     */
    private String generateUtils() {
        return """
                /**
                 * 格式化日期
                 * @param {Date} date 日期对象
                 * @param {string} format 格式字符串
                 * @returns {string} 格式化后的日期字符串
                 */
                export function formatDate(date, format = 'YYYY-MM-DD') {
                  if (!date) return ''

                  const year = date.getFullYear()
                  const month = date.getMonth() + 1
                  const day = date.getDate()
                  const hours = date.getHours()
                  const minutes = date.getMinutes()
                  const seconds = date.getSeconds()

                  const pad = (n) => n.toString().padStart(2, '0')

                  return format
                    .replace('YYYY', year)
                    .replace('MM', pad(month))
                    .replace('DD', pad(day))
                    .replace('HH', pad(hours))
                    .replace('mm', pad(minutes))
                    .replace('ss', pad(seconds))
                }

                /**
                 * 深拷贝对象
                 * @param {Object} obj 要拷贝的对象
                 * @returns {Object} 拷贝后的对象
                 */
                export function deepClone(obj) {
                  if (obj === null || typeof obj !== 'object') {
                    return obj
                  }

                  if (obj instanceof Date) {
                    return new Date(obj.getTime())
                  }

                  if (obj instanceof Array) {
                    return obj.map(item => deepClone(item))
                  }

                  if (obj instanceof Object) {
                    const copy = {}
                    Object.keys(obj).forEach(key => {
                      copy[key] = deepClone(obj[key])
                    })
                    return copy
                  }

                  return obj
                }
                """;
    }

    /**
     * 生成HelloWorld组件
     */
    private String generateHelloWorldComponent() {
        return """
                <template>
                  <div class="hello-world">
                    <h1>{{ msg }}</h1>
                    <p>
                      这是一个示例组件，用于展示如何创建和使用Vue组件。
                    </p>
                    <button @click="count++">点击次数: {{ count }}</button>
                  </div>
                </template>

                <script>
                export default {
                  name: 'HelloWorld',
                  props: {
                    msg: {
                      type: String,
                      default: 'Hello World'
                    }
                  },
                  data() {
                    return {
                      count: 0
                    }
                  }
                }
                </script>

                <style scoped>
                .hello-world {
                  padding: 20px;
                  border: 1px solid #eee;
                  border-radius: 4px;
                  margin: 20px 0;
                }

                h1 {
                  font-size: 24px;
                  margin-bottom: 16px;
                }

                button {
                  padding: 8px 16px;
                  background-color: #4CAF50;
                  color: white;
                  border: none;
                  border-radius: 4px;
                  cursor: pointer;
                }

                button:hover {
                  background-color: #45a049;
                }
                </style>
                """;
    }

    /**
     * 生成文档
     */
    private String generateDocs(ProjectEntity project, List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();

        sb.append("# ").append(project.getName()).append(" 文档\n\n");
        sb.append(project.getDescription()).append("\n\n");

        sb.append("## 页面列表\n\n");
        for (PageEntity page : pages) {
            sb.append("- ").append(page.getTitle()).append(" (").append(page.getName()).append(")\n");
        }

        sb.append("\n## 开发指南\n\n");
        sb.append("请参考 [DEVELOPMENT.md](./DEVELOPMENT.md) 文件了解开发指南。\n\n");

        sb.append("## API文档\n\n");
        sb.append("请参考 [API.md](./API.md) 文件了解API文档。\n");

        return sb.toString();
    }

    /**
     * 生成开发文档
     */
    private String generateDevelopmentDocs() {
        return """
                # 开发指南

                ## 项目结构

                ```
                ├── public/          # 静态资源目录
                ├── src/             # 源代码目录
                │   ├── api/         # API请求
                │   ├── assets/      # 资源文件
                │   ├── components/  # 组件
                │   ├── router/      # 路由配置
                │   ├── utils/       # 工具函数
                │   ├── views/       # 页面
                │   ├── App.vue      # 根组件
                │   └── main.js      # 入口文件
                ├── .env             # 环境变量
                ├── .env.development # 开发环境变量
                ├── .env.production  # 生产环境变量
                ├── index.html       # HTML模板
                ├── package.json     # 项目配置
                ├── README.md        # 项目说明
                └── vite.config.js   # Vite配置
                ```

                ## 开发环境

                ### 安装依赖

                ```bash
                npm install
                ```

                ### 启动开发服务器

                ```bash
                npm run dev
                ```

                ### 构建生产版本

                ```bash
                npm run build
                ```

                ### 预览生产版本

                ```bash
                npm run preview
                ```

                ## 开发规范

                ### 命名规范

                - 文件名：使用kebab-case（短横线命名法），如`hello-world.vue`
                - 组件名：使用PascalCase（大驼峰命名法），如`HelloWorld`
                - 变量名：使用camelCase（小驼峰命名法），如`userName`
                - 常量名：使用UPPER_SNAKE_CASE（大写下划线命名法），如`MAX_COUNT`

                ### 代码风格

                - 使用2个空格缩进
                - 使用单引号代替双引号
                - 每行代码不超过100个字符
                - 在适当的地方添加注释

                ### 组件开发

                - 组件应该是可复用的，每个组件应该只做一件事
                - 组件应该有清晰的接口，使用props和events进行通信
                - 组件应该有适当的文档，说明组件的用途和使用方法

                ### 路由配置

                路由配置在`src/router/index.js`文件中，每个路由应该包含以下信息：

                - path：路由路径
                - name：路由名称
                - component：路由组件
                - meta：路由元信息，如标题、权限等

                ### API请求

                API请求在`src/api`目录中，每个API应该包含以下信息：

                - 请求方法：GET、POST、PUT、DELETE等
                - 请求路径：API的URL
                - 请求参数：API的参数
                - 响应数据：API的响应数据

                ## 部署

                ### 部署到生产环境

                1. 构建生产版本

                ```bash
                npm run build
                ```

                2. 将`dist`目录下的文件部署到Web服务器

                ### 部署到测试环境

                1. 构建测试版本

                ```bash
                npm run build:test
                ```

                2. 将`dist`目录下的文件部署到测试服务器
                """;
    }

    /**
     * 生成favicon.ico文件
     * 从后端static目录读取
     */
    private byte[] generateFavicon() {
        // 从后端static目录读取favicon.ico文件
        byte[] faviconBytes = StaticResourceUtil.readFileAsBytes("favicon.ico");
        return faviconBytes;
    }

    /**
     * 生成Logo图片
     * 从后端static目录读取
     */
    private byte[] generateLogo() {
        // 从后端static目录读取logo.png文件
        byte[] logoBytes = StaticResourceUtil.readFileAsBytes("logo.png");
        return logoBytes;
    }

    /**
     * 生成API文档
     */
    private String generateApiDocs() {
        return """
                # API文档

                ## 基础API

                ### 获取项目列表

                ```
                GET /api/projects
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | page | number | 否 | 页码，默认为1 |
                | size | number | 否 | 每页数量，默认为10 |
                | keyword | string | 否 | 关键字搜索 |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "total": 100,
                    "list": [
                      {
                        "id": 1,
                        "name": "项目1",
                        "description": "项目1描述",
                        "createTime": "2023-01-01 00:00:00",
                        "updateTime": "2023-01-01 00:00:00"
                      }
                    ]
                  }
                }
                ```

                ### 获取项目详情

                ```
                GET /api/projects/{id}
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | id | number | 是 | 项目ID |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "id": 1,
                    "name": "项目1",
                    "description": "项目1描述",
                    "createTime": "2023-01-01 00:00:00",
                    "updateTime": "2023-01-01 00:00:00"
                  }
                }
                ```

                ### 创建项目

                ```
                POST /api/projects
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | name | string | 是 | 项目名称 |
                | description | string | 否 | 项目描述 |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "id": 1,
                    "name": "项目1",
                    "description": "项目1描述",
                    "createTime": "2023-01-01 00:00:00",
                    "updateTime": "2023-01-01 00:00:00"
                  }
                }
                ```

                ### 更新项目

                ```
                PUT /api/projects/{id}
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | id | number | 是 | 项目ID |
                | name | string | 否 | 项目名称 |
                | description | string | 否 | 项目描述 |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "id": 1,
                    "name": "项目1",
                    "description": "项目1描述",
                    "createTime": "2023-01-01 00:00:00",
                    "updateTime": "2023-01-01 00:00:00"
                  }
                }
                ```

                ### 删除项目

                ```
                DELETE /api/projects/{id}
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | id | number | 是 | 项目ID |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": null
                }
                ```

                ## 页面API

                ### 获取页面列表

                ```
                GET /api/projects/{projectId}/pages
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | projectId | number | 是 | 项目ID |
                | page | number | 否 | 页码，默认为1 |
                | size | number | 否 | 每页数量，默认为10 |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "total": 100,
                    "list": [
                      {
                        "id": 1,
                        "projectId": 1,
                        "name": "页面1",
                        "title": "页面1标题",
                        "path": "/page1",
                        "createTime": "2023-01-01 00:00:00",
                        "updateTime": "2023-01-01 00:00:00"
                      }
                    ]
                  }
                }
                ```

                ### 获取页面详情

                ```
                GET /api/pages/{id}
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | id | number | 是 | 页面ID |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "id": 1,
                    "projectId": 1,
                    "name": "页面1",
                    "title": "页面1标题",
                    "path": "/page1",
                    "createTime": "2023-01-01 00:00:00",
                    "updateTime": "2023-01-01 00:00:00"
                  }
                }
                ```

                ### 创建页面

                ```
                POST /api/projects/{projectId}/pages
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | projectId | number | 是 | 项目ID |
                | name | string | 是 | 页面名称 |
                | title | string | 是 | 页面标题 |
                | path | string | 是 | 页面路径 |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "id": 1,
                    "projectId": 1,
                    "name": "页面1",
                    "title": "页面1标题",
                    "path": "/page1",
                    "createTime": "2023-01-01 00:00:00",
                    "updateTime": "2023-01-01 00:00:00"
                  }
                }
                ```

                ### 更新页面

                ```
                PUT /api/pages/{id}
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | id | number | 是 | 页面ID |
                | name | string | 否 | 页面名称 |
                | title | string | 否 | 页面标题 |
                | path | string | 否 | 页面路径 |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": {
                    "id": 1,
                    "projectId": 1,
                    "name": "页面1",
                    "title": "页面1标题",
                    "path": "/page1",
                    "createTime": "2023-01-01 00:00:00",
                    "updateTime": "2023-01-01 00:00:00"
                  }
                }
                ```

                ### 删除页面

                ```
                DELETE /api/pages/{id}
                ```

                #### 请求参数

                | 参数名 | 类型 | 必填 | 描述 |
                | --- | --- | --- | --- |
                | id | number | 是 | 页面ID |

                #### 响应数据

                ```json
                {
                  "code": 0,
                  "message": "success",
                  "data": null
                }
                ```
                """;
    }

    /**
     * 生成NotFound页面
     * @return NotFound页面代码
     */
    private String generateNotFoundPage() {
        return """
                <template>
                  <div class="not-found">
                    <h1>404</h1>
                    <h2>页面未找到</h2>
                    <p>抱歉，您访问的页面不存在。</p>
                    <router-link to="/">返回首页</router-link>
                  </div>
                </template>

                <script>
                export default {
                  name: 'NotFound'
                }
                </script>

                <style scoped>
                .not-found {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100vh;
                  text-align: center;
                  padding: 20px;
                }

                h1 {
                  font-size: 72px;
                  margin-bottom: 0;
                  color: #e74c3c;
                }

                h2 {
                  margin-top: 0;
                  margin-bottom: 20px;
                  color: #333;
                }

                p {
                  margin-bottom: 30px;
                  color: #666;
                }

                a {
                  color: #3498db;
                  text-decoration: none;
                  padding: 10px 20px;
                  border: 1px solid #3498db;
                  border-radius: 4px;
                  transition: all 0.3s ease;
                }

                a:hover {
                  background-color: #3498db;
                  color: white;
                }
                </style>
                """;
    }
}
