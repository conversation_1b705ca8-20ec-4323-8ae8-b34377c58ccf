package com.web.lowcode.generator;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * UniApp框架的代码生成器实现
 */
@Slf4j
@Component
public class UniAppCodeGenerator implements CodeGenerator {

    private final PageCodeBuilder pageCodeBuilder;

    @Autowired
    public UniAppCodeGenerator(PageCodeBuilder pageCodeBuilder) {
        this.pageCodeBuilder = pageCodeBuilder;
    }

    @Override
    public String generatePageCode(PageEntity page, List<PageEntity> pages) {
        log.info("生成UniApp页面代码: {}", page.getName());
        return pageCodeBuilder.buildUniAppPage(page, pages);
    }

    @Override
    public Map<String, String> generateProjectFiles(ProjectEntity project, List<PageEntity> pages) {
        log.info("生成UniApp项目文件，项目: {}", project.getName());

        Map<String, String> files = new HashMap<>();

        // 生成页面文件
        for (PageEntity page : pages) {
            String pagePath = "src/pages/" + page.getName() + "/" + page.getName() + ".vue";
            String pageCode = generatePageCode(page, pages);
            files.put(pagePath, pageCode);
        }

        // 生成主入口文件
        files.put("src/main.js", generateMainFile());

        // 生成App.vue文件
        files.put("src/App.vue", generateAppFile());

        // 生成pages.json文件（UniApp的路由配置）
        files.put("src/pages.json", generatePagesJson(pages));

        // 生成manifest.json文件
        files.put("src/manifest.json", generateManifestJson(project));

        // 生成uni.scss文件
        files.put("src/uni.scss", generateUniScss());

        // 生成package.json文件
        files.put("package.json", generatePackageJson(project));

        // 生成README.md文件
        files.put("README.md", generateReadme(project));

        // 生成index.html文件
        files.put("index.html", generateIndexHtml(project));

        // 生成文档
        files.put("docs/README.md", generateDocs(project, pages));
        files.put("docs/DEVELOPMENT.md", generateUniAppDevelopmentDocs(project));

        return files;
    }

    @Override
    public String generateMainFile() {
        return """
                import { createSSRApp } from 'vue'
                import App from './App.vue'

                export function createApp() {
                  const app = createSSRApp(App)
                  return { app }
                }
                """;
    }

    @Override
    public String generateRouterFile(List<PageEntity> pages) {
        // UniApp使用pages.json进行路由配置，此方法可以为空
        return "";
    }

    @Override
    public String generateAppFile() {
        return """
                <script>
                export default {
                  onLaunch: function() {
                    console.log('App Launch')
                  },
                  onShow: function() {
                    console.log('App Show')
                  },
                  onHide: function() {
                    console.log('App Hide')
                  }
                }
                </script>

                <style>
                /* 全局样式 */
                page {
                  min-height: 100%;
                  display: flex;
                  font-size: 14px;
                }

                body {
                  background-color: #f8f8f8;
                  font-family: Arial, sans-serif;
                  margin: 0;
                  padding: 0;
                }
                </style>
                """;
    }

    /**
     * 生成pages.json文件（UniApp的路由配置）
     */
    private String generatePagesJson(List<PageEntity> pages) {
        StringBuilder pagesArray = new StringBuilder();

        // 生成页面配置
        for (int i = 0; i < pages.size(); i++) {
            PageEntity page = pages.get(i);
            pagesArray.append("    {\n");
            pagesArray.append("      \"path\": \"pages/").append(page.getName()).append("/").append(page.getName()).append("\",\n");
            pagesArray.append("      \"style\": {\n");
            pagesArray.append("        \"navigationBarTitleText\": \"").append(page.getTitle()).append("\"\n");
            pagesArray.append("      }\n");
            pagesArray.append("    }");

            if (i < pages.size() - 1) {
                pagesArray.append(",\n");
            } else {
                pagesArray.append("\n");
            }
        }

        return """
                {
                  "pages": [
                %s
                  ],
                  "globalStyle": {
                    "navigationBarTextStyle": "black",
                    "navigationBarTitleText": "UniApp",
                    "navigationBarBackgroundColor": "#F8F8F8",
                    "backgroundColor": "#F8F8F8"
                  },
                  "easycom": {
                    "autoscan": true,
                    "custom": {
                      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
                    }
                  }
                }
                """.formatted(pagesArray.toString());
    }

    /**
     * 生成manifest.json文件
     */
    private String generateManifestJson(ProjectEntity project) {
        return """
                {
                  "name": "%s",
                  "appid": "",
                  "description": "%s",
                  "versionName": "1.0.0",
                  "versionCode": "100",
                  "transformPx": false,
                  "app-plus": {
                    "usingComponents": true,
                    "nvueStyleCompiler": "uni-app",
                    "compilerVersion": 3,
                    "splashscreen": {
                      "alwaysShowBeforeRender": true,
                      "waiting": true,
                      "autoclose": true,
                      "delay": 0
                    },
                    "modules": {},
                    "distribute": {
                      "android": {
                        "permissions": [
                          "<uses-permission android:name=\\"android.permission.CHANGE_NETWORK_STATE\\"/>",
                          "<uses-permission android:name=\\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\\"/>",
                          "<uses-permission android:name=\\"android.permission.VIBRATE\\"/>",
                          "<uses-permission android:name=\\"android.permission.READ_LOGS\\"/>",
                          "<uses-permission android:name=\\"android.permission.ACCESS_WIFI_STATE\\"/>",
                          "<uses-permission android:name=\\"android.permission.WRITE_SETTINGS\\"/>",
                          "<uses-permission android:name=\\"android.permission.INTERNET\\"/>"
                        ]
                      },
                      "ios": {},
                      "sdkConfigs": {}
                    }
                  },
                  "quickapp": {},
                  "mp-weixin": {
                    "appid": "",
                    "setting": {
                      "urlCheck": false
                    },
                    "usingComponents": true
                  },
                  "mp-alipay": {
                    "usingComponents": true
                  },
                  "mp-baidu": {
                    "usingComponents": true
                  },
                  "mp-toutiao": {
                    "usingComponents": true
                  },
                  "uniStatistics": {
                    "enable": false
                  },
                  "vueVersion": "3"
                }
                """.formatted(project.getName(), project.getDescription());
    }

    /**
     * 生成uni.scss文件
     */
    private String generateUniScss() {
        return """
                /* 全局样式变量 */

                /* 颜色变量 */
                $uni-primary: #2979ff;
                $uni-success: #4cd964;
                $uni-warning: #f0ad4e;
                $uni-error: #dd524d;
                $uni-info: #909399;

                /* 文字基本颜色 */
                $uni-text-color: #333; // 基本色
                $uni-text-color-inverse: #fff; // 反色
                $uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
                $uni-text-color-placeholder: #808080;
                $uni-text-color-disable: #c0c0c0;

                /* 背景颜色 */
                $uni-bg-color: #ffffff;
                $uni-bg-color-grey: #f8f8f8;
                $uni-bg-color-hover: #f1f1f1; // 点击状态颜色
                $uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

                /* 边框颜色 */
                $uni-border-color: #c8c7cc;

                /* 尺寸变量 */
                $uni-font-size-sm: 12px;
                $uni-font-size-base: 14px;
                $uni-font-size-lg: 16px;

                /* 图片尺寸 */
                $uni-img-size-sm: 20px;
                $uni-img-size-base: 26px;
                $uni-img-size-lg: 40px;

                /* Border Radius */
                $uni-border-radius-sm: 2px;
                $uni-border-radius-base: 3px;
                $uni-border-radius-lg: 6px;
                $uni-border-radius-circle: 50%;

                /* 水平间距 */
                $uni-spacing-row-sm: 5px;
                $uni-spacing-row-base: 10px;
                $uni-spacing-row-lg: 15px;

                /* 垂直间距 */
                $uni-spacing-col-sm: 4px;
                $uni-spacing-col-base: 8px;
                $uni-spacing-col-lg: 12px;

                /* 透明度 */
                $uni-opacity-disabled: 0.3; // 组件禁用态的透明度

                /* 文章场景相关 */
                $uni-color-title: #2C405A; // 文章标题颜色
                $uni-font-size-title: 20px;
                $uni-color-subtitle: #555555; // 二级标题颜色
                $uni-font-size-subtitle: 18px;
                $uni-color-paragraph: #3F536E; // 文章段落颜色
                $uni-font-size-paragraph: 15px;
                """;
    }

    /**
     * 生成package.json文件
     */
    private String generatePackageJson(ProjectEntity project) {
        return """
                {
                  "name": "%s",
                  "version": "1.0.0",
                  "description": "%s",
                  "main": "main.js",
                  "scripts": {
                    "dev:app": "uni -p app",
                    "dev:h5": "uni",
                    "dev:mp-weixin": "uni -p mp-weixin",
                    "dev:mp-alipay": "uni -p mp-alipay",
                    "build:app": "uni build -p app",
                    "build:h5": "uni build",
                    "build:mp-weixin": "uni build -p mp-weixin",
                    "build:mp-alipay": "uni build -p mp-alipay"
                  },
                  "dependencies": {
                    "@dcloudio/uni-app": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-app-plus": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-h5": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-mp-alipay": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-mp-baidu": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-mp-toutiao": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-mp-weixin": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-ui": "^1.4.20",
                    "vue": "^3.2.45"
                  },
                  "devDependencies": {
                    "@dcloudio/types": "^3.0.7",
                    "@dcloudio/uni-automator": "^2.0.2-3061818230222002",
                    "@dcloudio/uni-cli-shared": "^2.0.2-3061818230222002",
                    "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-3061818230222002",
                    "sass": "^1.56.1",
                    "vite": "^4.0.0"
                  }
                }
                """.formatted(project.getName().toLowerCase().replace(" ", "-"), project.getDescription());
    }

    /**
     * 生成README.md文件
     */
    private String generateReadme(ProjectEntity project) {
        return """
                # %s

                %s

                ## 项目设置

                ```
                npm install
                ```

                ### 开发模式

                ```
                # H5
                npm run dev:h5

                # 微信小程序
                npm run dev:mp-weixin

                # App
                npm run dev:app
                ```

                ### 生产构建

                ```
                # H5
                npm run build:h5

                # 微信小程序
                npm run build:mp-weixin

                # App
                npm run build:app
                ```

                ## 项目结构

                ```
                src
                ├── pages            // 页面文件夹
                ├── static           // 静态资源
                ├── App.vue          // 应用配置，用来配置App全局样式以及监听应用生命周期
                ├── main.js          // Vue初始化入口文件
                ├── manifest.json    // 配置应用名称、appid、logo、版本等打包信息
                ├── pages.json       // 配置页面路由、导航条、选项卡等页面类信息
                └── uni.scss         // 全局样式变量
                ```
                """.formatted(project.getName(), project.getDescription());
    }

    /**
     * 生成index.html文件
     */
    private String generateIndexHtml(ProjectEntity project) {
        return """
                <!DOCTYPE html>
                <html lang="zh-CN">
                  <head>
                    <meta charset="UTF-8" />
                    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
                    <title>%s</title>
                    <!--preload-links-->
                    <!--app-context-->
                  </head>
                  <body>
                    <div id="app"><!--app-html--></div>
                    <script type="module" src="/src/main.js"></script>
                  </body>
                </html>
                """.formatted(project.getName());
    }

    /**
     * 生成文档
     */
    private String generateDocs(ProjectEntity project, List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();

        sb.append("# ").append(project.getName()).append(" 文档\n\n");
        sb.append(project.getDescription()).append("\n\n");

        sb.append("## 页面列表\n\n");
        for (PageEntity page : pages) {
            sb.append("- ").append(page.getTitle()).append(" (").append(page.getName()).append(")\n");
        }

        sb.append("\n## 开发指南\n\n");
        sb.append("请参考 [DEVELOPMENT.md](./DEVELOPMENT.md) 文件了解开发指南。\n");

        return sb.toString();
    }

    /**
     * 生成UniApp开发文档
     */
    private String generateUniAppDevelopmentDocs(ProjectEntity project) {
        return """
                # UniApp 开发指南

                ## 简介

                UniApp 是一个使用 Vue.js 开发所有前端应用的框架，开发者编写一套代码，可发布到iOS、Android、Web（响应式）、以及各种小程序（微信/支付宝/百度/头条/QQ/钉钉/淘宝）、快应用等多个平台。

                ## 开发环境

                推荐使用 HBuilderX 作为开发工具，它内置了 uni-app 的编译器和运行时，不需要额外配置。

                也可以使用 VS Code 或其他编辑器，但需要使用命令行工具进行编译和运行。

                ## 项目结构

                ```
                src
                ├── pages            // 页面文件夹
                ├── static           // 静态资源
                ├── App.vue          // 应用配置，用来配置App全局样式以及监听应用生命周期
                ├── main.js          // Vue初始化入口文件
                ├── manifest.json    // 配置应用名称、appid、logo、版本等打包信息
                ├── pages.json       // 配置页面路由、导航条、选项卡等页面类信息
                └── uni.scss         // 全局样式变量
                ```

                ## 页面开发

                UniApp 页面采用 Vue 单文件组件 (SFC) 规范，每个页面是一个 .vue 文件，由 template、script、style 三部分组成。

                ### 页面路由

                UniApp 的页面路由需要在 pages.json 中进行配置：

                ```json
                {
                  "pages": [
                    {
                      "path": "pages/index/index",
                      "style": {
                        "navigationBarTitleText": "首页"
                      }
                    }
                  ]
                }
                ```

                ### 页面跳转

                ```javascript
                // 保留当前页面，跳转到应用内的某个页面
                uni.navigateTo({
                  url: '/pages/detail/detail?id=1'
                })

                // 关闭当前页面，跳转到应用内的某个页面
                uni.redirectTo({
                  url: '/pages/detail/detail?id=1'
                })
                ```

                ## 组件使用

                UniApp 提供了丰富的内置组件，同时也支持使用第三方 Vue 组件。

                ### 内置组件

                ```html
                <template>
                  <view class="container">
                    <text>这是一个文本</text>
                    <button @click="handleClick">按钮</button>
                    <image src="/static/logo.png"></image>
                  </view>
                </template>
                ```

                ### 自定义组件

                ```html
                <!-- 自定义组件 MyComponent.vue -->
                <template>
                  <view class="my-component">
                    <text>{{ title }}</text>
                  </view>
                </template>

                <script>
                export default {
                  props: {
                    title: {
                      type: String,
                      default: ''
                    }
                  }
                }
                </script>

                <!-- 使用自定义组件 -->
                <template>
                  <view>
                    <my-component title="自定义组件"></my-component>
                  </view>
                </template>

                <script>
                import MyComponent from '@/components/MyComponent.vue'

                export default {
                  components: {
                    MyComponent
                  }
                }
                </script>
                ```

                ## API 使用

                UniApp 提供了丰富的 API，可以调用设备功能、网络请求等。

                ### 网络请求

                ```javascript
                uni.request({
                  url: 'https://api.example.com/data',
                  method: 'GET',
                  data: {
                    id: 1
                  },
                  success: (res) => {
                    console.log(res.data)
                  },
                  fail: (err) => {
                    console.error(err)
                  }
                })
                ```

                ### 数据缓存

                ```javascript
                // 存储数据
                uni.setStorage({
                  key: 'userInfo',
                  data: {
                    name: '张三',
                    age: 18
                  }
                })

                // 获取数据
                uni.getStorage({
                  key: 'userInfo',
                  success: (res) => {
                    console.log(res.data)
                  }
                })
                ```

                ## 条件编译

                UniApp 支持条件编译，可以针对不同平台编写特定代码。

                ```html
                <!-- #ifdef H5 -->
                <view>只在H5平台显示</view>
                <!-- #endif -->

                <!-- #ifdef MP-WEIXIN -->
                <view>只在微信小程序显示</view>
                <!-- #endif -->
                ```

                ```javascript
                // #ifdef H5
                console.log('只在H5平台执行')
                // #endif

                // #ifdef MP-WEIXIN
                console.log('只在微信小程序执行')
                // #endif
                ```

                ## 更多资源

                - [UniApp 官方文档](https://uniapp.dcloud.io/)
                - [UniApp 组件库](https://uniapp.dcloud.io/component/)
                - [UniApp API 参考](https://uniapp.dcloud.io/api/)
                - [UniApp 插件市场](https://ext.dcloud.net.cn/)
                """;
    }
}
