package com.web.lowcode.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果
 */
@Data
public class PageResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页数据
     */
    private List<T> records;
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 每页记录数
     */
    private Long size;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 构造方法
     */
    public PageResult() {
    }
    
    /**
     * 构造方法
     *
     * @param total   总记录数
     * @param records 当前页数据
     */
    public PageResult(Long total, List<T> records) {
        this.total = total;
        this.records = records;
    }
    
    /**
     * 构造方法
     *
     * @param total   总记录数
     * @param records 当前页数据
     * @param current 当前页码
     * @param size    每页记录数
     */
    public PageResult(Long total, List<T> records, Long current, Long size) {
        this.total = total;
        this.records = records;
        this.current = current;
        this.size = size;
        this.pages = (total + size - 1) / size;
    }
}
