package com.web.lowcode.security;

import com.web.lowcode.entity.UserEntity;
import com.web.lowcode.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 安全工具类
 * 用于获取当前登录用户信息
 */
@Component
public class SecurityUtils {

    private static UserService userService;

    @Autowired
    public void setUserService(UserService userService) {
        SecurityUtils.userService = userService;
    }

    /**
     * 获取当前登录用户名
     *
     * @return 当前登录用户名
     */
    public static Optional<String> getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null) {
            return Optional.empty();
        }
        
        if (authentication.getPrincipal() instanceof UserDetails) {
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            return Optional.of(userDetails.getUsername());
        }
        
        if (authentication.getPrincipal() instanceof String) {
            return Optional.of((String) authentication.getPrincipal());
        }
        
        return Optional.empty();
    }
    
    /**
     * 获取当前用户认证信息
     *
     * @return 认证信息
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }
    
    /**
     * 判断当前用户是否已认证
     *
     * @return 是否已认证
     */
    public static boolean isAuthenticated() {
        Authentication authentication = getAuthentication();
        return authentication != null && authentication.isAuthenticated();
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 当前登录用户ID
     */
    public static Long getCurrentUserId() {
        try {
            Optional<String> username = getCurrentUsername();
            if (username.isPresent() && userService != null) {
                UserEntity user = userService.getByUsername(username.get());
                return user != null ? user.getId() : 1L; // 默认返回系统用户ID
            }
        } catch (Exception e) {
            // 忽略异常，返回默认值
        }
        return 1L; // 默认系统用户ID
    }

    /**
     * 获取当前登录用户名（非Optional版本）
     *
     * @return 当前登录用户名
     */
    public static String getCurrentUserName() {
        try {
            Optional<String> username = getCurrentUsername();
            return username.orElse("system"); // 默认返回系统用户
        } catch (Exception e) {
            return "system"; // 默认系统用户
        }
    }

    /**
     * 获取当前登录用户实体
     *
     * @return 当前登录用户实体
     */
    public static UserEntity getCurrentUser() {
        try {
            Optional<String> username = getCurrentUsername();
            if (username.isPresent() && userService != null) {
                return userService.getByUsername(username.get());
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }
}
