package com.web.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 图片操作日志实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@TableName("image_log")
public class ImageLogEntity {
    
    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 图片ID
     */
    private Long imageId;
    
    /**
     * 操作类型（CREATE-创建，UPDATE-更新，DELETE-删除，VIEW-查看，DOWNLOAD-下载）
     */
    private String operationType;
    
    /**
     * 操作描述
     */
    private String operationDesc;
    
    /**
     * 操作前数据（JSON格式）
     */
    private String beforeData;
    
    /**
     * 操作后数据（JSON格式）
     */
    private String afterData;
    
    /**
     * 操作者ID
     */
    private Long operatorId;
    
    /**
     * 操作者姓名
     */
    private String operatorName;
    
    /**
     * 操作IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    /**
     * 操作结果（SUCCESS-成功，FAILED-失败）
     */
    private String result;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 操作耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
