package com.web.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据源实体类
 */
@Data
@TableName("data_source")
public class DataSourceEntity {
    
    /**
     * 数据源ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 所属项目ID
     */
    private Long projectId;
    
    /**
     * 数据源名称
     */
    private String name;
    
    /**
     * 数据源类型（static-静态数据，api-接口数据）
     */
    private String type;
    
    /**
     * 数据源配置（JSON格式）
     */
    private String config;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
