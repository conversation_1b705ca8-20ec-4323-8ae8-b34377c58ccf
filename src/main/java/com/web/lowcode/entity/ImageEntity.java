package com.web.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 图片管理实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@TableName("image")
public class ImageEntity {
    
    /**
     * 图片ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 图片名称
     */
    private String name;
    
    /**
     * 原始文件名
     */
    private String originalName;
    
    /**
     * 文件扩展名
     */
    private String extension;
    
    /**
     * 文件大小（字节）
     */
    private Long size;
    
    /**
     * 文件MIME类型
     */
    private String contentType;
    
    /**
     * MinIO存储路径
     */
    private String minioPath;
    
    /**
     * MinIO存储桶名称
     */
    private String bucketName;
    
    /**
     * 图片访问URL
     */
    private String url;
    
    /**
     * 图片宽度（像素）
     */
    private Integer width;
    
    /**
     * 图片高度（像素）
     */
    private Integer height;
    
    /**
     * 图片MD5值
     */
    private String md5;
    
    /**
     * 图片分类（1-组件图标，2-用户上传，3-系统图片，4-模板缩略图）
     */
    private Integer category;
    
    /**
     * 关联的业务ID（如组件ID、项目ID等）
     */
    private Long businessId;
    
    /**
     * 业务类型（component-组件，project-项目，template-模板，user-用户）
     */
    private String businessType;
    
    /**
     * 图片状态（0-正常，1-已删除）
     */
    private Integer status;
    
    /**
     * 上传者ID
     */
    private Long uploaderId;
    
    /**
     * 上传者姓名
     */
    private String uploaderName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;
    
    /**
     * 备注信息
     */
    private String remark;
}
