package com.web.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 组件实体类
 */
@Data
@TableName("component")
public class ComponentEntity {

    /**
     * 组件ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 组件类型
     */
    private String type;

    /**
     * 组件分类
     */
    private String category;

    /**
     * 组件图标
     */
    private String icon;

    /**
     * 组件属性配置（JSON格式）
     */
    private String props;

    /**
     * 组件事件配置（JSON格式）
     */
    private String events;

    /**
     * 组件样式配置（JSON格式）
     */
    private String styles;

    /**
     * 组件模板代码
     */
    private String template;

    /**
     * 是否系统内置（0-否，1-是）
     */
    private Integer isSystem;

    /**
     * 是否启用（0-禁用，1-启用）
     */
    private Integer enabled;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
