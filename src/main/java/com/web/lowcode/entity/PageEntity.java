package com.web.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 页面实体类
 */
@Data
@TableName("page")
public class PageEntity {
    
    /**
     * 页面ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 所属项目ID
     */
    private Long projectId;
    
    /**
     * 页面名称
     */
    private String name;
    
    /**
     * 页面标题
     */
    private String title;
    
    /**
     * 页面路径
     */
    private String path;
    
    /**
     * 页面配置（JSON格式）
     */
    private String config;
    
    /**
     * 页面排序
     */
    private Integer sort;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
