package com.web.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模板实体类
 */
@Data
@TableName("template")
public class TemplateEntity {
    
    /**
     * 模板ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 模板描述
     */
    private String description;
    
    /**
     * 模板分类
     */
    private String category;
    
    /**
     * 模板缩略图
     */
    private String thumbnail;
    
    /**
     * 模板配置（JSON格式）
     */
    private String config;
    
    /**
     * 是否系统内置（0-否，1-是）
     */
    private Integer isSystem;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
