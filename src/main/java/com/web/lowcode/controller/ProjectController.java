package com.web.lowcode.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.common.PageResult;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.entity.UserEntity;
import com.web.lowcode.security.SecurityUtils;
import com.web.lowcode.service.ProjectService;
import com.web.lowcode.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 项目控制器
 */
@Tag(name = "项目管理", description = "项目相关接口")
@RestController
@RequestMapping("/api/projects")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserService userService;

    /**
     * 分页查询项目列表
     */
    @Operation(summary = "分页查询项目列表")
    @GetMapping("/page")
    @PreAuthorize("isAuthenticated()")
    public Result<PageResult<ProjectEntity>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "项目名称") @RequestParam(required = false) String name) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        Page<ProjectEntity> page = new Page<>(current, size);
        Page<ProjectEntity> pageResult = projectService.pageByUser(page, user.getId(), name);

        PageResult<ProjectEntity> result = new PageResult<>(
                pageResult.getTotal(),
                pageResult.getRecords(),
                pageResult.getCurrent(),
                pageResult.getSize()
        );

        return Result.success(result);
    }

    /**
     * 获取项目详情
     */
    @Operation(summary = "获取项目详情")
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<ProjectEntity> getById(@Parameter(description = "项目ID") @PathVariable Long id) {
        // 检查项目ID是否有效
        if (id == null || id <= 0) {
            return Result.error("无效的项目ID");
        }
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        ProjectEntity project = projectService.getById(id);
        if (project == null) {
            return Result.error("项目不存在");
        }

        // 检查项目所有权
        if (!project.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权访问该项目");
        }

        return Result.success(project);
    }

    /**
     * 创建项目
     */
    @Operation(summary = "创建项目")
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    public Result<ProjectEntity> create(@RequestBody ProjectEntity project) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        project.setCreatorId(user.getId());
        project.setStatus(0);
        project.setCreateTime(LocalDateTime.now());
        project.setUpdateTime(LocalDateTime.now());

        projectService.save(project);

        return Result.success(project);
    }

    /**
     * 更新项目
     */
    @Operation(summary = "更新项目")
    @PutMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<ProjectEntity> update(
            @Parameter(description = "项目ID") @PathVariable Long id,
            @RequestBody ProjectEntity project) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查项目是否存在
        ProjectEntity existingProject = projectService.getById(id);
        if (existingProject == null) {
            return Result.error("项目不存在");
        }

        // 检查项目所有权
        if (!existingProject.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权更新该项目");
        }

        project.setId(id);
        project.setUpdateTime(LocalDateTime.now());

        // 不允许修改创建者ID
        project.setCreatorId(null);

        projectService.updateById(project);

        return Result.success(project);
    }

    /**
     * 删除项目
     */
    @Operation(summary = "删除项目")
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> delete(@Parameter(description = "项目ID") @PathVariable Long id) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查项目是否存在
        ProjectEntity existingProject = projectService.getById(id);
        if (existingProject == null) {
            return Result.error("项目不存在");
        }

        // 检查项目所有权
        if (!existingProject.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权删除该项目");
        }

        projectService.removeById(id);
        return Result.success();
    }
}
