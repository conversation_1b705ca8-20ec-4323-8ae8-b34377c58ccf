package com.web.lowcode.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.common.PageResult;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.DataSourceEntity;
import com.web.lowcode.service.DataSourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据源控制器
 */
@Tag(name = "数据源管理", description = "数据源相关接口")
@RestController
@RequestMapping("/api/datasources")
public class DataSourceController {
    
    @Autowired
    private DataSourceService dataSourceService;
    
    /**
     * 分页查询数据源列表
     */
    @Operation(summary = "分页查询数据源列表")
    @GetMapping("/page")
    public Result<PageResult<DataSourceEntity>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "项目ID") @RequestParam(required = false) Long projectId,
            @Parameter(description = "数据源名称") @RequestParam(required = false) String name,
            @Parameter(description = "数据源类型") @RequestParam(required = false) String type) {
        
        Page<DataSourceEntity> page = new Page<>(current, size);
        LambdaQueryWrapper<DataSourceEntity> wrapper = new LambdaQueryWrapper<>();
        
        if (projectId != null) {
            wrapper.eq(DataSourceEntity::getProjectId, projectId);
        }
        
        if (StringUtils.hasText(name)) {
            wrapper.like(DataSourceEntity::getName, name);
        }
        
        if (StringUtils.hasText(type)) {
            wrapper.eq(DataSourceEntity::getType, type);
        }
        
        wrapper.orderByDesc(DataSourceEntity::getCreateTime);
        
        Page<DataSourceEntity> pageResult = dataSourceService.page(page, wrapper);
        
        PageResult<DataSourceEntity> result = new PageResult<>(
                pageResult.getTotal(),
                pageResult.getRecords(),
                pageResult.getCurrent(),
                pageResult.getSize()
        );
        
        return Result.success(result);
    }
    
    /**
     * 获取数据源详情
     */
    @Operation(summary = "获取数据源详情")
    @GetMapping("/{id}")
    public Result<DataSourceEntity> getById(@Parameter(description = "数据源ID") @PathVariable Long id) {
        DataSourceEntity dataSourceEntity = dataSourceService.getById(id);
        return Result.success(dataSourceEntity);
    }
    
    /**
     * 获取项目下的所有数据源
     */
    @Operation(summary = "获取项目下的所有数据源")
    @GetMapping
    public Result<List<DataSourceEntity>> list(
            @Parameter(description = "项目ID") @RequestParam Long projectId,
            @Parameter(description = "数据源类型") @RequestParam(required = false) String type) {
        
        LambdaQueryWrapper<DataSourceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataSourceEntity::getProjectId, projectId);
        
        if (StringUtils.hasText(type)) {
            wrapper.eq(DataSourceEntity::getType, type);
        }
        
        wrapper.orderByDesc(DataSourceEntity::getCreateTime);
        
        List<DataSourceEntity> list = dataSourceService.list(wrapper);
        return Result.success(list);
    }
    
    /**
     * 创建数据源
     */
    @Operation(summary = "创建数据源")
    @PostMapping
    public Result<DataSourceEntity> create(@RequestBody DataSourceEntity dataSourceEntity) {
        // 模拟用户ID，实际应从登录用户获取
        Long userId = 1L;
        
        dataSourceEntity.setCreatorId(userId);
        dataSourceEntity.setCreateTime(LocalDateTime.now());
        dataSourceEntity.setUpdateTime(LocalDateTime.now());
        
        dataSourceService.save(dataSourceEntity);
        
        return Result.success(dataSourceEntity);
    }
    
    /**
     * 更新数据源
     */
    @Operation(summary = "更新数据源")
    @PutMapping("/{id}")
    public Result<DataSourceEntity> update(
            @Parameter(description = "数据源ID") @PathVariable Long id,
            @RequestBody DataSourceEntity dataSourceEntity) {
        
        dataSourceEntity.setId(id);
        dataSourceEntity.setUpdateTime(LocalDateTime.now());
        
        dataSourceService.updateById(dataSourceEntity);
        
        return Result.success(dataSourceEntity);
    }
    
    /**
     * 删除数据源
     */
    @Operation(summary = "删除数据源")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@Parameter(description = "数据源ID") @PathVariable Long id) {
        dataSourceService.removeById(id);
        return Result.success();
    }
    
    /**
     * 测试数据源连接
     */
    @Operation(summary = "测试数据源连接")
    @PostMapping("/test")
    public Result<Void> testConnection(@RequestBody DataSourceEntity dataSourceEntity) {
        // TODO: 实现数据源连接测试逻辑
        return Result.success();
    }
}
