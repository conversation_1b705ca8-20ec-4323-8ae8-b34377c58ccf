package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.UserEntity;
import com.web.lowcode.security.JwtTokenProvider;
import com.web.lowcode.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Collections;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@Tag(name = "用户管理", description = "用户相关接口")
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 用户登录
     */
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginForm) {
        String username = loginForm.get("username");
        String password = loginForm.get("password");

        // 检查用户名和密码是否为空
        if (username == null || username.isEmpty() || password == null || password.isEmpty()) {
            return Result.error("用户名和密码不能为空");
        }

        // 验证用户是否存在
        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        try {
            // 直接验证密码
            boolean passwordMatches = passwordEncoder.matches(password, user.getPassword());
            System.out.println("Password matches: " + passwordMatches);
            System.out.println("Input password: " + password);
            System.out.println("Stored password hash: " + user.getPassword());

            if (!passwordMatches) {
                return Result.error("密码错误");
            }

            // 创建认证令牌
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                username,
                password,
                Collections.singletonList(new SimpleGrantedAuthority(user.getRole()))
            );

            // 设置到安全上下文
            SecurityContextHolder.getContext().setAuthentication(authToken);

            // 生成JWT令牌
            String token = tokenProvider.createToken(authToken);

            // 清空密码
            user.setPassword(null);

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", user);

            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public Result<UserEntity> register(@RequestBody UserEntity user) {
        try {
            // 设置创建时间和更新时间
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());

            // 创建用户
            UserEntity createdUser = userService.createUser(user);

            // 清空密码
            createdUser.setPassword(null);

            return Result.success(createdUser);
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("注册失败，请稍后再试");
        }
    }

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取当前用户信息")
    @GetMapping("/info")
    public Result<UserEntity> getUserInfo(@AuthenticationPrincipal UserDetails userDetails) {
        if (userDetails == null) {
            return Result.error("用户未登录");
        }

        UserEntity user = userService.getByUsername(userDetails.getUsername());
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 清空密码
        user.setPassword(null);

        return Result.success(user);
    }

    /**
     * 更新用户信息
     */
    @Operation(summary = "更新用户信息")
    @PutMapping("/info")
    public Result<UserEntity> updateUserInfo(@RequestBody UserEntity user, @AuthenticationPrincipal UserDetails userDetails) {
        if (userDetails == null) {
            return Result.error("用户未登录");
        }

        // 获取当前用户
        UserEntity currentUser = userService.getByUsername(userDetails.getUsername());
        if (currentUser == null) {
            return Result.error("用户不存在");
        }

        user.setId(currentUser.getId());
        user.setUpdateTime(LocalDateTime.now());

        // 不允许修改用户名、密码和角色
        user.setUsername(null);
        user.setPassword(null);
        user.setRole(null);

        userService.updateById(user);

        return Result.success(user);
    }

    /**
     * 修改密码
     */
    @Operation(summary = "修改密码")
    @PostMapping("/password")
    public Result<Void> updatePassword(@RequestBody Map<String, String> passwordForm, @AuthenticationPrincipal UserDetails userDetails) {
        if (userDetails == null) {
            return Result.error("用户未登录");
        }

        String oldPassword = passwordForm.get("oldPassword");
        String newPassword = passwordForm.get("newPassword");

        if (oldPassword == null || newPassword == null) {
            return Result.error("密码不能为空");
        }

        // 获取当前用户
        UserEntity user = userService.getByUsername(userDetails.getUsername());
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            return Result.error("旧密码错误");
        }

        // 更新密码
        UserEntity updateUser = new UserEntity();
        updateUser.setId(user.getId());
        updateUser.setPassword(passwordEncoder.encode(newPassword)); // 密码加密
        updateUser.setUpdateTime(LocalDateTime.now());

        userService.updateById(updateUser);

        return Result.success();
    }

    /**
     * 用户登出
     */
    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public Result<Void> logout(@AuthenticationPrincipal UserDetails userDetails) {
        // 在客户端清除token，JWT无状态，服务端不需要存储会话
        // 实际项目中可以实现黑名单机制来使用户的token失效
        SecurityContextHolder.clearContext();
        return Result.success();
    }
}
