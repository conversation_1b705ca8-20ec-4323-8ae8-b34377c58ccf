package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ComponentEntity;
import com.web.lowcode.service.ComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 组件初始化控制器
 */
@Tag(name = "组件初始化", description = "组件初始化相关接口")
@RestController
@RequestMapping("/api/component-init")
public class ComponentInitController {

    private static final Logger logger = LoggerFactory.getLogger(ComponentInitController.class);

    @Autowired
    private ComponentService componentService;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 初始化容器组件
     */
    @Operation(summary = "初始化容器组件")
    @PostMapping("/containers")
    public Result<List<ComponentEntity>> initContainerComponents() {
        logger.info("Initializing container components...");

        List<ComponentEntity> addedComponents = new ArrayList<>();

        try {
            // 卡片容器
            if (!componentService.lambdaQuery().eq(ComponentEntity::getType, "card-container").exists()) {
                ComponentEntity cardContainer = new ComponentEntity();
                cardContainer.setName("卡片容器");
                cardContainer.setType("card-container");
                cardContainer.setCategory("layout");
                cardContainer.setIcon("Document");
                cardContainer.setProps("{\"title\":\"卡片标题\",\"shadow\":\"always\",\"children\":[]}");
                cardContainer.setStyles("{\"width\":\"100%\",\"margin\":\"10px 0\",\"padding\":\"0\"}");
                cardContainer.setIsSystem(1);
                cardContainer.setEnabled(1);
                cardContainer.setCreatorId(1L);
                cardContainer.setCreateTime(LocalDateTime.now());
                cardContainer.setUpdateTime(LocalDateTime.now());

                componentService.save(cardContainer);
                addedComponents.add(cardContainer);
                logger.info("Added card-container component");
            }

            // 标签页容器
            if (!componentService.lambdaQuery().eq(ComponentEntity::getType, "tab-container").exists()) {
                ComponentEntity tabContainer = new ComponentEntity();
                tabContainer.setName("标签页容器");
                tabContainer.setType("tab-container");
                tabContainer.setCategory("layout");
                tabContainer.setIcon("Menu");
                tabContainer.setProps("{\"tabs\":[{\"title\":\"标签页1\",\"name\":\"tab1\",\"children\":[]},{\"title\":\"标签页2\",\"name\":\"tab2\",\"children\":[]}],\"activeName\":\"tab1\"}");
                tabContainer.setStyles("{\"width\":\"100%\",\"margin\":\"10px 0\"}");
                tabContainer.setIsSystem(1);
                tabContainer.setEnabled(1);
                tabContainer.setCreatorId(1L);
                tabContainer.setCreateTime(LocalDateTime.now());
                tabContainer.setUpdateTime(LocalDateTime.now());

                componentService.save(tabContainer);
                addedComponents.add(tabContainer);
                logger.info("Added tab-container component");
            }

            // 折叠面板容器
            if (!componentService.lambdaQuery().eq(ComponentEntity::getType, "collapse-container").exists()) {
                ComponentEntity collapseContainer = new ComponentEntity();
                collapseContainer.setName("折叠面板容器");
                collapseContainer.setType("collapse-container");
                collapseContainer.setCategory("layout");
                collapseContainer.setIcon("ArrowDown");
                collapseContainer.setProps("{\"items\":[{\"title\":\"面板1\",\"name\":\"panel1\",\"children\":[]},{\"title\":\"面板2\",\"name\":\"panel2\",\"children\":[]}],\"activeNames\":[\"panel1\"]}");
                collapseContainer.setStyles("{\"width\":\"100%\",\"margin\":\"10px 0\"}");
                collapseContainer.setIsSystem(1);
                collapseContainer.setEnabled(1);
                collapseContainer.setCreatorId(1L);
                collapseContainer.setCreateTime(LocalDateTime.now());
                collapseContainer.setUpdateTime(LocalDateTime.now());

                componentService.save(collapseContainer);
                addedComponents.add(collapseContainer);
                logger.info("Added collapse-container component");
            }

            // 网格容器
            if (!componentService.lambdaQuery().eq(ComponentEntity::getType, "grid-container").exists()) {
                ComponentEntity gridContainer = new ComponentEntity();
                gridContainer.setName("网格容器");
                gridContainer.setType("grid-container");
                gridContainer.setCategory("layout");
                gridContainer.setIcon("Grid");
                gridContainer.setProps("{\"cols\":3,\"gutter\":20,\"children\":[]}");
                gridContainer.setStyles("{\"width\":\"100%\",\"margin\":\"10px 0\"}");
                gridContainer.setIsSystem(1);
                gridContainer.setEnabled(1);
                gridContainer.setCreatorId(1L);
                gridContainer.setCreateTime(LocalDateTime.now());
                gridContainer.setUpdateTime(LocalDateTime.now());

                componentService.save(gridContainer);
                addedComponents.add(gridContainer);
                logger.info("Added grid-container component");
            }

            logger.info("Container components initialization completed. Added {} components", addedComponents.size());
            return Result.success(addedComponents);
        } catch (Exception e) {
            logger.error("Failed to initialize container components", e);
            return Result.error("初始化容器组件失败: " + e.getMessage());
        }
    }

    /**
     * 更新间距组件
     */
    @Operation(summary = "更新间距组件")
    @PostMapping("/update-space")
    public Result<String> updateSpaceComponent() {
        logger.info("Updating space component...");

        try {
            // 执行SQL脚本更新间距组件
            jdbcTemplate.execute("UPDATE `component` SET `props` = '{\"direction\":\"horizontal\",\"size\":\"medium\",\"wrap\":false,\"alignment\":\"center\",\"itemCount\":4,\"itemType\":\"card\",\"items\":[{\"id\":\"item_1\",\"type\":\"card\",\"content\":\"Item 1\",\"events\":{\"click\":{\"type\":\"message\",\"message\":\"\\u70b9\\u51fb\\u4e86\\u7b2c1\\u4e2a\\u5361\\u7247\",\"messageType\":\"success\"}}},{\"id\":\"item_2\",\"type\":\"button\",\"content\":\"Button 2\",\"buttonType\":\"primary\",\"events\":{\"click\":{\"type\":\"message\",\"message\":\"\\u70b9\\u51fb\\u4e86\\u7b2c2\\u4e2a\\u6309\\u94ae\",\"messageType\":\"warning\"}}},{\"id\":\"item_3\",\"type\":\"text\",\"content\":\"Text 3\",\"events\":{\"click\":{\"type\":\"link\",\"url\":\"https://www.example.com\",\"target\":\"_blank\"}}},{\"id\":\"item_4\",\"type\":\"button\",\"content\":\"\\u9875\\u9762\\u5bfc\\u822a\",\"buttonType\":\"success\",\"events\":{\"click\":{\"type\":\"navigate\",\"pageType\":\"external\",\"pagePath\":\"/dashboard\"}}}]}' WHERE `type` = 'space';");

            logger.info("Space component updated successfully");
            return Result.success("间距组件更新成功");
        } catch (Exception e) {
            logger.error("Failed to update space component", e);
            return Result.error("更新间距组件失败: " + e.getMessage());
        }
    }
}
