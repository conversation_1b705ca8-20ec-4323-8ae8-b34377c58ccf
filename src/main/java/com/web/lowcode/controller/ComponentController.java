package com.web.lowcode.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.common.PageResult;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ComponentEntity;
import com.web.lowcode.service.ComponentService;
import com.web.lowcode.util.ImageUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组件控制器
 */
@Tag(name = "组件管理", description = "组件相关接口")
@RestController
@RequestMapping("/api/components")
public class ComponentController {

    private static final Logger logger = LoggerFactory.getLogger(ComponentController.class);

    @Autowired
    private ComponentService componentService;

    /**
     * 分页查询组件列表
     */
    @Operation(summary = "分页查询组件列表")
    @GetMapping("/page")
    @PreAuthorize("isAuthenticated()")
    public Result<PageResult<ComponentEntity>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "组件名称") @RequestParam(required = false) String name,
            @Parameter(description = "组件类型") @RequestParam(required = false) String type,
            @Parameter(description = "组件分类") @RequestParam(required = false) String category) {

        Page<ComponentEntity> page = new Page<>(current, size);
        LambdaQueryWrapper<ComponentEntity> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(name)) {
            wrapper.like(ComponentEntity::getName, name);
        }

        if (StringUtils.hasText(type)) {
            wrapper.eq(ComponentEntity::getType, type);
        }

        if (StringUtils.hasText(category)) {
            wrapper.eq(ComponentEntity::getCategory, category);
        }

        wrapper.orderByDesc(ComponentEntity::getCreateTime);

        Page<ComponentEntity> pageResult = componentService.page(page, wrapper);

        PageResult<ComponentEntity> result = new PageResult<>(
                pageResult.getTotal(),
                pageResult.getRecords(),
                pageResult.getCurrent(),
                pageResult.getSize()
        );

        return Result.success(result);
    }

    /**
     * 获取组件详情
     */
    @Operation(summary = "获取组件详情")
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentEntity> getById(@Parameter(description = "组件ID") @PathVariable Long id) {
        ComponentEntity component = componentService.getById(id);
        return Result.success(component);
    }

    /**
     * 获取所有组件
     */
    @Operation(summary = "获取所有组件")
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public Result<List<ComponentEntity>> list(
            @Parameter(description = "组件类型") @RequestParam(required = false) String type,
            @Parameter(description = "组件分类") @RequestParam(required = false) String category) {

        LambdaQueryWrapper<ComponentEntity> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(type)) {
            wrapper.eq(ComponentEntity::getType, type);
        }

        if (StringUtils.hasText(category)) {
            wrapper.eq(ComponentEntity::getCategory, category);
        }

        wrapper.orderByAsc(ComponentEntity::getCategory).orderByAsc(ComponentEntity::getType);

        List<ComponentEntity> list = componentService.list(wrapper);
        logger.info("Returning {} components", list.size());
        return Result.success(list);
    }

    /**
     * 创建组件
     */
    @Operation(summary = "创建组件")
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentEntity> create(@RequestBody ComponentEntity component) {
        // 模拟用户ID，实际应从登录用户获取
        Long userId = 1L;

        component.setCreatorId(userId);
        component.setIsSystem(0); // 非系统内置
        component.setCreateTime(LocalDateTime.now());
        component.setUpdateTime(LocalDateTime.now());

        componentService.save(component);

        return Result.success(component);
    }

    /**
     * 更新组件
     */
    @Operation(summary = "更新组件")
    @PutMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentEntity> update(
            @Parameter(description = "组件ID") @PathVariable Long id,
            @RequestBody ComponentEntity component) {

        component.setId(id);
        component.setUpdateTime(LocalDateTime.now());

        componentService.updateById(component);

        return Result.success(component);
    }

    /**
     * 删除组件
     */
    @Operation(summary = "删除组件")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@Parameter(description = "组件ID") @PathVariable Long id) {
        // 检查是否为系统内置组件
        ComponentEntity component = componentService.getById(id);
        if (component != null && component.getIsSystem() == 1) {
            return Result.error("系统内置组件不允许删除");
        }

        componentService.removeById(id);
        return Result.success();
    }

    /**
     * 将组件图标转换为Base64格式
     *
     * @param id 组件ID
     * @param iconData 图标数据
     * @return 更新后的组件
     */
    @PutMapping("/{id}/icon")
    @Operation(summary = "更新组件图标为Base64格式")
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentEntity> updateComponentIcon(
            @PathVariable Long id,
            @RequestBody Map<String, String> iconData) {
        try {
            // 获取图标数据
            String base64Icon = iconData.get("icon");
            if (base64Icon == null || base64Icon.isEmpty()) {
                return Result.error("图标数据不能为空");
            }

            // 检查是否已经是Base64格式
            if (!base64Icon.startsWith("data:")) {
                // 生成占位图标
                base64Icon = ImageUtil.generateColoredIconBase64(base64Icon, 24, "#409EFF");
            }

            // 更新组件图标
            ComponentEntity component = componentService.getById(id);
            if (component == null) {
                return Result.error("组件不存在");
            }

            component.setIcon(base64Icon);
            component.setUpdateTime(LocalDateTime.now());

            componentService.updateById(component);

            return Result.success(component);
        } catch (Exception e) {
            logger.error("Failed to update component icon: " + id, e);
            return Result.error("更新组件图标失败: " + e.getMessage());
        }
    }

    /**
     * 批量将所有组件图标转换为Base64格式
     *
     * @return 转换结果
     */
    @PostMapping("/convert-icons")
    @Operation(summary = "批量将所有组件图标转换为Base64格式")
    @PreAuthorize("isAuthenticated()")
    public Result<Map<String, Object>> convertAllComponentIcons() {
        try {
            List<ComponentEntity> components = componentService.list();
            int totalCount = components.size();
            int convertedCount = 0;
            List<String> failedComponents = new ArrayList<>();

            for (ComponentEntity component : components) {
                try {
                    String icon = component.getIcon();
                    if (icon != null && !icon.isEmpty() && !icon.startsWith("data:")) {
                        // 生成占位图标
                        String base64Icon = ImageUtil.generateColoredIconBase64(icon, 24, "#409EFF");

                        component.setIcon(base64Icon);
                        component.setUpdateTime(LocalDateTime.now());

                        componentService.updateById(component);
                        convertedCount++;
                    }
                } catch (Exception e) {
                    logger.error("Failed to convert icon for component: " + component.getName(), e);
                    failedComponents.add(component.getName());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("convertedCount", convertedCount);
            result.put("failedComponents", failedComponents);

            return Result.success(result);
        } catch (Exception e) {
            logger.error("Failed to convert all component icons", e);
            return Result.error("批量转换组件图标失败: " + e.getMessage());
        }
    }
}
