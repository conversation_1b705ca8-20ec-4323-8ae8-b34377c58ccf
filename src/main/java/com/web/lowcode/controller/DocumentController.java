package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.service.PageService;
import com.web.lowcode.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档导出控制器
 */
@Tag(name = "文档导出", description = "文档导出相关接口")
@RestController
@RequestMapping("/api/document")
public class DocumentController {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private PageService pageService;

    /**
     * 导出项目文档
     */
    @Operation(summary = "导出项目文档")
    @GetMapping("/project/{id}")
    public ResponseEntity<ByteArrayResource> exportProjectDocument(
            @Parameter(description = "项目ID") @PathVariable Long id,
            @Parameter(description = "导出格式，支持md、html") @RequestParam(defaultValue = "md") String format) {

        // 获取项目信息
        ProjectEntity project = projectService.getById(id);
        if (project == null) {
            return ResponseEntity.notFound().build();
        }

        // 获取项目下的所有页面
        List<PageEntity> pages = pageService.getPagesByProjectId(id);

        // 生成文档内容
        String content;
        String contentType;
        String fileExtension;

        if ("html".equalsIgnoreCase(format)) {
            content = generateHtmlDocument(project, pages);
            contentType = MediaType.TEXT_HTML_VALUE;
            fileExtension = "html";
        } else {
            // 默认为Markdown格式
            content = generateMarkdownDocument(project, pages);
            contentType = MediaType.TEXT_MARKDOWN_VALUE;
            fileExtension = "md";
        }

        // 设置文件名
        String originalFilename = "project_" + project.getName() + "_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) +
                "." + fileExtension;

        // 对文件名进行 URL 编码，确保只包含 ASCII 字符
        String encodedFilename;
        try {
            encodedFilename = java.net.URLEncoder.encode(originalFilename, StandardCharsets.UTF_8.name())
                    .replace("+", "%20"); // 替换空格的编码
        } catch (UnsupportedEncodingException e) {
            encodedFilename = originalFilename; // 如果编码失败，使用原始文件名
        }

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        // 使用 RFC 5987 格式设置 Content-Disposition 头部
        headers.add(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename);

        // 返回文件
        ByteArrayResource resource = new ByteArrayResource(content.getBytes(StandardCharsets.UTF_8));

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType(contentType))
                .body(resource);
    }

    /**
     * 导出页面文档
     */
    @Operation(summary = "导出页面文档")
    @GetMapping("/page/{id}")
    public ResponseEntity<ByteArrayResource> exportPageDocument(
            @Parameter(description = "页面ID") @PathVariable Long id,
            @Parameter(description = "导出格式，支持md、html") @RequestParam(defaultValue = "md") String format) {

        // 获取页面信息
        PageEntity page = pageService.getById(id);
        if (page == null) {
            return ResponseEntity.notFound().build();
        }

        // 生成文档内容
        String content;
        String contentType;
        String fileExtension;

        if ("html".equalsIgnoreCase(format)) {
            content = generateHtmlPageDocument(page);
            contentType = MediaType.TEXT_HTML_VALUE;
            fileExtension = "html";
        } else {
            // 默认为Markdown格式
            content = generateMarkdownPageDocument(page);
            contentType = MediaType.TEXT_MARKDOWN_VALUE;
            fileExtension = "md";
        }

        // 设置文件名
        String originalFilename = "page_" + page.getName() + "_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) +
                "." + fileExtension;

        // 对文件名进行 URL 编码，确保只包含 ASCII 字符
        String encodedFilename;
        try {
            encodedFilename = java.net.URLEncoder.encode(originalFilename, StandardCharsets.UTF_8.name())
                    .replace("+", "%20"); // 替换空格的编码
        } catch (UnsupportedEncodingException e) {
            encodedFilename = originalFilename; // 如果编码失败，使用原始文件名
        }

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        // 使用 RFC 5987 格式设置 Content-Disposition 头部
        headers.add(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename);

        // 返回文件
        ByteArrayResource resource = new ByteArrayResource(content.getBytes(StandardCharsets.UTF_8));

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType(contentType))
                .body(resource);
    }

    /**
     * 获取文档格式列表
     */
    @Operation(summary = "获取文档格式列表")
    @GetMapping("/formats")
    public Result<List<Map<String, String>>> getDocumentFormats() {
        List<Map<String, String>> formats = List.of(
                createFormat("md", "Markdown", "Markdown格式文档"),
                createFormat("html", "HTML", "HTML格式文档")
        );

        return Result.success(formats);
    }

    /**
     * 创建格式信息
     */
    private Map<String, String> createFormat(String value, String label, String description) {
        Map<String, String> format = new HashMap<>();
        format.put("value", value);
        format.put("label", label);
        format.put("description", description);
        return format;
    }

    /**
     * 生成Markdown格式的项目文档
     */
    private String generateMarkdownDocument(ProjectEntity project, List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();

        // 项目标题
        sb.append("# ").append(project.getName()).append("\n\n");

        // 项目描述
        if (project.getDescription() != null && !project.getDescription().isEmpty()) {
            sb.append(project.getDescription()).append("\n\n");
        }

        // 项目信息
        sb.append("## 项目信息\n\n");
        sb.append("- **项目ID**: ").append(project.getId()).append("\n");
        sb.append("- **创建时间**: ").append(formatDateTime(project.getCreateTime())).append("\n");
        sb.append("- **更新时间**: ").append(formatDateTime(project.getUpdateTime())).append("\n");
        sb.append("- **状态**: ").append(project.getStatus() == 1 ? "已发布" : "草稿").append("\n\n");

        // 页面列表
        sb.append("## 页面列表\n\n");

        if (pages.isEmpty()) {
            sb.append("暂无页面\n\n");
        } else {
            for (PageEntity page : pages) {
                sb.append("### ").append(page.getName()).append("\n\n");
                sb.append("- **页面ID**: ").append(page.getId()).append("\n");
                sb.append("- **页面标题**: ").append(page.getTitle()).append("\n");
                sb.append("- **页面路径**: ").append(page.getPath()).append("\n");
                sb.append("- **创建时间**: ").append(formatDateTime(page.getCreateTime())).append("\n");
                sb.append("- **更新时间**: ").append(formatDateTime(page.getUpdateTime())).append("\n\n");

                // 页面组件信息
                sb.append("#### 组件信息\n\n");

                if (page.getConfig() != null && !page.getConfig().isEmpty()) {
                    sb.append("```json\n");
                    sb.append(page.getConfig());
                    sb.append("\n```\n\n");
                } else {
                    sb.append("暂无组件信息\n\n");
                }
            }
        }

        // 导出信息
        sb.append("---\n\n");
        sb.append("*文档生成时间: ").append(formatDateTime(LocalDateTime.now())).append("*\n");

        return sb.toString();
    }

    /**
     * 生成HTML格式的项目文档
     */
    private String generateHtmlDocument(ProjectEntity project, List<PageEntity> pages) {
        StringBuilder sb = new StringBuilder();

        // HTML头部
        sb.append("<!DOCTYPE html>\n");
        sb.append("<html lang=\"zh-CN\">\n");
        sb.append("<head>\n");
        sb.append("  <meta charset=\"UTF-8\">\n");
        sb.append("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        sb.append("  <title>").append(project.getName()).append(" - 项目文档</title>\n");
        sb.append("  <style>\n");
        sb.append("    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }\n");
        sb.append("    h1 { color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 10px; }\n");
        sb.append("    h2 { color: #3498db; margin-top: 30px; }\n");
        sb.append("    h3 { color: #2980b9; margin-top: 25px; }\n");
        sb.append("    h4 { color: #16a085; }\n");
        sb.append("    .info-item { margin: 5px 0; }\n");
        sb.append("    .page-section { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }\n");
        sb.append("    pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }\n");
        sb.append("    code { font-family: Consolas, monospace; }\n");
        sb.append("    .footer { margin-top: 50px; border-top: 1px solid #eee; padding-top: 10px; color: #7f8c8d; font-size: 0.9em; }\n");
        sb.append("  </style>\n");
        sb.append("</head>\n");
        sb.append("<body>\n");

        // 项目标题
        sb.append("  <h1>").append(project.getName()).append("</h1>\n");

        // 项目描述
        if (project.getDescription() != null && !project.getDescription().isEmpty()) {
            sb.append("  <p>").append(project.getDescription()).append("</p>\n");
        }

        // 项目信息
        sb.append("  <h2>项目信息</h2>\n");
        sb.append("  <div class=\"info-section\">\n");
        sb.append("    <p class=\"info-item\"><strong>项目ID:</strong> ").append(project.getId()).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>创建时间:</strong> ").append(formatDateTime(project.getCreateTime())).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>更新时间:</strong> ").append(formatDateTime(project.getUpdateTime())).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>状态:</strong> ").append(project.getStatus() == 1 ? "已发布" : "草稿").append("</p>\n");
        sb.append("  </div>\n");

        // 页面列表
        sb.append("  <h2>页面列表</h2>\n");

        if (pages.isEmpty()) {
            sb.append("  <p>暂无页面</p>\n");
        } else {
            for (PageEntity page : pages) {
                sb.append("  <div class=\"page-section\">\n");
                sb.append("    <h3>").append(page.getName()).append("</h3>\n");
                sb.append("    <p class=\"info-item\"><strong>页面ID:</strong> ").append(page.getId()).append("</p>\n");
                sb.append("    <p class=\"info-item\"><strong>页面标题:</strong> ").append(page.getTitle()).append("</p>\n");
                sb.append("    <p class=\"info-item\"><strong>页面路径:</strong> ").append(page.getPath()).append("</p>\n");
                sb.append("    <p class=\"info-item\"><strong>创建时间:</strong> ").append(formatDateTime(page.getCreateTime())).append("</p>\n");
                sb.append("    <p class=\"info-item\"><strong>更新时间:</strong> ").append(formatDateTime(page.getUpdateTime())).append("</p>\n");

                // 页面组件信息
                sb.append("    <h4>组件信息</h4>\n");

                if (page.getConfig() != null && !page.getConfig().isEmpty()) {
                    sb.append("    <pre><code>");
                    sb.append(page.getConfig().replace("<", "&lt;").replace(">", "&gt;"));
                    sb.append("</code></pre>\n");
                } else {
                    sb.append("    <p>暂无组件信息</p>\n");
                }

                sb.append("  </div>\n");
            }
        }

        // 页脚
        sb.append("  <div class=\"footer\">\n");
        sb.append("    <p>文档生成时间: ").append(formatDateTime(LocalDateTime.now())).append("</p>\n");
        sb.append("  </div>\n");

        // HTML尾部
        sb.append("</body>\n");
        sb.append("</html>");

        return sb.toString();
    }

    /**
     * 生成Markdown格式的页面文档
     */
    private String generateMarkdownPageDocument(PageEntity page) {
        StringBuilder sb = new StringBuilder();

        // 页面标题
        sb.append("# ").append(page.getName()).append("\n\n");

        // 页面信息
        sb.append("## 页面信息\n\n");
        sb.append("- **页面ID**: ").append(page.getId()).append("\n");
        sb.append("- **页面标题**: ").append(page.getTitle()).append("\n");
        sb.append("- **页面路径**: ").append(page.getPath()).append("\n");
        sb.append("- **所属项目ID**: ").append(page.getProjectId()).append("\n");
        sb.append("- **创建时间**: ").append(formatDateTime(page.getCreateTime())).append("\n");
        sb.append("- **更新时间**: ").append(formatDateTime(page.getUpdateTime())).append("\n\n");

        // 页面组件信息
        sb.append("## 组件信息\n\n");

        if (page.getConfig() != null && !page.getConfig().isEmpty()) {
            sb.append("```json\n");
            sb.append(page.getConfig());
            sb.append("\n```\n\n");
        } else {
            sb.append("暂无组件信息\n\n");
        }

        // 导出信息
        sb.append("---\n\n");
        sb.append("*文档生成时间: ").append(formatDateTime(LocalDateTime.now())).append("*\n");

        return sb.toString();
    }

    /**
     * 生成HTML格式的页面文档
     */
    private String generateHtmlPageDocument(PageEntity page) {
        StringBuilder sb = new StringBuilder();

        // HTML头部
        sb.append("<!DOCTYPE html>\n");
        sb.append("<html lang=\"zh-CN\">\n");
        sb.append("<head>\n");
        sb.append("  <meta charset=\"UTF-8\">\n");
        sb.append("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        sb.append("  <title>").append(page.getName()).append(" - 页面文档</title>\n");
        sb.append("  <style>\n");
        sb.append("    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }\n");
        sb.append("    h1 { color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 10px; }\n");
        sb.append("    h2 { color: #3498db; margin-top: 30px; }\n");
        sb.append("    .info-item { margin: 5px 0; }\n");
        sb.append("    pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }\n");
        sb.append("    code { font-family: Consolas, monospace; }\n");
        sb.append("    .footer { margin-top: 50px; border-top: 1px solid #eee; padding-top: 10px; color: #7f8c8d; font-size: 0.9em; }\n");
        sb.append("  </style>\n");
        sb.append("</head>\n");
        sb.append("<body>\n");

        // 页面标题
        sb.append("  <h1>").append(page.getName()).append("</h1>\n");

        // 页面信息
        sb.append("  <h2>页面信息</h2>\n");
        sb.append("  <div class=\"info-section\">\n");
        sb.append("    <p class=\"info-item\"><strong>页面ID:</strong> ").append(page.getId()).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>页面标题:</strong> ").append(page.getTitle()).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>页面路径:</strong> ").append(page.getPath()).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>所属项目ID:</strong> ").append(page.getProjectId()).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>创建时间:</strong> ").append(formatDateTime(page.getCreateTime())).append("</p>\n");
        sb.append("    <p class=\"info-item\"><strong>更新时间:</strong> ").append(formatDateTime(page.getUpdateTime())).append("</p>\n");
        sb.append("  </div>\n");

        // 页面组件信息
        sb.append("  <h2>组件信息</h2>\n");

        if (page.getConfig() != null && !page.getConfig().isEmpty()) {
            sb.append("  <pre><code>");
            sb.append(page.getConfig().replace("<", "&lt;").replace(">", "&gt;"));
            sb.append("</code></pre>\n");
        } else {
            sb.append("  <p>暂无组件信息</p>\n");
        }

        // 页脚
        sb.append("  <div class=\"footer\">\n");
        sb.append("    <p>文档生成时间: ").append(formatDateTime(LocalDateTime.now())).append("</p>\n");
        sb.append("  </div>\n");

        // HTML尾部
        sb.append("</body>\n");
        sb.append("</html>");

        return sb.toString();
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "N/A";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
