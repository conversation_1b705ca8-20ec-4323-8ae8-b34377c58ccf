package com.web.lowcode.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ImageEntity;
import com.web.lowcode.entity.ImageLogEntity;
import com.web.lowcode.service.ImageLogService;
import com.web.lowcode.service.ImageService;
import com.web.lowcode.service.MinioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图片管理控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/images")
@RequiredArgsConstructor
@Tag(name = "图片管理", description = "图片上传、下载、管理相关接口")
public class ImageController {
    
    private final ImageService imageService;
    private final ImageLogService imageLogService;
    private final MinioService minioService;
    
    /**
     * 上传单个图片
     */
    @PostMapping("/upload")
    @Operation(summary = "上传单个图片")
    public Result<ImageEntity> uploadImage(
            @Parameter(description = "图片文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "图片分类（1-组件图标，2-用户上传，3-系统图片，4-模板缩略图）") @RequestParam(value = "category", defaultValue = "2") Integer category,
            @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
            @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) Long businessId,
            @Parameter(description = "备注信息") @RequestParam(value = "remark", required = false) String remark) {

        try {
            log.info("接收到图片上传请求: 文件名={}, 大小={}, 分类={}, 业务类型={}, 业务ID={}",
                    file.getOriginalFilename(), file.getSize(), category, businessType, businessId);

            ImageEntity image = imageService.uploadImage(file, category, businessType, businessId, remark);

            log.info("图片上传成功: ID={}, URL={}", image.getId(), image.getUrl());

            return Result.success(image);
        } catch (Exception e) {
            log.error("上传图片失败", e);
            return Result.error("上传图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量上传图片
     */
    @PostMapping("/upload/batch")
    @Operation(summary = "批量上传图片")
    public Result<List<ImageEntity>> uploadImages(
            @Parameter(description = "图片文件数组") @RequestParam("files") MultipartFile[] files,
            @Parameter(description = "图片分类") @RequestParam(value = "category", defaultValue = "2") Integer category,
            @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
            @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) Long businessId,
            @Parameter(description = "备注信息") @RequestParam(value = "remark", required = false) String remark) {
        
        try {
            List<ImageEntity> images = imageService.uploadImages(files, category, businessType, businessId, remark);
            return Result.success(images);
        } catch (Exception e) {
            log.error("批量上传图片失败", e);
            return Result.error("批量上传图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询图片列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询图片列表")
    public Result<IPage<ImageEntity>> getImagePage(
            @Parameter(description = "当前页") @RequestParam(value = "current", defaultValue = "1") int current,
            @Parameter(description = "页大小") @RequestParam(value = "size", defaultValue = "10") int size,
            @Parameter(description = "图片分类") @RequestParam(value = "category", required = false) Integer category,
            @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
            @Parameter(description = "上传者ID") @RequestParam(value = "uploaderId", required = false) Long uploaderId,
            @Parameter(description = "状态") @RequestParam(value = "status", required = false) Integer status,
            @Parameter(description = "开始时间") @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "关键词") @RequestParam(value = "keyword", required = false) String keyword) {
        
        try {
            IPage<ImageEntity> page = imageService.getImagePage(current, size, category, businessType, 
                                                               uploaderId, status, startTime, endTime, keyword);
            return Result.success(page);
        } catch (Exception e) {
            log.error("查询图片列表失败", e);
            return Result.error("查询图片列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取图片详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取图片详情")
    public Result<ImageEntity> getImageById(@Parameter(description = "图片ID") @PathVariable Long id) {
        try {
            ImageEntity image = imageService.getImageById(id);
            if (image == null) {
                return Result.error("图片不存在");
            }
            return Result.success(image);
        } catch (Exception e) {
            log.error("获取图片详情失败: {}", id, e);
            return Result.error("获取图片详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据业务信息查询图片列表
     */
    @GetMapping("/business")
    @Operation(summary = "根据业务信息查询图片列表")
    public Result<List<ImageEntity>> getImagesByBusiness(
            @Parameter(description = "业务类型") @RequestParam("businessType") String businessType,
            @Parameter(description = "业务ID") @RequestParam("businessId") Long businessId) {
        
        try {
            List<ImageEntity> images = imageService.getImagesByBusiness(businessType, businessId);
            return Result.success(images);
        } catch (Exception e) {
            log.error("根据业务信息查询图片失败", e);
            return Result.error("查询图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新图片信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新图片信息")
    public Result<Boolean> updateImage(
            @Parameter(description = "图片ID") @PathVariable Long id,
            @RequestBody ImageEntity image) {
        
        try {
            image.setId(id);
            boolean result = imageService.updateImage(image);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新图片信息失败: {}", id, e);
            return Result.error("更新图片信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除图片（软删除）
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除图片")
    public Result<Boolean> deleteImage(@Parameter(description = "图片ID") @PathVariable Long id) {
        try {
            boolean result = imageService.deleteImage(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("删除图片失败: {}", id, e);
            return Result.error("删除图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除图片
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除图片")
    public Result<Boolean> deleteImages(@Parameter(description = "图片ID列表") @RequestBody List<Long> ids) {
        try {
            boolean result = imageService.deleteImages(ids);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量删除图片失败", e);
            return Result.error("批量删除图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 恢复已删除的图片
     */
    @PutMapping("/{id}/restore")
    @Operation(summary = "恢复已删除的图片")
    public Result<Boolean> restoreImage(@Parameter(description = "图片ID") @PathVariable Long id) {
        try {
            boolean result = imageService.restoreImage(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("恢复图片失败: {}", id, e);
            return Result.error("恢复图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 永久删除图片
     */
    @DeleteMapping("/{id}/permanent")
    @Operation(summary = "永久删除图片")
    public Result<Boolean> permanentDeleteImage(@Parameter(description = "图片ID") @PathVariable Long id) {
        try {
            boolean result = imageService.permanentDeleteImage(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("永久删除图片失败: {}", id, e);
            return Result.error("永久删除图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 复制图片
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制图片")
    public Result<ImageEntity> copyImage(
            @Parameter(description = "源图片ID") @PathVariable Long id,
            @Parameter(description = "业务类型") @RequestParam(value = "businessType", required = false) String businessType,
            @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) Long businessId,
            @Parameter(description = "备注信息") @RequestParam(value = "remark", required = false) String remark) {
        
        try {
            ImageEntity image = imageService.copyImage(id, businessType, businessId, remark);
            return Result.success(image);
        } catch (Exception e) {
            log.error("复制图片失败: {}", id, e);
            return Result.error("复制图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载图片
     */
    @GetMapping("/{id}/download")
    @Operation(summary = "下载图片")
    public ResponseEntity<InputStreamResource> downloadImage(@Parameter(description = "图片ID") @PathVariable Long id) {
        try {
            ImageEntity image = imageService.getImageById(id);
            if (image == null) {
                return ResponseEntity.notFound().build();
            }
            
            InputStream inputStream = minioService.downloadFile(image.getMinioPath());
            
            // 记录下载日志
            imageLogService.recordLog(id, "DOWNLOAD", "下载图片", null, null, "SUCCESS", null);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + image.getOriginalName() + "\"")
                    .contentType(MediaType.parseMediaType(image.getContentType()))
                    .body(new InputStreamResource(inputStream));
                    
        } catch (Exception e) {
            log.error("下载图片失败: {}", id, e);
            imageLogService.recordLog(id, "DOWNLOAD", "下载图片失败", null, null, "FAILED", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取图片统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取图片统计信息")
    public Result<Map<String, Object>> getImageStatistics() {
        try {
            Map<String, Object> statistics = imageService.getImageStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取图片统计信息失败", e);
            return Result.error("获取图片统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分类统计信息
     */
    @GetMapping("/statistics/category/{category}")
    @Operation(summary = "获取分类统计信息")
    public Result<Map<String, Object>> getCategoryStatistics(@Parameter(description = "图片分类") @PathVariable Integer category) {
        try {
            Map<String, Object> statistics = imageService.getCategoryStatistics(category);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取分类统计信息失败", e);
            return Result.error("获取分类统计信息失败: " + e.getMessage());
        }
    }
}
