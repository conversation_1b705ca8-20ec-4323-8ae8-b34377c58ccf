package com.web.lowcode.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.common.PageResult;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.entity.UserEntity;
import com.web.lowcode.security.SecurityUtils;
import com.web.lowcode.service.PageService;
import com.web.lowcode.service.ProjectService;
import com.web.lowcode.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 页面兼容控制器
 * 用于处理前端请求的 /api/page 路径
 */
@Tag(name = "页面管理兼容", description = "页面相关接口兼容层")
@RestController
@RequestMapping("/api/page")
public class PageCompatController {

    @Autowired
    private PageService pageService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserService userService;

    /**
     * 获取项目下的所有页面
     */
    @Operation(summary = "获取项目下的所有页面")
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public Result<List<PageEntity>> list(@Parameter(description = "项目ID") @RequestParam Long projectId) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查项目ID是否有效
        if (projectId == null || projectId <= 0) {
            return Result.error("无效的项目ID");
        }

        // 检查项目是否存在
        ProjectEntity project = projectService.getById(projectId);
        if (project == null) {
            return Result.error("项目不存在");
        }

        // 非管理员只能访问自己的项目
        if (!project.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权访问该项目的页面");
        }

        LambdaQueryWrapper<PageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PageEntity::getProjectId, projectId);
        wrapper.orderByAsc(PageEntity::getSort).orderByDesc(PageEntity::getCreateTime);

        List<PageEntity> list = pageService.list(wrapper);
        return Result.success(list);
    }

    /**
     * 分页查询页面列表
     */
    @Operation(summary = "分页查询页面列表")
    @GetMapping("/page")
    @PreAuthorize("isAuthenticated()")
    public Result<PageResult<PageEntity>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "项目ID") @RequestParam(required = false) Long projectId,
            @Parameter(description = "页面名称") @RequestParam(required = false) String name) {

        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        Page<PageEntity> page = new Page<>(current, size);
        LambdaQueryWrapper<PageEntity> wrapper = new LambdaQueryWrapper<>();

        if (projectId != null) {
            wrapper.eq(PageEntity::getProjectId, projectId);
        } else {
            // 如果没有指定项目ID，非管理员只能查看自己的项目页面
            if (!"管理员".equals(user.getNickname())) {
                wrapper.inSql(PageEntity::getProjectId, "SELECT id FROM project WHERE creator_id = " + user.getId());
            }
        }

        if (StringUtils.hasText(name)) {
            wrapper.like(PageEntity::getName, name);
        }

        wrapper.orderByAsc(PageEntity::getSort).orderByDesc(PageEntity::getCreateTime);

        Page<PageEntity> pageResult = pageService.page(page, wrapper);

        PageResult<PageEntity> result = new PageResult<>(
                pageResult.getTotal(),
                pageResult.getRecords(),
                pageResult.getCurrent(),
                pageResult.getSize()
        );

        return Result.success(result);
    }

    /**
     * 获取页面详情
     */
    @Operation(summary = "获取页面详情")
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<PageEntity> getById(@Parameter(description = "页面ID") @PathVariable Long id) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        PageEntity page = pageService.getById(id);
        if (page == null) {
            return Result.error("页面不存在");
        }

        // 检查项目是否存在
        ProjectEntity project = projectService.getById(page.getProjectId());
        if (project == null) {
            return Result.error("项目不存在");
        }

        // 非管理员只能访问自己的项目页面
        if (!project.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权访问该页面");
        }

        return Result.success(page);
    }

    /**
     * 创建页面
     */
    @Operation(summary = "创建页面")
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    public Result<PageEntity> create(@RequestBody PageEntity page) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查项目是否存在
        if (page.getProjectId() == null) {
            return Result.error("项目ID不能为空");
        }

        ProjectEntity project = projectService.getById(page.getProjectId());
        if (project == null) {
            return Result.error("项目不存在");
        }

        // 非管理员只能在自己的项目下创建页面
        if (!project.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权在该项目下创建页面");
        }

        // 设置页面排序
        if (page.getSort() == null) {
            // 获取当前项目下最大的排序值
            LambdaQueryWrapper<PageEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PageEntity::getProjectId, page.getProjectId());
            wrapper.orderByDesc(PageEntity::getSort);
            wrapper.last("LIMIT 1");
            PageEntity maxSortPage = pageService.getOne(wrapper);
            if (maxSortPage != null) {
                page.setSort(maxSortPage.getSort() + 10);
            } else {
                page.setSort(10);
            }
        }

        page.setCreateTime(LocalDateTime.now());
        page.setUpdateTime(LocalDateTime.now());

        pageService.save(page);

        return Result.success(page);
    }

    /**
     * 更新页面
     */
    @Operation(summary = "更新页面")
    @PutMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<PageEntity> update(
            @Parameter(description = "页面ID") @PathVariable Long id,
            @RequestBody PageEntity page) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查页面是否存在
        PageEntity existingPage = pageService.getById(id);
        if (existingPage == null) {
            return Result.error("页面不存在");
        }

        // 检查项目是否存在
        ProjectEntity project = projectService.getById(existingPage.getProjectId());
        if (project == null) {
            return Result.error("项目不存在");
        }

        // 非管理员只能更新自己的项目页面
        if (!project.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权更新该页面");
        }

        page.setId(id);
        page.setUpdateTime(LocalDateTime.now());

        // 不允许修改项目ID
        page.setProjectId(null);

        pageService.updateById(page);

        return Result.success(page);
    }

    /**
     * 删除页面
     */
    @Operation(summary = "删除页面")
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> delete(@Parameter(description = "页面ID") @PathVariable Long id) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查页面是否存在
        PageEntity existingPage = pageService.getById(id);
        if (existingPage == null) {
            return Result.error("页面不存在");
        }

        // 检查项目是否存在
        ProjectEntity project = projectService.getById(existingPage.getProjectId());
        if (project == null) {
            return Result.error("项目不存在");
        }

        // 非管理员只能删除自己的项目页面
        if (!project.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权删除该页面");
        }

        pageService.removeById(id);
        return Result.success();
    }

    /**
     * 更新页面排序
     */
    @Operation(summary = "更新页面排序")
    @PutMapping("/{id}/sort")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> updateSort(
            @Parameter(description = "页面ID") @PathVariable Long id,
            @Parameter(description = "排序值") @RequestParam Integer sort) {
        // 获取当前登录用户
        String username = SecurityUtils.getCurrentUsername()
                .orElseThrow(() -> new IllegalStateException("用户未登录"));

        UserEntity user = userService.getByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查页面是否存在
        PageEntity existingPage = pageService.getById(id);
        if (existingPage == null) {
            return Result.error("页面不存在");
        }

        // 检查项目是否存在
        ProjectEntity project = projectService.getById(existingPage.getProjectId());
        if (project == null) {
            return Result.error("项目不存在");
        }

        // 非管理员只能更新自己的项目页面
        if (!project.getCreatorId().equals(user.getId()) && !"管理员".equals(user.getNickname())) {
            return Result.error("无权更新该页面");
        }

        PageEntity page = new PageEntity();
        page.setId(id);
        page.setSort(sort);
        page.setUpdateTime(LocalDateTime.now());

        pageService.updateById(page);

        return Result.success();
    }
}
