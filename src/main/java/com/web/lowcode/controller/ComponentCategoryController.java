package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ComponentCategoryEntity;
import com.web.lowcode.service.ComponentCategoryService;
import com.web.lowcode.util.ImageUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组件分类控制器
 */
@RestController
@RequestMapping("/api/component-categories")
@Tag(name = "组件分类管理", description = "组件分类管理相关接口")
public class ComponentCategoryController {

    private static final Logger logger = LoggerFactory.getLogger(ComponentCategoryController.class);

    @Autowired
    private ComponentCategoryService componentCategoryService;

    /**
     * 获取所有组件分类
     *
     * @return 组件分类列表
     */
    @GetMapping
    @Operation(summary = "获取所有组件分类")
    @PreAuthorize("isAuthenticated()")
    public Result<List<ComponentCategoryEntity>> getAllCategories() {
        List<ComponentCategoryEntity> categories = componentCategoryService.getAllCategories();
        logger.info("Returning {} component categories", categories.size());
        return Result.success(categories);
    }

    /**
     * 根据编码获取组件分类
     *
     * @param code 分类编码
     * @return 组件分类
     */
    @GetMapping("/{code}")
    @Operation(summary = "根据编码获取组件分类")
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentCategoryEntity> getCategoryByCode(@PathVariable String code) {
        try {
            ComponentCategoryEntity category = componentCategoryService.getCategoryByCode(code);
            if (category == null) {
                return Result.error("分类不存在");
            }
            return Result.success(category);
        } catch (Exception e) {
            logger.error("Failed to get component category by code: " + code, e);
            return Result.error("获取组件分类失败");
        }
    }

    /**
     * 创建组件分类
     *
     * @param category 组件分类
     * @return 创建后的组件分类
     */
    @PostMapping
    @Operation(summary = "创建组件分类")
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentCategoryEntity> createCategory(@RequestBody ComponentCategoryEntity category) {
        try {
            ComponentCategoryEntity createdCategory = componentCategoryService.createCategory(category);
            return Result.success(createdCategory);
        } catch (IllegalArgumentException e) {
            logger.error("Failed to create component category", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            logger.error("Failed to create component category", e);
            return Result.error("创建组件分类失败");
        }
    }

    /**
     * 更新组件分类
     *
     * @param id       分类ID
     * @param category 组件分类
     * @return 更新后的组件分类
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新组件分类")
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentCategoryEntity> updateCategory(@PathVariable Long id, @RequestBody ComponentCategoryEntity category) {
        try {
            ComponentCategoryEntity updatedCategory = componentCategoryService.updateCategory(id, category);
            return Result.success(updatedCategory);
        } catch (IllegalArgumentException e) {
            logger.error("Failed to update component category: " + id, e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            logger.error("Failed to update component category: " + id, e);
            return Result.error("更新组件分类失败");
        }
    }

    /**
     * 删除组件分类
     *
     * @param id 分类ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除组件分类")
    public Result<Boolean> deleteCategory(@PathVariable Long id) {
        try {
            boolean deleted = componentCategoryService.deleteCategory(id);
            return Result.success(deleted);
        } catch (IllegalArgumentException e) {
            logger.error("Failed to delete component category: " + id, e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            logger.error("Failed to delete component category: " + id, e);
            return Result.error("删除组件分类失败");
        }
    }

    /**
     * 将组件分类图标转换为Base64格式
     *
     * @param id 分类ID
     * @param iconData 图标数据
     * @return 更新后的组件分类
     */
    @PutMapping("/{id}/icon")
    @Operation(summary = "更新组件分类图标为Base64格式")
    @PreAuthorize("isAuthenticated()")
    public Result<ComponentCategoryEntity> updateCategoryIcon(
            @PathVariable Long id,
            @RequestBody Map<String, String> iconData) {
        try {
            // 获取图标数据
            String base64Icon = iconData.get("icon");
            if (base64Icon == null || base64Icon.isEmpty()) {
                return Result.error("图标数据不能为空");
            }

            // 检查是否已经是Base64格式
            if (!base64Icon.startsWith("data:")) {
                // 生成占位图标
                base64Icon = ImageUtil.generateColoredIconBase64(base64Icon, 24, "#409EFF");
            }

            // 更新分类图标
            ComponentCategoryEntity category = componentCategoryService.getById(id);
            if (category == null) {
                return Result.error("分类不存在");
            }

            category.setIcon(base64Icon);
            category.setUpdateTime(LocalDateTime.now());

            componentCategoryService.updateById(category);

            return Result.success(category);
        } catch (Exception e) {
            logger.error("Failed to update category icon: " + id, e);
            return Result.error("更新组件分类图标失败: " + e.getMessage());
        }
    }

    /**
     * 批量将所有组件分类图标转换为Base64格式
     *
     * @return 转换结果
     */
    @PostMapping("/convert-icons")
    @Operation(summary = "批量将所有组件分类图标转换为Base64格式")
    @PreAuthorize("isAuthenticated()")
    public Result<Map<String, Object>> convertAllCategoryIcons() {
        try {
            List<ComponentCategoryEntity> categories = componentCategoryService.getAllCategories();
            int totalCount = categories.size();
            int convertedCount = 0;
            List<String> failedCategories = new ArrayList<>();

            for (ComponentCategoryEntity category : categories) {
                try {
                    String icon = category.getIcon();
                    if (icon != null && !icon.isEmpty() && !icon.startsWith("data:")) {
                        // 生成占位图标
                        String base64Icon = ImageUtil.generateColoredIconBase64(icon, 24, "#409EFF");

                        category.setIcon(base64Icon);
                        category.setUpdateTime(LocalDateTime.now());

                        componentCategoryService.updateById(category);
                        convertedCount++;
                    }
                } catch (Exception e) {
                    logger.error("Failed to convert icon for category: " + category.getName(), e);
                    failedCategories.add(category.getName());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("convertedCount", convertedCount);
            result.put("failedCategories", failedCategories);

            return Result.success(result);
        } catch (Exception e) {
            logger.error("Failed to convert all category icons", e);
            return Result.error("批量转换组件分类图标失败: " + e.getMessage());
        }
    }
}
