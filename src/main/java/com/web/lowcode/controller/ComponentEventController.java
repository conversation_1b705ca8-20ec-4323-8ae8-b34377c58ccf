package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ComponentEntity;
import com.web.lowcode.service.ComponentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 组件事件控制器
 */
@Tag(name = "组件事件管理", description = "组件事件相关接口")
@RestController
@RequestMapping("/api/component-events")
public class ComponentEventController {

    private static final Logger logger = LoggerFactory.getLogger(ComponentEventController.class);

    @Autowired
    private ComponentService componentService;

    /**
     * 获取组件事件配置
     */
    @Operation(summary = "获取组件事件配置")
    @GetMapping("/components/{id}")
    public Result<Map<String, Object>> getComponentEvents(@Parameter(description = "组件ID") @PathVariable Long id) {
        ComponentEntity component = componentService.getById(id);
        if (component == null) {
            return Result.error("组件不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("id", component.getId());
        result.put("name", component.getName());
        result.put("type", component.getType());
        result.put("events", component.getEvents());

        return Result.success(result);
    }

    /**
     * 更新组件事件配置
     */
    @Operation(summary = "更新组件事件配置")
    @PutMapping("/components/{id}")
    public Result<ComponentEntity> updateComponentEvents(
            @Parameter(description = "组件ID") @PathVariable Long id,
            @RequestBody Map<String, Object> eventsData) {
        
        ComponentEntity component = componentService.getById(id);
        if (component == null) {
            return Result.error("组件不存在");
        }

        // 获取事件数据
        String events = eventsData.get("events") != null ? eventsData.get("events").toString() : null;
        
        // 验证事件数据格式
        if (events != null) {
            try {
                new JSONArray(events);
            } catch (Exception e) {
                return Result.error("事件数据格式不正确，应为JSON数组");
            }
        }

        // 更新组件事件配置
        component.setEvents(events);
        component.setUpdateTime(LocalDateTime.now());
        componentService.updateById(component);

        logger.info("Component events updated: {}", component.getName());
        return Result.success(component);
    }

    /**
     * 清空组件事件配置
     */
    @Operation(summary = "清空组件事件配置")
    @DeleteMapping("/components/{id}")
    public Result<ComponentEntity> clearComponentEvents(@Parameter(description = "组件ID") @PathVariable Long id) {
        ComponentEntity component = componentService.getById(id);
        if (component == null) {
            return Result.error("组件不存在");
        }

        // 清空组件事件配置
        component.setEvents(null);
        component.setUpdateTime(LocalDateTime.now());
        componentService.updateById(component);

        logger.info("Component events cleared: {}", component.getName());
        return Result.success(component);
    }

    /**
     * 批量更新组件事件配置
     */
    @Operation(summary = "批量更新组件事件配置")
    @PutMapping("/components/batch")
    public Result<Map<String, Object>> batchUpdateComponentEvents(@RequestBody List<Map<String, Object>> batchData) {
        int successCount = 0;
        int failCount = 0;

        for (Map<String, Object> data : batchData) {
            try {
                Long id = Long.parseLong(data.get("id").toString());
                String events = data.get("events") != null ? data.get("events").toString() : null;

                ComponentEntity component = componentService.getById(id);
                if (component != null) {
                    // 验证事件数据格式
                    if (events != null) {
                        try {
                            new JSONArray(events);
                        } catch (Exception e) {
                            logger.error("Invalid events data format for component: " + id, e);
                            failCount++;
                            continue;
                        }
                    }

                    // 更新组件事件配置
                    component.setEvents(events);
                    component.setUpdateTime(LocalDateTime.now());
                    componentService.updateById(component);
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                logger.error("Failed to update component events", e);
                failCount++;
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", batchData.size());
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        return Result.success(result);
    }

    /**
     * 获取可用事件类型
     */
    @Operation(summary = "获取可用事件类型")
    @GetMapping("/event-types")
    public Result<List<Map<String, Object>>> getEventTypes() {
        // 返回预定义的事件类型列表
        List<Map<String, Object>> eventTypes = List.of(
            Map.of("value", "click", "label", "点击", "description", "当用户点击组件时触发"),
            Map.of("value", "dblclick", "label", "双击", "description", "当用户双击组件时触发"),
            Map.of("value", "mouseenter", "label", "鼠标进入", "description", "当鼠标指针进入组件时触发"),
            Map.of("value", "mouseleave", "label", "鼠标离开", "description", "当鼠标指针离开组件时触发"),
            Map.of("value", "input", "label", "输入变化", "description", "当输入框内容变化时触发"),
            Map.of("value", "change", "label", "值变化", "description", "当组件值变化时触发"),
            Map.of("value", "focus", "label", "获得焦点", "description", "当组件获得焦点时触发"),
            Map.of("value", "blur", "label", "失去焦点", "description", "当组件失去焦点时触发"),
            Map.of("value", "submit", "label", "提交", "description", "当表单提交时触发"),
            Map.of("value", "keydown", "label", "按键按下", "description", "当键盘按键按下时触发"),
            Map.of("value", "keyup", "label", "按键释放", "description", "当键盘按键释放时触发")
        );
        
        return Result.success(eventTypes);
    }

    /**
     * 获取可用动作类型
     */
    @Operation(summary = "获取可用动作类型")
    @GetMapping("/action-types")
    public Result<List<Map<String, Object>>> getActionTypes() {
        // 返回预定义的动作类型列表
        List<Map<String, Object>> actionTypes = List.of(
            Map.of(
                "value", "navigate",
                "label", "页面跳转",
                "description", "跳转到指定页面"
            ),
            Map.of(
                "value", "showMessage",
                "label", "显示消息",
                "description", "显示提示消息"
            ),
            Map.of(
                "value", "callApi",
                "label", "调用接口",
                "description", "调用后端API接口"
            ),
            Map.of(
                "value", "updateComponent",
                "label", "更新组件",
                "description", "更新其他组件的属性或状态"
            ),
            Map.of(
                "value", "executeScript",
                "label", "执行脚本",
                "description", "执行自定义JavaScript代码"
            ),
            Map.of(
                "value", "showModal",
                "label", "显示弹窗",
                "description", "显示模态对话框"
            ),
            Map.of(
                "value", "refreshData",
                "label", "刷新数据",
                "description", "重新加载数据"
            ),
            Map.of(
                "value", "validateForm",
                "label", "表单验证",
                "description", "触发表单验证"
            )
        );
        
        return Result.success(actionTypes);
    }
}
