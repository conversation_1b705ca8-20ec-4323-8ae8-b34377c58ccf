package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ComponentEntity;
import com.web.lowcode.entity.ComponentCategoryEntity;
import com.web.lowcode.service.ComponentService;
import com.web.lowcode.service.ComponentCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组件启用/禁用控制器
 */
@Tag(name = "组件启用管理", description = "组件启用/禁用相关接口")
@RestController
@RequestMapping("/api/component-enable")
public class ComponentEnableController {

    private static final Logger logger = LoggerFactory.getLogger(ComponentEnableController.class);

    @Autowired
    private ComponentService componentService;

    @Autowired
    private ComponentCategoryService componentCategoryService;

    /**
     * 启用组件
     */
    @Operation(summary = "启用组件")
    @PutMapping("/components/{id}/enable")
    public Result<ComponentEntity> enableComponent(@Parameter(description = "组件ID") @PathVariable Long id) {
        ComponentEntity component = componentService.getById(id);
        if (component == null) {
            return Result.error("组件不存在");
        }

        component.setEnabled(1);
        component.setUpdateTime(LocalDateTime.now());
        componentService.updateById(component);

        logger.info("Component enabled: {}", component.getName());
        return Result.success(component);
    }

    /**
     * 禁用组件
     */
    @Operation(summary = "禁用组件")
    @PutMapping("/components/{id}/disable")
    public Result<ComponentEntity> disableComponent(@Parameter(description = "组件ID") @PathVariable Long id) {
        ComponentEntity component = componentService.getById(id);
        if (component == null) {
            return Result.error("组件不存在");
        }

        component.setEnabled(0);
        component.setUpdateTime(LocalDateTime.now());
        componentService.updateById(component);

        logger.info("Component disabled: {}", component.getName());
        return Result.success(component);
    }

    /**
     * 批量启用组件
     */
    @Operation(summary = "批量启用组件")
    @PutMapping("/components/batch-enable")
    public Result<Map<String, Object>> batchEnableComponents(@RequestBody List<Long> ids) {
        int successCount = 0;
        for (Long id : ids) {
            try {
                ComponentEntity component = componentService.getById(id);
                if (component != null) {
                    component.setEnabled(1);
                    component.setUpdateTime(LocalDateTime.now());
                    componentService.updateById(component);
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("Failed to enable component: " + id, e);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", ids.size());
        result.put("successCount", successCount);
        return Result.success(result);
    }

    /**
     * 批量禁用组件
     */
    @Operation(summary = "批量禁用组件")
    @PutMapping("/components/batch-disable")
    public Result<Map<String, Object>> batchDisableComponents(@RequestBody List<Long> ids) {
        int successCount = 0;
        for (Long id : ids) {
            try {
                ComponentEntity component = componentService.getById(id);
                if (component != null) {
                    component.setEnabled(0);
                    component.setUpdateTime(LocalDateTime.now());
                    componentService.updateById(component);
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("Failed to disable component: " + id, e);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", ids.size());
        result.put("successCount", successCount);
        return Result.success(result);
    }

    /**
     * 启用组件分类
     */
    @Operation(summary = "启用组件分类")
    @PutMapping("/categories/{id}/enable")
    public Result<ComponentCategoryEntity> enableCategory(@Parameter(description = "分类ID") @PathVariable Long id) {
        ComponentCategoryEntity category = componentCategoryService.getById(id);
        if (category == null) {
            return Result.error("分类不存在");
        }

        category.setEnabled(1);
        category.setUpdateTime(LocalDateTime.now());
        componentCategoryService.updateById(category);

        logger.info("Category enabled: {}", category.getName());
        return Result.success(category);
    }

    /**
     * 禁用组件分类
     */
    @Operation(summary = "禁用组件分类")
    @PutMapping("/categories/{id}/disable")
    public Result<ComponentCategoryEntity> disableCategory(@Parameter(description = "分类ID") @PathVariable Long id) {
        ComponentCategoryEntity category = componentCategoryService.getById(id);
        if (category == null) {
            return Result.error("分类不存在");
        }

        category.setEnabled(0);
        category.setUpdateTime(LocalDateTime.now());
        componentCategoryService.updateById(category);

        logger.info("Category disabled: {}", category.getName());
        return Result.success(category);
    }

    /**
     * 批量启用组件分类
     */
    @Operation(summary = "批量启用组件分类")
    @PutMapping("/categories/batch-enable")
    public Result<Map<String, Object>> batchEnableCategories(@RequestBody List<Long> ids) {
        int successCount = 0;
        for (Long id : ids) {
            try {
                ComponentCategoryEntity category = componentCategoryService.getById(id);
                if (category != null) {
                    category.setEnabled(1);
                    category.setUpdateTime(LocalDateTime.now());
                    componentCategoryService.updateById(category);
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("Failed to enable category: " + id, e);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", ids.size());
        result.put("successCount", successCount);
        return Result.success(result);
    }

    /**
     * 批量禁用组件分类
     */
    @Operation(summary = "批量禁用组件分类")
    @PutMapping("/categories/batch-disable")
    public Result<Map<String, Object>> batchDisableCategories(@RequestBody List<Long> ids) {
        int successCount = 0;
        for (Long id : ids) {
            try {
                ComponentCategoryEntity category = componentCategoryService.getById(id);
                if (category != null) {
                    category.setEnabled(0);
                    category.setUpdateTime(LocalDateTime.now());
                    componentCategoryService.updateById(category);
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("Failed to disable category: " + id, e);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", ids.size());
        result.put("successCount", successCount);
        return Result.success(result);
    }
}
