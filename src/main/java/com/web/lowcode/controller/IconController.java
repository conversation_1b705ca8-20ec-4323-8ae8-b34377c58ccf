package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 图标控制器
 */
@RestController
@RequestMapping("/icons")
@Tag(name = "图标管理", description = "图标上传和获取相关接口")
public class IconController {

    private static final Logger logger = LoggerFactory.getLogger(IconController.class);

    @Value("${file.upload-dir:./uploads/icons}")
    private String uploadDir;

    /**
     * 上传图标
     *
     * @param file 图标文件
     * @return 上传结果
     */
    @PostMapping("/upload")
    @Operation(summary = "上传图标")
    public Result<Map<String, String>> uploadIcon(@RequestParam("file") MultipartFile file) {
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }

            // 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.error("只能上传图片文件");
            }

            // 创建上传目录
            Path uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            Files.createDirectories(uploadPath);

            // 生成文件名
            String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = UUID.randomUUID() + fileExtension;

            // 保存文件
            Path targetLocation = uploadPath.resolve(filename);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // 返回文件信息
            Map<String, String> fileInfo = new HashMap<>();
            fileInfo.put("filename", filename);
            fileInfo.put("url", "/icons/" + filename);
            fileInfo.put("originalFilename", originalFilename);
            fileInfo.put("size", String.valueOf(file.getSize()));
            fileInfo.put("contentType", contentType);

            logger.info("Icon uploaded successfully: {}", filename);
            return Result.success(fileInfo);
        } catch (IOException ex) {
            logger.error("Failed to upload icon", ex);
            return Result.error("上传图标失败: " + ex.getMessage());
        }
    }

    /**
     * 获取图标
     *
     * @param filename 图标文件名
     * @return 图标文件
     */
    @GetMapping("/{filename:.+}")
    @Operation(summary = "获取图标")
    public ResponseEntity<Resource> getIcon(@PathVariable String filename) {
        try {
            Path filePath = Paths.get(uploadDir).toAbsolutePath().normalize().resolve(filename);
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists()) {
                // 确定文件的内容类型
                String contentType = determineContentType(filename);

                // 对文件名进行 URL 编码，确保只包含 ASCII 字符
                String originalFilename = resource.getFilename();
                String encodedFilename;
                try {
                    encodedFilename = java.net.URLEncoder.encode(originalFilename, StandardCharsets.UTF_8.name())
                            .replace("+", "%20"); // 替换空格的编码
                } catch (UnsupportedEncodingException e) {
                    encodedFilename = originalFilename; // 如果编码失败，使用原始文件名
                }

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                "inline; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename)
                        .body(resource);
            } else {
                logger.error("Icon not found: {}", filename);
                return ResponseEntity.notFound().build();
            }
        } catch (MalformedURLException ex) {
            logger.error("Failed to get icon", ex);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据文件名确定内容类型
     *
     * @param filename 文件名
     * @return 内容类型
     */
    private String determineContentType(String filename) {
        if (filename.endsWith(".png")) {
            return "image/png";
        } else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (filename.endsWith(".gif")) {
            return "image/gif";
        } else if (filename.endsWith(".svg")) {
            return "image/svg+xml";
        } else {
            return "application/octet-stream";
        }
    }
}
