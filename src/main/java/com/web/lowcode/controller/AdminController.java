package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.UserEntity;
import com.web.lowcode.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器
 */
@Tag(name = "管理员接口", description = "管理员相关接口")
@RestController
@RequestMapping("/api/admin")
public class AdminController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    /**
     * 创建用户
     */
    @Operation(summary = "创建用户")
    @PostMapping("/create-user")
    public Result<Map<String, Object>> createUser(@RequestParam String username, 
                                                 @RequestParam String password,
                                                 @RequestParam String nickname,
                                                 @RequestParam(defaultValue = "ROLE_USER") String role) {
        // 检查用户名是否已存在
        UserEntity existingUser = userService.getByUsername(username);
        
        Map<String, Object> result = new HashMap<>();
        
        if (existingUser != null) {
            result.put("success", false);
            result.put("message", "用户名已存在");
            return Result.success(result);
        }
        
        // 创建新用户
        UserEntity user = new UserEntity();
        user.setUsername(username);
        user.setPassword(passwordEncoder.encode(password));
        user.setNickname(nickname);
        user.setStatus(1);
        user.setRole(role);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        userService.save(user);
        
        // 清空密码
        user.setPassword(null);
        
        result.put("success", true);
        result.put("message", "用户创建成功");
        result.put("user", user);
        
        return Result.success(result);
    }
    
    /**
     * 重置用户密码
     */
    @Operation(summary = "重置用户密码")
    @PostMapping("/reset-password")
    public Result<Map<String, Object>> resetPassword(@RequestParam String username, 
                                                    @RequestParam String newPassword) {
        // 检查用户是否存在
        UserEntity user = userService.getByUsername(username);
        
        Map<String, Object> result = new HashMap<>();
        
        if (user == null) {
            result.put("success", false);
            result.put("message", "用户不存在");
            return Result.success(result);
        }
        
        // 更新密码
        UserEntity updateUser = new UserEntity();
        updateUser.setId(user.getId());
        updateUser.setPassword(passwordEncoder.encode(newPassword));
        updateUser.setUpdateTime(LocalDateTime.now());
        
        userService.updateById(updateUser);
        
        result.put("success", true);
        result.put("message", "密码重置成功");
        
        return Result.success(result);
    }
}
