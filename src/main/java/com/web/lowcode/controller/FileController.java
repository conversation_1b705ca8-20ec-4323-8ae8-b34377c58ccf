package com.web.lowcode.controller;

import com.web.lowcode.service.FileStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件访问控制器
 * 用于提供本地存储文件的HTTP访问
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
@Tag(name = "文件访问", description = "文件下载和访问接口")
public class FileController {
    
    private final FileStorageService fileStorageService;
    
    /**
     * 下载文件
     * 支持本地存储和MinIO存储的文件访问
     */
    @GetMapping("/**")
    @Operation(summary = "下载文件")
    public ResponseEntity<InputStreamResource> downloadFile(
            @Parameter(description = "文件路径") HttpServletRequest request) {
        
        try {
            // 获取文件相对路径
            String requestURI = request.getRequestURI();
            String relativePath = requestURI.substring("/api/files/".length());
            
            log.info("请求下载文件: {}", relativePath);
            
            // 检查文件是否存在
            if (!fileStorageService.fileExists(relativePath)) {
                log.warn("文件不存在: {}", relativePath);
                return ResponseEntity.notFound().build();
            }
            
            // 获取文件流
            InputStream inputStream = fileStorageService.downloadFile(relativePath);
            InputStreamResource resource = new InputStreamResource(inputStream);
            
            // 获取文件信息
            var fileInfo = fileStorageService.getFileInfo(relativePath);
            
            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            
            // 设置内容类型
            String contentType = determineContentType(relativePath);
            headers.setContentType(MediaType.parseMediaType(contentType));
            
            // 设置文件大小（如果可用）
            if (fileInfo.containsKey("size")) {
                headers.setContentLength((Long) fileInfo.get("size"));
            }
            
            // 设置缓存控制
            headers.setCacheControl("public, max-age=31536000"); // 1年缓存
            
            // 对于图片文件，设置为内联显示；其他文件设置为下载
            if (isImageFile(relativePath)) {
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline");
            } else {
                String fileName = getFileName(relativePath);
                String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
                headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                    "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName);
            }
            
            log.info("文件下载成功: {}, 内容类型: {}", relativePath, contentType);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("文件下载失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取文件信息
     */
    @GetMapping("/info/**")
    @Operation(summary = "获取文件信息")
    public ResponseEntity<?> getFileInfo(HttpServletRequest request) {
        try {
            // 获取文件相对路径
            String requestURI = request.getRequestURI();
            String relativePath = requestURI.substring("/api/files/info/".length());
            
            log.info("请求文件信息: {}", relativePath);
            
            // 检查文件是否存在
            if (!fileStorageService.fileExists(relativePath)) {
                return ResponseEntity.notFound().build();
            }
            
            // 获取文件信息
            var fileInfo = fileStorageService.getFileInfo(relativePath);
            
            return ResponseEntity.ok(fileInfo);
            
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 确定文件的内容类型
     */
    private String determineContentType(String relativePath) {
        String fileName = getFileName(relativePath).toLowerCase();
        
        if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else if (fileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (fileName.endsWith(".webp")) {
            return "image/webp";
        } else if (fileName.endsWith(".svg")) {
            return "image/svg+xml";
        } else if (fileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (fileName.endsWith(".txt")) {
            return "text/plain";
        } else if (fileName.endsWith(".json")) {
            return "application/json";
        } else if (fileName.endsWith(".xml")) {
            return "application/xml";
        } else {
            return "application/octet-stream";
        }
    }
    
    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String relativePath) {
        String fileName = getFileName(relativePath).toLowerCase();
        return fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") || 
               fileName.endsWith(".png") || fileName.endsWith(".gif") || 
               fileName.endsWith(".bmp") || fileName.endsWith(".webp") || 
               fileName.endsWith(".svg");
    }
    
    /**
     * 从路径中提取文件名
     */
    private String getFileName(String relativePath) {
        int lastSlash = relativePath.lastIndexOf('/');
        if (lastSlash >= 0) {
            return relativePath.substring(lastSlash + 1);
        }
        return relativePath;
    }
}
