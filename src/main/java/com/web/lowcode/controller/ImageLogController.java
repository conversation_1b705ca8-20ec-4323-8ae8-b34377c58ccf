package com.web.lowcode.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.ImageLogEntity;
import com.web.lowcode.service.ImageLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图片操作日志控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/image-logs")
@RequiredArgsConstructor
@Tag(name = "图片操作日志", description = "图片操作日志查询相关接口")
public class ImageLogController {
    
    private final ImageLogService imageLogService;
    
    /**
     * 分页查询操作日志
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询操作日志")
    public Result<IPage<ImageLogEntity>> getLogPage(
            @Parameter(description = "当前页") @RequestParam(value = "current", defaultValue = "1") int current,
            @Parameter(description = "页大小") @RequestParam(value = "size", defaultValue = "10") int size,
            @Parameter(description = "图片ID") @RequestParam(value = "imageId", required = false) Long imageId,
            @Parameter(description = "操作类型") @RequestParam(value = "operationType", required = false) String operationType,
            @Parameter(description = "操作者ID") @RequestParam(value = "operatorId", required = false) Long operatorId,
            @Parameter(description = "操作结果") @RequestParam(value = "result", required = false) String result,
            @Parameter(description = "开始时间") @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        try {
            IPage<ImageLogEntity> page = imageLogService.getLogPage(current, size, imageId, operationType, 
                                                                   operatorId, result, startTime, endTime);
            return Result.success(page);
        } catch (Exception e) {
            log.error("查询操作日志失败", e);
            return Result.error("查询操作日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据图片ID查询操作日志
     */
    @GetMapping("/image/{imageId}")
    @Operation(summary = "根据图片ID查询操作日志")
    public Result<List<ImageLogEntity>> getLogsByImageId(@Parameter(description = "图片ID") @PathVariable Long imageId) {
        try {
            List<ImageLogEntity> logs = imageLogService.getLogsByImageId(imageId);
            return Result.success(logs);
        } catch (Exception e) {
            log.error("根据图片ID查询操作日志失败: {}", imageId, e);
            return Result.error("查询操作日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 统计操作次数
     */
    @GetMapping("/count")
    @Operation(summary = "统计操作次数")
    public Result<Long> countOperations(
            @Parameter(description = "操作类型") @RequestParam("operationType") String operationType,
            @Parameter(description = "开始时间") @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        try {
            Long count = imageLogService.countOperations(operationType, startTime, endTime);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计操作次数失败", e);
            return Result.error("统计操作次数失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取操作统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取操作统计信息")
    public Result<Map<String, Object>> getOperationStatistics(
            @Parameter(description = "开始时间") @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        try {
            Map<String, Object> statistics = imageLogService.getOperationStatistics(startTime, endTime);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取操作统计信息失败", e);
            return Result.error("获取操作统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理过期日志
     */
    @DeleteMapping("/clean")
    @Operation(summary = "清理过期日志")
    public Result<Integer> cleanExpiredLogs(@Parameter(description = "保留天数") @RequestParam(value = "days", defaultValue = "90") int days) {
        try {
            int cleanedCount = imageLogService.cleanExpiredLogs(days);
            return Result.success(cleanedCount);
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return Result.error("清理过期日志失败: " + e.getMessage());
        }
    }
}
