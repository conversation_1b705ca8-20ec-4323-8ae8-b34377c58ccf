package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.entity.UserEntity;
import com.web.lowcode.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 登录测试控制器
 */
@RestController
@RequestMapping("/api/test-login")
public class LoginTestController {

    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    /**
     * 测试密码是否匹配
     */
    @GetMapping("/check-password")
    public Result<Map<String, Object>> checkPassword(@RequestParam String username, @RequestParam String password) {
        UserEntity user = userService.getByUsername(username);
        
        Map<String, Object> result = new HashMap<>();
        
        if (user == null) {
            result.put("exists", false);
            result.put("message", "用户不存在");
            return Result.success(result);
        }
        
        result.put("exists", true);
        result.put("username", user.getUsername());
        result.put("role", user.getRole());
        
        boolean matches = passwordEncoder.matches(password, user.getPassword());
        result.put("passwordMatches", matches);
        
        if (!matches) {
            result.put("message", "密码不匹配");
            result.put("encodedPassword", user.getPassword());
            
            // 生成新的加密密码用于比较
            String newEncodedPassword = passwordEncoder.encode(password);
            result.put("newEncodedPassword", newEncodedPassword);
        } else {
            result.put("message", "密码匹配成功");
        }
        
        return Result.success(result);
    }
    
    /**
     * 生成加密密码
     */
    @GetMapping("/encode-password")
    public Result<Map<String, Object>> encodePassword(@RequestParam String password) {
        String encodedPassword = passwordEncoder.encode(password);
        
        Map<String, Object> result = new HashMap<>();
        result.put("rawPassword", password);
        result.put("encodedPassword", encodedPassword);
        
        return Result.success(result);
    }
}
