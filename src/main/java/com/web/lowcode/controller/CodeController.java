package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.service.ExportProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 代码生成控制器
 * 处理所有与代码生成相关的HTTP请求
 */
@Tag(name = "代码生成", description = "代码生成相关接口")
@RestController
@RequestMapping("/api/code")
@Slf4j
public class CodeController {

    private final ExportProjectService exportProjectService;

    @Autowired
    public CodeController(ExportProjectService exportProjectService) {
        this.exportProjectService = exportProjectService;
    }

//    /**
//     * 生成Vue页面代码
//     *
//     * @param pageConfig 页面配置信息
//     * @return 生成的Vue代码
//     */
//    @Operation(summary = "生成Vue代码")
//    @PostMapping("/vue")
//    @PreAuthorize("isAuthenticated()")
//    public Result<String> generateVueCode(@RequestBody Map<String, Object> pageConfig) {
//    }
//
//    /**
//     * 生成UniApp代码
//     *
//     * @param pageConfig 页面配置信息
//     * @return 生成的UniApp代码
//     */
//    @Operation(summary = "生成UniApp代码")
//    @PostMapping("/uniapp")
//    @PreAuthorize("isAuthenticated()")
//    public Result<String> generateUniappCode(@RequestBody Map<String, Object> pageConfig) {
//
//    }

    /**
     * 导出项目代码
     *
     * @param exportConfig 导出配置信息
     * @return 包含生成代码的ZIP文件
     */
    @Operation(summary = "导出项目代码")
    @PostMapping("/export")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<byte[]> exportCode(@RequestBody Map<String, Object> exportConfig) {
        log.info("接收到导出项目代码请求: {}", exportConfig);

        try {
            // 获取项目ID和框架类型
            Long projectId = extractProjectId(exportConfig);
            String framework = (String) exportConfig.getOrDefault("framework", "vue");

            log.info("开始导出项目代码，项目ID: {}，框架类型: {}", projectId, framework);

            // 调用导出服务
            return exportProjectService.exportProjectCode(projectId, framework);
        } catch (Exception e) {
            log.error("导出项目代码失败", e);
            return ResponseEntity.badRequest().body(("导出项目代码失败: " + e.getMessage()).getBytes());
        }
    }

    /**
     * 从导出配置中提取项目ID
     *
     * @param exportConfig 导出配置
     * @return 项目ID
     */
    private Long extractProjectId(Map<String, Object> exportConfig) {
        if (exportConfig == null || !exportConfig.containsKey("projectId")) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        Object projectIdObj = exportConfig.get("projectId");
        if (projectIdObj instanceof Integer) {
            return ((Integer) projectIdObj).longValue();
        } else if (projectIdObj instanceof Long) {
            return (Long) projectIdObj;
        } else if (projectIdObj instanceof String) {
            try {
                return Long.parseLong((String) projectIdObj);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("无效的项目ID格式: " + projectIdObj);
            }
        }

        throw new IllegalArgumentException("无效的项目ID类型: " + projectIdObj.getClass().getName());
    }

}
