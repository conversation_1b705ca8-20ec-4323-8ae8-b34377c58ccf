package com.web.lowcode.controller;

import com.web.lowcode.common.Result;
import com.web.lowcode.security.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于测试Spring Security配置
 */
@Tag(name = "测试接口", description = "用于测试Spring Security配置")
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 公开接口，无需认证
     */
    @Operation(summary = "公开接口")
    @GetMapping("/public")
    public Result<Map<String, Object>> publicEndpoint() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "这是一个公开接口，无需认证");
        data.put("timestamp", System.currentTimeMillis());
        
        return Result.success(data);
    }
    
    /**
     * 需要认证的接口
     */
    @Operation(summary = "需要认证的接口")
    @GetMapping("/authenticated")
    @PreAuthorize("isAuthenticated()")
    public Result<Map<String, Object>> authenticatedEndpoint() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "这是一个需要认证的接口");
        data.put("timestamp", System.currentTimeMillis());
        
        // 获取当前用户名
        String username = SecurityUtils.getCurrentUsername().orElse("未知用户");
        data.put("username", username);
        
        return Result.success(data);
    }
    
    /**
     * 需要管理员角色的接口
     */
    @Operation(summary = "需要管理员角色的接口")
    @GetMapping("/admin")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Map<String, Object>> adminEndpoint() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "这是一个需要管理员角色的接口");
        data.put("timestamp", System.currentTimeMillis());
        
        // 获取当前用户名
        String username = SecurityUtils.getCurrentUsername().orElse("未知用户");
        data.put("username", username);
        
        return Result.success(data);
    }
    
    /**
     * 需要用户角色的接口
     */
    @Operation(summary = "需要用户角色的接口")
    @GetMapping("/user")
    @PreAuthorize("hasRole('USER')")
    public Result<Map<String, Object>> userEndpoint() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "这是一个需要用户角色的接口");
        data.put("timestamp", System.currentTimeMillis());
        
        // 获取当前用户名
        String username = SecurityUtils.getCurrentUsername().orElse("未知用户");
        data.put("username", username);
        
        return Result.success(data);
    }
}
