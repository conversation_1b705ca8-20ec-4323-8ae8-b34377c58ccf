package com.web.lowcode.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.common.PageResult;
import com.web.lowcode.common.Result;
import com.web.lowcode.entity.TemplateEntity;
import com.web.lowcode.service.TemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模板控制器
 */
@Tag(name = "模板管理", description = "模板相关接口")
@RestController
@RequestMapping("/api/templates")
public class TemplateController {
    
    @Autowired
    private TemplateService templateService;
    
    /**
     * 分页查询模板列表
     */
    @Operation(summary = "分页查询模板列表")
    @GetMapping("/page")
    public Result<PageResult<TemplateEntity>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "模板名称") @RequestParam(required = false) String name,
            @Parameter(description = "模板分类") @RequestParam(required = false) String category) {
        
        Page<TemplateEntity> page = new Page<>(current, size);
        LambdaQueryWrapper<TemplateEntity> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(name)) {
            wrapper.like(TemplateEntity::getName, name);
        }
        
        if (StringUtils.hasText(category)) {
            wrapper.eq(TemplateEntity::getCategory, category);
        }
        
        wrapper.orderByDesc(TemplateEntity::getCreateTime);
        
        Page<TemplateEntity> pageResult = templateService.page(page, wrapper);
        
        PageResult<TemplateEntity> result = new PageResult<>(
                pageResult.getTotal(),
                pageResult.getRecords(),
                pageResult.getCurrent(),
                pageResult.getSize()
        );
        
        return Result.success(result);
    }
    
    /**
     * 获取模板详情
     */
    @Operation(summary = "获取模板详情")
    @GetMapping("/{id}")
    public Result<TemplateEntity> getById(@Parameter(description = "模板ID") @PathVariable Long id) {
        TemplateEntity template = templateService.getById(id);
        return Result.success(template);
    }
    
    /**
     * 获取所有模板
     */
    @Operation(summary = "获取所有模板")
    @GetMapping
    public Result<List<TemplateEntity>> list(
            @Parameter(description = "模板分类") @RequestParam(required = false) String category) {
        
        LambdaQueryWrapper<TemplateEntity> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(category)) {
            wrapper.eq(TemplateEntity::getCategory, category);
        }
        
        wrapper.orderByAsc(TemplateEntity::getCategory).orderByDesc(TemplateEntity::getCreateTime);
        
        List<TemplateEntity> list = templateService.list(wrapper);
        return Result.success(list);
    }
    
    /**
     * 创建模板
     */
    @Operation(summary = "创建模板")
    @PostMapping
    public Result<TemplateEntity> create(@RequestBody TemplateEntity template) {
        // 模拟用户ID，实际应从登录用户获取
        Long userId = 1L;
        
        template.setCreatorId(userId);
        template.setIsSystem(0); // 非系统内置
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());
        
        templateService.save(template);
        
        return Result.success(template);
    }
    
    /**
     * 更新模板
     */
    @Operation(summary = "更新模板")
    @PutMapping("/{id}")
    public Result<TemplateEntity> update(
            @Parameter(description = "模板ID") @PathVariable Long id,
            @RequestBody TemplateEntity template) {
        
        template.setId(id);
        template.setUpdateTime(LocalDateTime.now());
        
        templateService.updateById(template);
        
        return Result.success(template);
    }
    
    /**
     * 删除模板
     */
    @Operation(summary = "删除模板")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@Parameter(description = "模板ID") @PathVariable Long id) {
        // 检查是否为系统内置模板
        TemplateEntity template = templateService.getById(id);
        if (template != null && template.getIsSystem() == 1) {
            return Result.error("系统内置模板不允许删除");
        }
        
        templateService.removeById(id);
        return Result.success();
    }
}
