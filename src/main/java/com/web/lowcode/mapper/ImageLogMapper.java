package com.web.lowcode.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.entity.ImageLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图片操作日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface ImageLogMapper extends BaseMapper<ImageLogEntity> {
    
    /**
     * 分页查询操作日志
     */
    IPage<ImageLogEntity> selectLogPage(Page<ImageLogEntity> page,
                                       @Param("imageId") Long imageId,
                                       @Param("operationType") String operationType,
                                       @Param("operatorId") Long operatorId,
                                       @Param("result") String result,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据图片ID查询操作日志
     */
    List<ImageLogEntity> selectByImageId(@Param("imageId") Long imageId);
    
    /**
     * 统计操作次数
     */
    Long countByOperationType(@Param("operationType") String operationType,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime);
    
    /**
     * 清理过期日志
     */
    int deleteExpiredLogs(@Param("expireTime") LocalDateTime expireTime);
}
