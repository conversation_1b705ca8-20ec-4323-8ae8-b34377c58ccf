package com.web.lowcode.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.web.lowcode.entity.ImageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图片管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface ImageMapper extends BaseMapper<ImageEntity> {
    
    /**
     * 分页查询图片列表
     */
    IPage<ImageEntity> selectImagePage(Page<ImageEntity> page, 
                                      @Param("category") Integer category,
                                      @Param("businessType") String businessType,
                                      @Param("uploaderId") Long uploaderId,
                                      @Param("status") Integer status,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      @Param("keyword") String keyword);
    
    /**
     * 根据MD5查询图片
     */
    ImageEntity selectByMd5(@Param("md5") String md5);
    
    /**
     * 根据业务信息查询图片列表
     */
    List<ImageEntity> selectByBusiness(@Param("businessType") String businessType, 
                                      @Param("businessId") Long businessId);
    
    /**
     * 统计图片数量
     */
    Long countByCategory(@Param("category") Integer category);
    
    /**
     * 统计存储大小
     */
    Long sumSizeByCategory(@Param("category") Integer category);
    
    /**
     * 批量更新状态
     */
    int updateStatusBatch(@Param("ids") List<Long> ids, 
                         @Param("status") Integer status,
                         @Param("updateTime") LocalDateTime updateTime);
}
