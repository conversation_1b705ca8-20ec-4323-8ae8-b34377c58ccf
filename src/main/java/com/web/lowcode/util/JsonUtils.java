package com.web.lowcode.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * JSON工具类
 */
public class JsonUtils {
    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将对象转换为JSON字符串
     *
     * @param obj 要转换的对象
     * @return JSON字符串
     */
    public static String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            logger.error("Convert object to JSON string failed", e);
            return null;
        }
    }

    /**
     * 将JSON字符串解析为Map对象
     *
     * @param jsonString JSON字符串
     * @return Map对象
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> parseObject(String jsonString) {
        try {
            if (jsonString == null || jsonString.isEmpty()) {
                return null;
            }
            return objectMapper.readValue(jsonString, Map.class);
        } catch (JsonProcessingException e) {
            logger.error("Parse JSON string to Map failed", e);
            return null;
        }
    }

    /**
     * 将JSON字符串解析为指定类型的对象
     *
     * @param jsonString JSON字符串
     * @param valueType 目标类型
     * @return 指定类型的对象
     */
    public static <T> T parseObject(String jsonString, Class<T> valueType) {
        try {
            if (jsonString == null || jsonString.isEmpty()) {
                return null;
            }
            return objectMapper.readValue(jsonString, valueType);
        } catch (JsonProcessingException e) {
            logger.error("Parse JSON string to object failed", e);
            return null;
        }
    }
}