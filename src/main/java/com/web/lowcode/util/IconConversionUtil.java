package com.web.lowcode.util;

import com.web.lowcode.entity.ComponentEntity;
import com.web.lowcode.entity.ComponentCategoryEntity;
import com.web.lowcode.service.ComponentCategoryService;
import com.web.lowcode.service.ComponentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图标转换工具类
 * 用于将组件和组件分类的图标转换为Base64格式
 */
@Configuration
@Order(3) // 在其他初始化完成后执行
public class IconConversionUtil implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(IconConversionUtil.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ComponentCategoryService componentCategoryService;

    @Autowired
    private ComponentService componentService;

    @Override
    public void run(String... args) throws Exception {
        try {
            // 执行数据库表结构修改
            executeSchemaChanges();
            
            // 转换组件分类图标
            convertComponentCategoryIcons();
            
            // 转换组件图标
            convertComponentIcons();
        } catch (Exception e) {
            logger.error("Failed to convert icons to base64", e);
        }
    }

    /**
     * 执行数据库表结构修改
     */
    private void executeSchemaChanges() {
        try {
            logger.info("Executing schema changes for icon columns...");
            
            // 修改组件分类表的icon列
            jdbcTemplate.execute("ALTER TABLE `component_category` MODIFY COLUMN `icon` TEXT DEFAULT NULL COMMENT '分类图标（支持Base64格式）'");
            
            // 修改组件表的icon列
            jdbcTemplate.execute("ALTER TABLE `component` MODIFY COLUMN `icon` TEXT DEFAULT NULL COMMENT '组件图标（支持Base64格式）'");
            
            logger.info("Schema changes executed successfully");
        } catch (Exception e) {
            logger.error("Failed to execute schema changes", e);
        }
    }

    /**
     * 转换组件分类图标为Base64格式
     */
    private void convertComponentCategoryIcons() {
        try {
            logger.info("Converting component category icons to base64...");
            
            List<ComponentCategoryEntity> categories = componentCategoryService.getAllCategories();
            int convertedCount = 0;
            
            for (ComponentCategoryEntity category : categories) {
                try {
                    String icon = category.getIcon();
                    if (icon != null && !icon.isEmpty() && !icon.startsWith("data:")) {
                        // 生成占位图标
                        String base64Icon = ImageUtil.generateColoredIconBase64(icon, 24, "#409EFF");
                        
                        category.setIcon(base64Icon);
                        category.setUpdateTime(LocalDateTime.now());
                        
                        componentCategoryService.updateById(category);
                        convertedCount++;
                    }
                } catch (Exception e) {
                    logger.error("Failed to convert icon for category: " + category.getName(), e);
                }
            }
            
            logger.info("Converted {} of {} component category icons", convertedCount, categories.size());
        } catch (Exception e) {
            logger.error("Failed to convert component category icons", e);
        }
    }

    /**
     * 转换组件图标为Base64格式
     */
    private void convertComponentIcons() {
        try {
            logger.info("Converting component icons to base64...");
            
            List<ComponentEntity> components = componentService.list();
            int convertedCount = 0;
            
            for (ComponentEntity component : components) {
                try {
                    String icon = component.getIcon();
                    if (icon != null && !icon.isEmpty() && !icon.startsWith("data:")) {
                        // 生成占位图标
                        String base64Icon = ImageUtil.generateColoredIconBase64(icon, 24, "#409EFF");
                        
                        component.setIcon(base64Icon);
                        component.setUpdateTime(LocalDateTime.now());
                        
                        componentService.updateById(component);
                        convertedCount++;
                    }
                } catch (Exception e) {
                    logger.error("Failed to convert icon for component: " + component.getName(), e);
                }
            }
            
            logger.info("Converted {} of {} component icons", convertedCount, components.size());
        } catch (Exception e) {
            logger.error("Failed to convert component icons", e);
        }
    }
}
