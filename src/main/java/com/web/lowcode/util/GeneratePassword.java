package com.web.lowcode.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 生成密码哈希
 */
public class GeneratePassword {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "123456";
        String encodedPassword = encoder.encode(password);
        
        System.out.println("密码: " + password);
        System.out.println("加密后: " + encodedPassword);
        
        // 验证
        boolean matches = encoder.matches(password, encodedPassword);
        System.out.println("验证结果: " + matches);
        
        // 验证已知哈希
        String knownHash = "$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG";
        boolean knownMatches = encoder.matches(password, knownHash);
        System.out.println("已知哈希验证结果: " + knownMatches);
        
        // 如果已知哈希不匹配，尝试其他可能的密码
        if (!knownMatches) {
            String[] possiblePasswords = {"admin", "password", "test", "123456789", "111111", "000000"};
            for (String pwd : possiblePasswords) {
                boolean match = encoder.matches(pwd, knownHash);
                if (match) {
                    System.out.println("找到匹配的密码: " + pwd);
                    break;
                }
            }
        }
    }
}
