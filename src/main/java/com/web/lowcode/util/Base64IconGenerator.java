package com.web.lowcode.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * Base64 图标生成器
 * 用于生成基于 Element Plus 图标的 Base64 格式图标
 */
public class Base64IconGenerator {

    private static final Logger logger = LoggerFactory.getLogger(Base64IconGenerator.class);
    
    // 图标颜色映射
    private static final Map<String, Color> ICON_COLORS = new HashMap<>();
    
    static {
        // 初始化图标颜色
        ICON_COLORS.put("basic", new Color(64, 158, 255)); // 蓝色 #409EFF
        ICON_COLORS.put("form", new Color(103, 194, 58));  // 绿色 #67C23A
        ICON_COLORS.put("layout", new Color(230, 162, 60)); // 黄色 #E6A23C
        ICON_COLORS.put("advanced", new Color(245, 108, 108)); // 红色 #F56C6C
        ICON_COLORS.put("data", new Color(144, 147, 153));  // 灰色 #909399
        ICON_COLORS.put("flow", new Color(121, 187, 255)); // 浅蓝色 #79BBFF
        ICON_COLORS.put("icon", new Color(255, 214, 102)); // 浅黄色 #FFD666
        ICON_COLORS.put("custom", new Color(179, 216, 255)); // 极浅蓝色 #B3D8FF
    }
    
    /**
     * 生成基于图标名称的 Base64 格式图标
     *
     * @param iconName 图标名称
     * @param category 组件分类
     * @return Base64 格式的图标
     */
    public static String generateIconBase64(String iconName, String category) {
        try {
            // 获取分类对应的颜色，如果没有则使用默认蓝色
            Color color = ICON_COLORS.getOrDefault(category, new Color(64, 158, 255));
            
            // 创建图像
            int size = 24;
            BufferedImage image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = image.createGraphics();
            
            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            
            // 绘制背景（圆形）
            g2d.setColor(new Color(color.getRed(), color.getGreen(), color.getBlue(), 50));
            g2d.fillOval(0, 0, size, size);
            
            // 绘制图标（简化为首字母）
            g2d.setColor(color);
            g2d.setFont(new Font("Arial", Font.BOLD, size / 2));
            FontMetrics fm = g2d.getFontMetrics();
            String text = iconName.length() > 0 ? iconName.substring(0, 1).toUpperCase() : "?";
            int textWidth = fm.stringWidth(text);
            int textHeight = fm.getHeight();
            g2d.drawString(text, (size - textWidth) / 2, (size + textHeight / 3) / 2);
            
            g2d.dispose();
            
            // 转换为 Base64
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            byte[] bytes = outputStream.toByteArray();
            
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            logger.error("Failed to generate icon base64 for " + iconName, e);
            return null;
        }
    }
    
    /**
     * 为组件分类生成 Base64 格式图标
     *
     * @param categoryCode 分类代码
     * @param categoryName 分类名称
     * @return Base64 格式的图标
     */
    public static String generateCategoryIconBase64(String categoryCode, String categoryName) {
        try {
            // 获取分类对应的颜色，如果没有则使用默认蓝色
            Color color = ICON_COLORS.getOrDefault(categoryCode, new Color(64, 158, 255));
            
            // 创建图像
            int size = 32;
            BufferedImage image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = image.createGraphics();
            
            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            
            // 绘制背景（圆角矩形）
            g2d.setColor(new Color(color.getRed(), color.getGreen(), color.getBlue(), 50));
            g2d.fillRoundRect(0, 0, size, size, 8, 8);
            
            // 绘制边框
            g2d.setColor(color);
            g2d.setStroke(new BasicStroke(2));
            g2d.drawRoundRect(1, 1, size - 2, size - 2, 8, 8);
            
            // 绘制图标（简化为首字母）
            g2d.setColor(color);
            g2d.setFont(new Font("Arial", Font.BOLD, size / 2));
            FontMetrics fm = g2d.getFontMetrics();
            String text = categoryName.length() > 0 ? categoryName.substring(0, 1) : "?";
            int textWidth = fm.stringWidth(text);
            int textHeight = fm.getHeight();
            g2d.drawString(text, (size - textWidth) / 2, (size + textHeight / 3) / 2);
            
            g2d.dispose();
            
            // 转换为 Base64
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            byte[] bytes = outputStream.toByteArray();
            
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            logger.error("Failed to generate category icon base64 for " + categoryCode, e);
            return null;
        }
    }
}
