package com.web.lowcode.util;

import com.web.lowcode.entity.UserEntity;
import com.web.lowcode.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 创建用户工具类
 * 在应用启动时自动创建测试用户
 */
@Component
public class CreateUserUtil implements CommandLineRunner {

    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public void run(String... args) throws Exception {
        createUserIfNotExists("admin", "123456", "管理员", "ROLE_ADMIN");
        createUserIfNotExists("test", "123456", "测试用户", "ROLE_USER");
        createUserIfNotExists("dev", "123456", "开发者", "ROLE_USER");
        createUserIfNotExists("designer", "123456", "设计师", "ROLE_USER");
    }
    
    /**
     * 如果用户不存在，则创建用户
     */
    private void createUserIfNotExists(String username, String password, String nickname, String role) {
        UserEntity existingUser = userService.getByUsername(username);
        
        if (existingUser == null) {
            System.out.println("Creating user: " + username);
            
            UserEntity user = new UserEntity();
            user.setUsername(username);
            user.setPassword(passwordEncoder.encode(password));
            user.setNickname(nickname);
            user.setStatus(1);
            user.setRole(role);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            userService.save(user);
            
            System.out.println("User created: " + username);
        } else {
            System.out.println("User already exists: " + username);
            
            // 更新密码
            UserEntity user = new UserEntity();
            user.setId(existingUser.getId());
            user.setPassword(passwordEncoder.encode(password));
            user.setUpdateTime(LocalDateTime.now());
            
            userService.updateById(user);
            
            System.out.println("User password updated: " + username);
        }
    }
}
