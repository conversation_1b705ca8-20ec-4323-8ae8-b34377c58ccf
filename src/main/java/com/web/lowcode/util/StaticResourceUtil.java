package com.web.lowcode.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;

/**
 * 静态资源工具类
 * 用于读取静态资源文件
 */
public class StaticResourceUtil {
    private static final Logger logger = LoggerFactory.getLogger(StaticResourceUtil.class);

    // 默认资源映射
    private static final HashMap<String, byte[]> DEFAULT_RESOURCES = new HashMap<>();

    static {
        // 默认favicon.ico（一个简单的图标）
        DEFAULT_RESOURCES.put("favicon.ico", Base64.getDecoder().decode(
                "AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABILAAASCwAAAAAA" +
                "AAAAAAD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD/" +
                "//8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A" +
                "////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////" +
                "AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//" +
                "/wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD/" +
                "//8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A" +
                "////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////" +
                "AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//" +
                "/wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD/" +
                "//8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A" +
                "////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////" +
                "AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//" +
                "/wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD/" +
                "//8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A" +
                "////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////" +
                "AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//" +
                "/wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD/" +
                "//8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A" +
                "////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////" +
                "AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//" +
                "/wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD/" +
                "//8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A" +
                "////AP///wD///8A////AP///wA="));

        // 默认logo.png（一个简单的图标）
        DEFAULT_RESOURCES.put("logo.png", Base64.getDecoder().decode(
                "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAB" +
                "NmlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjarY6xSsNQFEDPi6LiUCsEcXB4kygotupgxqQt" +
                "RRCs1SHJ1qShSmkSXl7VfoSjWwcXd7/AyVFwUPwC/0Bx6uAQIYODCJ7p3MPlcsGo2HWnYZRhEGvV" +
                "bjrS9Xw5+8QMUwDQCbPUbrUOAOIkjvjB5ysC4HnTrjsN/sZ8mCoNTIDtbpSFICpA/0KnGsQYMIN+" +
                "qkHcAaY6addAPAClXu4vQCnI/Q0oKdfzQXwAZs/1fDDmADPIfQUwdXSpAWpJOlJnvVMtq5ZlSbub" +
                "BJiKUsdLPIV7jyzYrEtnYmn4LoLQlTmabEg+Y/2IxfG4FOvKFHPc+XqCMyQGu7Aqom7UzOD2QQ/5" +
                "l1/caCqTXBfOAAAAAElFTkSuQmCC"));
    }

    /**
     * 从静态资源目录读取文件内容
     * @param path 文件路径，相对于 static 目录
     * @return 文件内容的字符串表示
     */
    public static String readFileAsString(String path) {
        try {
            ClassPathResource resource = new ClassPathResource("static/" + path);
            byte[] bytes = FileCopyUtils.copyToByteArray(resource.getInputStream());
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("读取静态资源文件失败: {}", path, e);
            return "";
        }
    }

    /**
     * 从静态资源目录读取二进制文件内容
     * @param path 文件路径，相对于 static 目录
     * @return 文件内容的字节数组
     */
    public static byte[] readFileAsBytes(String path) {
        try {
            ClassPathResource resource = new ClassPathResource("static/" + path);
            return FileCopyUtils.copyToByteArray(resource.getInputStream());
        } catch (IOException e) {
            logger.warn("从静态资源目录读取文件失败: {}，尝试使用默认资源", path);

            // 尝试使用默认资源
            String fileName = path;
            int lastSlashIndex = path.lastIndexOf('/');
            if (lastSlashIndex >= 0) {
                fileName = path.substring(lastSlashIndex + 1);
            }

            byte[] defaultResource = DEFAULT_RESOURCES.get(fileName);
            if (defaultResource != null) {
                logger.info("使用默认资源: {}", fileName);
                return defaultResource;
            }

            logger.error("没有找到默认资源: {}", fileName);
            return new byte[0];
        }
    }

    /**
     * 从静态资源目录读取二进制文件内容，并转换为Base64编码的字符串
     * @param path 文件路径，相对于 static 目录
     * @return 文件内容的Base64编码字符串
     */
    public static String readFileAsBase64(String path) {
        byte[] bytes = readFileAsBytes(path);
        if (bytes.length == 0) {
            return "";
        }
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 检查静态资源文件是否存在
     * @param path 文件路径，相对于 static 目录
     * @return 文件是否存在
     */
    public static boolean fileExists(String path) {
        try {
            ClassPathResource resource = new ClassPathResource("static/" + path);
            try (InputStream inputStream = resource.getInputStream()) {
                return true;
            }
        } catch (IOException e) {
            return false;
        }
    }
}
