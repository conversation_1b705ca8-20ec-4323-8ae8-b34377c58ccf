package com.web.lowcode.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ZIP文件工具类
 * 用于创建ZIP文件
 */
public class ZipFileUtil {

    private static final Logger logger = LoggerFactory.getLogger(ZipFileUtil.class);

    /**
     * 创建ZIP文件
     *
     * @param files 文件内容映射（文件路径 -> 文件内容）
     * @return ZIP文件的字节数组
     */
    public static byte[] createZip(Map<String, String> files) {
        logger.info("Creating ZIP file with {} files", files.size());
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {

            // 添加文件到ZIP
            for (Map.Entry<String, String> entry : files.entrySet()) {
                String filePath = entry.getKey();
                String content = entry.getValue();

                // 创建ZIP条目
                ZipEntry zipEntry = new ZipEntry(filePath);
                zos.putNextEntry(zipEntry);

                // 检查是否是Base64编码的二进制文件
                if (isBinaryFile(filePath) && isBase64Content(content)) {
                    // 解码Base64并写入二进制内容
                    byte[] binaryData = Base64.getDecoder().decode(content);
                    zos.write(binaryData);
                } else {
                    // 写入文本内容
                    zos.write(content.getBytes(StandardCharsets.UTF_8));
                }
                zos.closeEntry();
            }

            // 完成ZIP文件
            zos.finish();
            zos.flush();

            return baos.toByteArray();
        } catch (Exception e) {
            logger.error("Failed to create ZIP file", e);
            throw new RuntimeException("Failed to create ZIP file", e);
        }
    }

    /**
     * 创建ZIP文件，支持二进制文件
     *
     * @param textFiles 文本文件映射（文件路径 -> 文件内容）
     * @param binaryFiles 二进制文件映射（文件路径 -> 文件内容）
     * @return ZIP文件的字节数组
     */
    public static byte[] createZip(Map<String, String> textFiles, Map<String, byte[]> binaryFiles) {
        int totalFiles = textFiles.size() + (binaryFiles != null ? binaryFiles.size() : 0);
        logger.info("Creating ZIP file with {} files ({} text, {} binary)",
                totalFiles, textFiles.size(), binaryFiles != null ? binaryFiles.size() : 0);

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {

            // 添加文本文件到ZIP
            for (Map.Entry<String, String> entry : textFiles.entrySet()) {
                String filePath = entry.getKey();
                String content = entry.getValue();

                // 创建ZIP条目
                ZipEntry zipEntry = new ZipEntry(filePath);
                zos.putNextEntry(zipEntry);

                // 写入文本内容
                zos.write(content.getBytes(StandardCharsets.UTF_8));
                zos.closeEntry();
            }

            // 添加二进制文件到ZIP
            if (binaryFiles != null) {
                for (Map.Entry<String, byte[]> entry : binaryFiles.entrySet()) {
                    String filePath = entry.getKey();
                    byte[] content = entry.getValue();

                    // 创建ZIP条目
                    ZipEntry zipEntry = new ZipEntry(filePath);
                    zos.putNextEntry(zipEntry);

                    // 写入二进制内容
                    zos.write(content);
                    zos.closeEntry();
                }
            }

            // 完成ZIP文件
            zos.finish();
            zos.flush();

            return baos.toByteArray();
        } catch (Exception e) {
            logger.error("Failed to create ZIP file", e);
            throw new RuntimeException("Failed to create ZIP file", e);
        }
    }

    /**
     * 检查文件是否为二进制文件
     * @param filePath 文件路径
     * @return 是否为二进制文件
     */
    private static boolean isBinaryFile(String filePath) {
        String lowerPath = filePath.toLowerCase();
        return lowerPath.endsWith(".ico") ||
               lowerPath.endsWith(".png") ||
               lowerPath.endsWith(".jpg") ||
               lowerPath.endsWith(".jpeg") ||
               lowerPath.endsWith(".gif") ||
               lowerPath.endsWith(".bmp") ||
               lowerPath.endsWith(".pdf") ||
               lowerPath.endsWith(".zip") ||
               lowerPath.endsWith(".jar");
    }

    /**
     * 检查内容是否为Base64编码
     * @param content 内容字符串
     * @return 是否为Base64编码
     */
    private static boolean isBase64Content(String content) {
        // 简单检查是否符合Base64编码格式
        if (content == null || content.isEmpty()) {
            return false;
        }

        // 检查是否只包含Base64字符集
        return content.matches("^[A-Za-z0-9+/=]+$");
    }
}
