package com.web.lowcode.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码测试工具类
 */
public class PasswordTest {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // 测试密码
        String rawPassword = "123456";
        
        // 生成新的加密密码
        String encodedPassword = encoder.encode(rawPassword);
        System.out.println("新生成的加密密码: " + encodedPassword);
        
        // 验证数据库中的密码
        String dbPassword = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi";
        boolean matches = encoder.matches(rawPassword, dbPassword);
        System.out.println("数据库密码是否匹配123456: " + matches);
        
        // 如果不匹配，尝试其他可能的密码
        if (!matches) {
            String[] possiblePasswords = {"admin", "password", "test", "123456789"};
            for (String pwd : possiblePasswords) {
                boolean match = encoder.matches(pwd, dbPassword);
                if (match) {
                    System.out.println("找到匹配的密码: " + pwd);
                    break;
                }
            }
        }
    }
}
