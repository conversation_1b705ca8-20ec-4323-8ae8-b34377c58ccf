package com.web.lowcode.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码编码工具类
 * 用于生成BCrypt加密的密码
 */
public class PasswordEncoderUtil {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "123456";
        String encodedPassword = encoder.encode(password);
        
        System.out.println("原始密码: " + password);
        System.out.println("加密后的密码: " + encodedPassword);
        
        // 验证已有的密码是否为123456
        String existingEncodedPassword = "$2a$10$YCJ5Ky5QJWYxWU1UjPYX8.8gBR8/LKI3kqOmbTOoOb7ABFj0ML6Oe";
        boolean matches = encoder.matches(password, existingEncodedPassword);
        
        System.out.println("已有密码是否匹配123456: " + matches);
    }
}
