package com.web.lowcode.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

/**
 * 图片处理工具类
 */
public class ImageUtil {

    private static final Logger logger = LoggerFactory.getLogger(ImageUtil.class);

    /**
     * 将图片转换为Base64编码
     *
     * @param imageBytes 图片字节数组
     * @param formatName 图片格式（如 "png", "jpg"）
     * @return Base64编码的字符串
     */
    public static String imageToBase64(byte[] imageBytes, String formatName) {
        try {
            // 读取图片
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
            if (image == null) {
                logger.error("Failed to read image from byte array");
                return null;
            }

            // 转换为Base64
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, formatName, outputStream);
            byte[] bytes = outputStream.toByteArray();
            
            // 返回Base64编码
            return "data:image/" + formatName + ";base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            logger.error("Failed to convert image to Base64", e);
            return null;
        }
    }

    /**
     * 将Base64编码转换为图片字节数组
     *
     * @param base64 Base64编码的字符串
     * @return 图片字节数组
     */
    public static byte[] base64ToImage(String base64) {
        try {
            // 移除前缀（如果有）
            if (base64.contains(",")) {
                base64 = base64.split(",")[1];
            }
            
            // 解码Base64
            return Base64.getDecoder().decode(base64);
        } catch (Exception e) {
            logger.error("Failed to convert Base64 to image", e);
            return null;
        }
    }

    /**
     * 生成纯色图标的PNG图像并转换为Base64
     *
     * @param iconName 图标名称
     * @param size     图标大小
     * @param color    图标颜色（十六进制，如 "#409EFF"）
     * @return Base64编码的PNG图像
     */
    public static String generateColoredIconBase64(String iconName, int size, String color) {
        try {
            // 创建图像
            BufferedImage image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = image.createGraphics();
            
            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            
            // 解析颜色
            Color iconColor = Color.decode(color);
            g2d.setColor(iconColor);
            
            // 绘制简单图形（作为占位符）
            int padding = size / 4;
            g2d.fillRect(padding, padding, size - 2 * padding, size - 2 * padding);
            
            // 添加文字（图标名称的首字母）
            g2d.setColor(Color.WHITE);
            g2d.setFont(new Font("Arial", Font.BOLD, size / 2));
            FontMetrics fm = g2d.getFontMetrics();
            String text = iconName.substring(0, 1).toUpperCase();
            int textWidth = fm.stringWidth(text);
            int textHeight = fm.getHeight();
            g2d.drawString(text, (size - textWidth) / 2, (size + textHeight / 2) / 2);
            
            g2d.dispose();
            
            // 转换为Base64
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            byte[] bytes = outputStream.toByteArray();
            
            // 返回Base64编码
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            logger.error("Failed to generate colored icon", e);
            return null;
        }
    }

    /**
     * 从SVG字符串生成PNG图像并转换为Base64
     *
     * @param svgContent SVG内容
     * @param size       图像大小
     * @return Base64编码的PNG图像
     */
    public static String svgToBase64Png(String svgContent, int size) {
        // 注意：此方法需要额外的依赖库如Batik来实现
        // 由于项目可能没有这些依赖，这里提供一个简单的占位符实现
        logger.warn("SVG to PNG conversion not fully implemented - using placeholder");
        return generateColoredIconBase64("Icon", size, "#409EFF");
    }
}
