package com.web.lowcode.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.web.lowcode.entity.ImageEntity;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图片管理服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ImageService extends IService<ImageEntity> {
    
    /**
     * 上传图片
     */
    ImageEntity uploadImage(MultipartFile file, Integer category, String businessType, Long businessId, String remark);
    
    /**
     * 批量上传图片
     */
    List<ImageEntity> uploadImages(MultipartFile[] files, Integer category, String businessType, Long businessId, String remark);
    
    /**
     * 删除图片
     */
    boolean deleteImage(Long imageId);
    
    /**
     * 批量删除图片
     */
    boolean deleteImages(List<Long> imageIds);
    
    /**
     * 更新图片信息
     */
    boolean updateImage(ImageEntity image);
    
    /**
     * 根据ID获取图片信息
     */
    ImageEntity getImageById(Long imageId);
    
    /**
     * 分页查询图片列表
     */
    IPage<ImageEntity> getImagePage(int current, int size, Integer category, String businessType, 
                                   Long uploaderId, Integer status, LocalDateTime startTime, 
                                   LocalDateTime endTime, String keyword);
    
    /**
     * 根据业务信息查询图片列表
     */
    List<ImageEntity> getImagesByBusiness(String businessType, Long businessId);
    
    /**
     * 根据MD5查询图片（用于去重）
     */
    ImageEntity getImageByMd5(String md5);
    
    /**
     * 获取图片统计信息
     */
    Map<String, Object> getImageStatistics();
    
    /**
     * 获取分类统计信息
     */
    Map<String, Object> getCategoryStatistics(Integer category);
    
    /**
     * 检查图片是否存在
     */
    boolean imageExists(Long imageId);
    
    /**
     * 复制图片
     */
    ImageEntity copyImage(Long sourceImageId, String businessType, Long businessId, String remark);
    
    /**
     * 恢复已删除的图片
     */
    boolean restoreImage(Long imageId);
    
    /**
     * 永久删除图片
     */
    boolean permanentDeleteImage(Long imageId);
    
    /**
     * 清理过期的已删除图片
     */
    int cleanExpiredImages(int days);
}
