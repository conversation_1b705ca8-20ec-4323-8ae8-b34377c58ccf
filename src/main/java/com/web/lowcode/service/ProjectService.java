package com.web.lowcode.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.web.lowcode.entity.ProjectEntity;

/**
 * 项目服务接口
 */
public interface ProjectService extends IService<ProjectEntity> {
    
    /**
     * 分页查询用户项目列表
     *
     * @param page      分页参数
     * @param userId    用户ID
     * @param name      项目名称（可选）
     * @return 分页结果
     */
    Page<ProjectEntity> pageByUser(Page<ProjectEntity> page, Long userId, String name);
}
