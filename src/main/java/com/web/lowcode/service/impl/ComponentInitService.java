package com.web.lowcode.service.impl;

import com.web.lowcode.service.ComponentService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

/**
 * 组件初始化服务
 */
@Service
public class ComponentInitService {
    
    private static final Logger logger = LoggerFactory.getLogger(ComponentInitService.class);
    
    @Autowired
    private ComponentService componentService;
    
    @Autowired
    private DataSource dataSource;
    
    /**
     * 初始化组件数据
     */
    @PostConstruct
    public void initComponents() {
        try {
            // 检查是否已有组件数据
            long count = componentService.count();
            
            if (count == 0) {
                logger.info("No components found, initializing default components...");
                
                // 执行SQL脚本初始化组件数据
                ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
                populator.addScript(new ClassPathResource("db/component_init.sql"));
                populator.execute(dataSource);
                
                logger.info("Default components initialized successfully");
            } else {
                logger.info("Components already exist, skipping initialization");
            }
        } catch (Exception e) {
            logger.error("Failed to initialize components", e);
        }
    }
}
