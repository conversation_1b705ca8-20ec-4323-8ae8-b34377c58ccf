package com.web.lowcode.service.impl;

import com.web.lowcode.entity.ComponentCategoryEntity;
import com.web.lowcode.service.ComponentCategoryService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 组件分类初始化服务
 */
@Service
public class ComponentCategoryInitService {
    
    private static final Logger logger = LoggerFactory.getLogger(ComponentCategoryInitService.class);
    
    @Autowired
    private ComponentCategoryService componentCategoryService;
    
    @Autowired
    private DataSource dataSource;
    
    /**
     * 初始化组件分类数据
     */
    @PostConstruct
    public void initComponentCategories() {
        try {
            // 检查是否已有分类数据
            long count = componentCategoryService.count();
            
            if (count == 0) {
                logger.info("No component categories found, initializing default categories...");
                
                try (Connection connection = dataSource.getConnection()) {
                    // 执行SQL脚本初始化组件分类数据
                    ScriptUtils.executeSqlScript(connection, new ClassPathResource("db/component_category_init.sql"));
                    logger.info("Default component categories initialized successfully from SQL script");
                } catch (Exception e) {
                    logger.error("Failed to execute SQL script, falling back to programmatic initialization", e);
                    
                    // 如果SQL脚本执行失败，使用编程方式初始化
                    List<ComponentCategoryEntity> defaultCategories = new ArrayList<>();
                    
                    // 基础组件
                    defaultCategories.add(ComponentCategoryEntity.builder()
                            .code("basic")
                            .name("基础组件")
                            .icon("Document")
                            .sort(1)
                            .isSystem(1)
                            .creatorId(1L)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build());
                    
                    // 表单组件
                    defaultCategories.add(ComponentCategoryEntity.builder()
                            .code("form")
                            .name("表单组件")
                            .icon("Edit")
                            .sort(2)
                            .isSystem(1)
                            .creatorId(1L)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build());
                    
                    // 布局组件
                    defaultCategories.add(ComponentCategoryEntity.builder()
                            .code("layout")
                            .name("布局组件")
                            .icon("Grid")
                            .sort(3)
                            .isSystem(1)
                            .creatorId(1L)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build());
                    
                    // 高级组件
                    defaultCategories.add(ComponentCategoryEntity.builder()
                            .code("advanced")
                            .name("高级组件")
                            .icon("Star")
                            .sort(4)
                            .isSystem(1)
                            .creatorId(1L)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build());
                    
                    // 流程组件
                    defaultCategories.add(ComponentCategoryEntity.builder()
                            .code("flow")
                            .name("流程组件")
                            .icon("Connection")
                            .sort(5)
                            .isSystem(1)
                            .creatorId(1L)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build());
                    
                    // 图标组件
                    defaultCategories.add(ComponentCategoryEntity.builder()
                            .code("icon")
                            .name("图标组件")
                            .icon("PictureRounded")
                            .sort(6)
                            .isSystem(1)
                            .creatorId(1L)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build());
                    
                    // 自定义组件
                    defaultCategories.add(ComponentCategoryEntity.builder()
                            .code("custom")
                            .name("自定义组件")
                            .icon("SetUp")
                            .sort(7)
                            .isSystem(1)
                            .creatorId(1L)
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build());
                    
                    // 保存默认分类
                    componentCategoryService.saveBatch(defaultCategories);
                    logger.info("Default component categories initialized successfully programmatically");
                }
            } else {
                logger.info("Component categories already exist, skipping initialization");
            }
        } catch (Exception e) {
            logger.error("Failed to initialize component categories", e);
        }
    }
}
