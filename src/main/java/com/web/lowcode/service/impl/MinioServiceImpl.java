package com.web.lowcode.service.impl;

import com.web.lowcode.config.MinioConfig;
import com.web.lowcode.service.MinioService;
import io.minio.*;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MinIO服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MinioServiceImpl implements MinioService {
    
    private final MinioClient minioClient;
    private final MinioConfig.MinioProperties minioProperties;
    
    /**
     * 初始化存储桶
     */
    @PostConstruct
    public void initBucket() {
        try {
            String bucketName = minioProperties.getBucketName();
            if (!bucketExists(bucketName)) {
                createBucket(bucketName);
                log.info("MinIO存储桶创建成功: {}", bucketName);
            } else {
                log.info("MinIO存储桶已存在: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("初始化MinIO存储桶失败", e);
        }
    }
    
    @Override
    public void createBucket(String bucketName) {
        try {
            minioClient.makeBucket(MakeBucketArgs.builder()
                    .bucket(bucketName)
                    .build());
            log.info("创建存储桶成功: {}", bucketName);
        } catch (Exception e) {
            log.error("创建存储桶失败: {}", bucketName, e);
            throw new RuntimeException("创建存储桶失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean bucketExists(String bucketName) {
        try {
            return minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败: {}", bucketName, e);
            return false;
        }
    }
    
    @Override
    public String uploadFile(MultipartFile file, String objectName) {
        try {
            String bucketName = minioProperties.getBucketName();
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build());
            
            log.info("文件上传成功: {}", objectName);
            return getFileUrl(objectName);
        } catch (Exception e) {
            log.error("文件上传失败: {}", objectName, e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public String uploadFile(InputStream inputStream, String objectName, String contentType, long size) {
        try {
            String bucketName = minioProperties.getBucketName();
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(inputStream, size, -1)
                    .contentType(contentType)
                    .build());
            
            log.info("文件流上传成功: {}", objectName);
            return getFileUrl(objectName);
        } catch (Exception e) {
            log.error("文件流上传失败: {}", objectName, e);
            throw new RuntimeException("文件流上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public InputStream downloadFile(String objectName) {
        try {
            String bucketName = minioProperties.getBucketName();
            return minioClient.getObject(GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
        } catch (Exception e) {
            log.error("文件下载失败: {}", objectName, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }
    
    @Override
    public void deleteFile(String objectName) {
        try {
            String bucketName = minioProperties.getBucketName();
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
            log.info("文件删除成功: {}", objectName);
        } catch (Exception e) {
            log.error("文件删除失败: {}", objectName, e);
            throw new RuntimeException("文件删除失败: " + e.getMessage());
        }
    }
    
    @Override
    public void deleteFiles(String[] objectNames) {
        // 实现批量删除逻辑
        for (String objectName : objectNames) {
            deleteFile(objectName);
        }
    }
    
    @Override
    public String getFileUrl(String objectName) {
        return minioProperties.getEndpoint() + "/" + minioProperties.getBucketName() + "/" + objectName;
    }
    
    @Override
    public String getPresignedUrl(String objectName, int expiry) {
        try {
            String bucketName = minioProperties.getBucketName();
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expiry, TimeUnit.SECONDS)
                    .build());
        } catch (Exception e) {
            log.error("获取预签名URL失败: {}", objectName, e);
            throw new RuntimeException("获取预签名URL失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> getFileInfo(String objectName) {
        try {
            String bucketName = minioProperties.getBucketName();
            StatObjectResponse stat = minioClient.statObject(StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
            
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("objectName", objectName);
            fileInfo.put("size", stat.size());
            fileInfo.put("contentType", stat.contentType());
            fileInfo.put("lastModified", stat.lastModified());
            fileInfo.put("etag", stat.etag());
            
            return fileInfo;
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", objectName, e);
            throw new RuntimeException("获取文件信息失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean fileExists(String objectName) {
        try {
            getFileInfo(objectName);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public void copyFile(String sourceObjectName, String targetObjectName) {
        try {
            String bucketName = minioProperties.getBucketName();
            minioClient.copyObject(CopyObjectArgs.builder()
                    .bucket(bucketName)
                    .object(targetObjectName)
                    .source(CopySource.builder()
                            .bucket(bucketName)
                            .object(sourceObjectName)
                            .build())
                    .build());
            log.info("文件复制成功: {} -> {}", sourceObjectName, targetObjectName);
        } catch (Exception e) {
            log.error("文件复制失败: {} -> {}", sourceObjectName, targetObjectName, e);
            throw new RuntimeException("文件复制失败: " + e.getMessage());
        }
    }
}
