package com.web.lowcode.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

/**
 * 数据库初始化服务
 * 用于执行数据库初始化脚本
 */
@Service
@Order(1) // 在其他初始化之前执行
public class DatabaseInitService implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitService.class);

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        try {
            logger.info("Initializing database schema...");
            
            // 执行组件启用字段添加脚本
            executeScript("db/alter_component_enabled.sql");
            
            logger.info("Database schema initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize database schema", e);
        }
    }
    
    /**
     * 执行SQL脚本
     *
     * @param scriptPath 脚本路径
     */
    private void executeScript(String scriptPath) {
        try {
            logger.info("Executing SQL script: {}", scriptPath);
            
            ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
            populator.addScript(new ClassPathResource(scriptPath));
            populator.setContinueOnError(true); // 忽略错误继续执行
            populator.execute(dataSource);
            
            logger.info("SQL script executed successfully: {}", scriptPath);
        } catch (Exception e) {
            logger.error("Failed to execute SQL script: " + scriptPath, e);
        }
    }
}
