package com.web.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.web.lowcode.entity.ComponentEntity;
import com.web.lowcode.mapper.ComponentMapper;
import com.web.lowcode.service.ComponentService;
import org.springframework.stereotype.Service;

/**
 * 组件服务实现类
 */
@Service
public class ComponentServiceImpl extends ServiceImpl<ComponentMapper, ComponentEntity> implements ComponentService {

}
