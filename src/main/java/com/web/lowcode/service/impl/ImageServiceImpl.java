package com.web.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.web.lowcode.config.MinioConfig;
import com.web.lowcode.entity.ImageEntity;
import com.web.lowcode.mapper.ImageMapper;
import com.web.lowcode.service.ImageLogService;
import com.web.lowcode.service.ImageService;
import com.web.lowcode.service.MinioService;
import com.web.lowcode.service.FileStorageService;
import com.web.lowcode.security.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 图片管理服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageServiceImpl extends ServiceImpl<ImageMapper, ImageEntity> implements ImageService {
    
    private final MinioService minioService;
    private final ImageLogService imageLogService;
    private final MinioConfig.MinioProperties minioProperties;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImageEntity uploadImage(MultipartFile file, Integer category, String businessType, Long businessId, String remark) {
        try {
            // 参数验证
            validateUploadParams(file, category);
            
            // 计算文件MD5
            String md5 = calculateMD5(file);
            
            // 检查是否已存在相同文件
            ImageEntity existingImage = getImageByMd5(md5);
            if (existingImage != null && existingImage.getStatus() == 0) {
                log.info("文件已存在，返回现有记录: {}", existingImage.getId());
                // 记录操作日志
                imageLogService.recordLog(existingImage.getId(), "VIEW", "获取已存在的图片", null, null, "SUCCESS", null);
                return existingImage;
            }
            
            // 生成文件名和路径
            String originalName = file.getOriginalFilename();
            String extension = getFileExtension(originalName);
            String fileName = generateFileName(extension);
            String minioPath = generateMinioPath(category, fileName);
            
            // 上传到MinIO
            log.info("开始上传文件到MinIO: {}", minioPath);
            String url = minioService.uploadFile(file, minioPath);
            log.info("MinIO上传成功，生成URL: {}", url);

            // 获取图片尺寸
            int[] dimensions = getImageDimensions(file);
            log.info("图片尺寸: {}x{}", dimensions[0], dimensions[1]);

            // 创建图片记录
            ImageEntity image = new ImageEntity();
            image.setName(fileName);
            image.setOriginalName(originalName);
            image.setExtension(extension);
            image.setSize(file.getSize());
            image.setContentType(file.getContentType());
            image.setMinioPath(minioPath);
            image.setBucketName(minioProperties.getBucketName());
            image.setUrl(url);
            image.setWidth(dimensions[0]);
            image.setHeight(dimensions[1]);
            image.setMd5(md5);
            image.setCategory(category);
            image.setBusinessId(businessId);
            image.setBusinessType(businessType);
            image.setStatus(0);
            image.setUploaderId(getCurrentUserId());
            image.setUploaderName(getCurrentUserName());
            image.setCreateTime(LocalDateTime.now());
            image.setUpdateTime(LocalDateTime.now());
            image.setRemark(remark);

            log.info("准备保存图片记录到数据库，URL: {}", image.getUrl());

            // 保存到数据库
            save(image);
            
            // 记录操作日志
            imageLogService.recordLog(image.getId(), "CREATE", "上传图片", null, image, "SUCCESS", null);
            
            log.info("图片上传成功: {}", image.getId());
            return image;
            
        } catch (Exception e) {
            log.error("图片上传失败", e);
            // 记录失败日志
            imageLogService.recordLog(null, "CREATE", "上传图片失败", null, null, "FAILED", e.getMessage());
            throw new RuntimeException("图片上传失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImageEntity> uploadImages(MultipartFile[] files, Integer category, String businessType, Long businessId, String remark) {
        List<ImageEntity> results = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                ImageEntity image = uploadImage(file, category, businessType, businessId, remark);
                results.add(image);
            } catch (Exception e) {
                log.error("批量上传中单个文件失败: {}", file.getOriginalFilename(), e);
                // 继续处理其他文件
            }
        }
        return results;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteImage(Long imageId) {
        try {
            ImageEntity image = getById(imageId);
            if (image == null) {
                throw new RuntimeException("图片不存在");
            }
            
            ImageEntity beforeData = new ImageEntity();
            // 复制原始数据用于日志记录
            copyImageData(image, beforeData);
            
            // 软删除：更新状态为已删除
            image.setStatus(1);
            image.setDeleteTime(LocalDateTime.now());
            image.setUpdateTime(LocalDateTime.now());
            
            boolean result = updateById(image);
            
            // 记录操作日志
            imageLogService.recordLog(imageId, "DELETE", "删除图片", beforeData, image, 
                                    result ? "SUCCESS" : "FAILED", null);
            
            log.info("图片删除成功: {}", imageId);
            return result;
            
        } catch (Exception e) {
            log.error("图片删除失败: {}", imageId, e);
            imageLogService.recordLog(imageId, "DELETE", "删除图片失败", null, null, "FAILED", e.getMessage());
            throw new RuntimeException("图片删除失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteImages(List<Long> imageIds) {
        boolean allSuccess = true;
        for (Long imageId : imageIds) {
            try {
                deleteImage(imageId);
            } catch (Exception e) {
                log.error("批量删除中单个图片失败: {}", imageId, e);
                allSuccess = false;
            }
        }
        return allSuccess;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateImage(ImageEntity image) {
        try {
            ImageEntity beforeData = getById(image.getId());
            if (beforeData == null) {
                throw new RuntimeException("图片不存在");
            }
            
            image.setUpdateTime(LocalDateTime.now());
            boolean result = updateById(image);
            
            // 记录操作日志
            imageLogService.recordLog(image.getId(), "UPDATE", "更新图片信息", beforeData, image, 
                                    result ? "SUCCESS" : "FAILED", null);
            
            return result;
            
        } catch (Exception e) {
            log.error("更新图片信息失败: {}", image.getId(), e);
            imageLogService.recordLog(image.getId(), "UPDATE", "更新图片信息失败", null, null, "FAILED", e.getMessage());
            throw new RuntimeException("更新图片信息失败: " + e.getMessage());
        }
    }
    
    @Override
    public ImageEntity getImageById(Long imageId) {
        try {
            ImageEntity image = getById(imageId);
            if (image != null) {
                // 记录查看日志
                imageLogService.recordLog(imageId, "VIEW", "查看图片详情", null, null, "SUCCESS", null);
            }
            return image;
        } catch (Exception e) {
            log.error("获取图片信息失败: {}", imageId, e);
            imageLogService.recordLog(imageId, "VIEW", "查看图片详情失败", null, null, "FAILED", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证上传参数
     */
    private void validateUploadParams(MultipartFile file, Integer category) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > minioProperties.getImageSizeLimit()) {
            throw new RuntimeException("文件大小超过限制");
        }
        
        // 检查文件类型
        String contentType = file.getContentType();
        if (!isAllowedContentType(contentType)) {
            throw new RuntimeException("不支持的文件类型");
        }
        
        // 检查分类
        if (category == null || category < 1 || category > 4) {
            throw new RuntimeException("图片分类参数错误");
        }
    }
    
    /**
     * 检查是否为允许的内容类型
     */
    private boolean isAllowedContentType(String contentType) {
        if (!StringUtils.hasText(contentType)) {
            return false;
        }
        String[] allowedTypes = minioProperties.getAllowedContentTypes().split(",");
        for (String allowedType : allowedTypes) {
            if (contentType.equals(allowedType.trim())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 计算文件MD5
     */
    private String calculateMD5(MultipartFile file) throws IOException {
        return DigestUtils.md5DigestAsHex(file.getInputStream());
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf(".");
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1).toLowerCase() : "";
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String extension) {
        return UUID.randomUUID().toString().replace("-", "") + 
               (StringUtils.hasText(extension) ? "." + extension : "");
    }
    
    /**
     * 生成MinIO存储路径
     */
    private String generateMinioPath(Integer category, String fileName) {
        String categoryPath = getCategoryPath(category);
        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return categoryPath + "/" + datePath + "/" + fileName;
    }
    
    /**
     * 获取分类路径
     */
    private String getCategoryPath(Integer category) {
        switch (category) {
            case 1: return "icons";
            case 2: return "uploads";
            case 3: return "system";
            case 4: return "templates";
            default: return "others";
        }
    }
    
    /**
     * 获取图片尺寸
     */
    private int[] getImageDimensions(MultipartFile file) {
        try {
            BufferedImage image = ImageIO.read(file.getInputStream());
            if (image != null) {
                return new int[]{image.getWidth(), image.getHeight()};
            }
        } catch (IOException e) {
            log.warn("获取图片尺寸失败: {}", file.getOriginalFilename(), e);
        }
        return new int[]{0, 0};
    }
    
    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        try {
            return SecurityUtils.getCurrentUserId();
        } catch (Exception e) {
            return 1L; // 默认系统用户
        }
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUserName() {
        try {
            return SecurityUtils.getCurrentUserName();
        } catch (Exception e) {
            return "system"; // 默认系统用户
        }
    }
    
    @Override
    public IPage<ImageEntity> getImagePage(int current, int size, Integer category, String businessType,
                                          Long uploaderId, Integer status, LocalDateTime startTime,
                                          LocalDateTime endTime, String keyword) {
        Page<ImageEntity> page = new Page<>(current, size);
        return baseMapper.selectImagePage(page, category, businessType, uploaderId, status, startTime, endTime, keyword);
    }

    @Override
    public List<ImageEntity> getImagesByBusiness(String businessType, Long businessId) {
        return baseMapper.selectByBusiness(businessType, businessId);
    }

    @Override
    public ImageEntity getImageByMd5(String md5) {
        return baseMapper.selectByMd5(md5);
    }

    @Override
    public Map<String, Object> getImageStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总图片数量
        long totalCount = count();
        statistics.put("totalCount", totalCount);

        // 各分类统计
        for (int category = 1; category <= 4; category++) {
            Long categoryCount = baseMapper.countByCategory(category);
            Long categorySize = baseMapper.sumSizeByCategory(category);

            Map<String, Object> categoryStats = new HashMap<>();
            categoryStats.put("count", categoryCount != null ? categoryCount : 0);
            categoryStats.put("size", categorySize != null ? categorySize : 0);

            statistics.put("category" + category, categoryStats);
        }

        return statistics;
    }

    @Override
    public Map<String, Object> getCategoryStatistics(Integer category) {
        Map<String, Object> statistics = new HashMap<>();
        Long count = baseMapper.countByCategory(category);
        Long size = baseMapper.sumSizeByCategory(category);

        statistics.put("count", count != null ? count : 0);
        statistics.put("size", size != null ? size : 0);

        return statistics;
    }

    @Override
    public boolean imageExists(Long imageId) {
        return getById(imageId) != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImageEntity copyImage(Long sourceImageId, String businessType, Long businessId, String remark) {
        try {
            ImageEntity sourceImage = getById(sourceImageId);
            if (sourceImage == null) {
                throw new RuntimeException("源图片不存在");
            }

            // 生成新的文件名和路径
            String newFileName = generateFileName(sourceImage.getExtension());
            String newMinioPath = generateMinioPath(sourceImage.getCategory(), newFileName);

            // 在MinIO中复制文件
            minioService.copyFile(sourceImage.getMinioPath(), newMinioPath);
            String newUrl = minioService.getFileUrl(newMinioPath);

            // 创建新的图片记录
            ImageEntity newImage = new ImageEntity();
            copyImageData(sourceImage, newImage);
            newImage.setId(null);
            newImage.setName(newFileName);
            newImage.setMinioPath(newMinioPath);
            newImage.setUrl(newUrl);
            newImage.setBusinessId(businessId);
            newImage.setBusinessType(businessType);
            newImage.setUploaderId(getCurrentUserId());
            newImage.setUploaderName(getCurrentUserName());
            newImage.setCreateTime(LocalDateTime.now());
            newImage.setUpdateTime(LocalDateTime.now());
            newImage.setDeleteTime(null);
            newImage.setRemark(remark);

            save(newImage);

            // 记录操作日志
            imageLogService.recordLog(newImage.getId(), "CREATE", "复制图片", sourceImage, newImage, "SUCCESS", null);

            return newImage;

        } catch (Exception e) {
            log.error("复制图片失败: {}", sourceImageId, e);
            imageLogService.recordLog(sourceImageId, "CREATE", "复制图片失败", null, null, "FAILED", e.getMessage());
            throw new RuntimeException("复制图片失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean restoreImage(Long imageId) {
        try {
            ImageEntity image = getById(imageId);
            if (image == null) {
                throw new RuntimeException("图片不存在");
            }

            ImageEntity beforeData = new ImageEntity();
            copyImageData(image, beforeData);

            // 恢复图片：更新状态为正常
            image.setStatus(0);
            image.setDeleteTime(null);
            image.setUpdateTime(LocalDateTime.now());

            boolean result = updateById(image);

            // 记录操作日志
            imageLogService.recordLog(imageId, "UPDATE", "恢复图片", beforeData, image,
                                    result ? "SUCCESS" : "FAILED", null);

            return result;

        } catch (Exception e) {
            log.error("恢复图片失败: {}", imageId, e);
            imageLogService.recordLog(imageId, "UPDATE", "恢复图片失败", null, null, "FAILED", e.getMessage());
            throw new RuntimeException("恢复图片失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean permanentDeleteImage(Long imageId) {
        try {
            ImageEntity image = getById(imageId);
            if (image == null) {
                throw new RuntimeException("图片不存在");
            }

            // 从MinIO删除文件
            minioService.deleteFile(image.getMinioPath());

            // 从数据库删除记录
            boolean result = removeById(imageId);

            // 记录操作日志
            imageLogService.recordLog(imageId, "DELETE", "永久删除图片", image, null,
                                    result ? "SUCCESS" : "FAILED", null);

            return result;

        } catch (Exception e) {
            log.error("永久删除图片失败: {}", imageId, e);
            imageLogService.recordLog(imageId, "DELETE", "永久删除图片失败", null, null, "FAILED", e.getMessage());
            throw new RuntimeException("永久删除图片失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredImages(int days) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(days);

            // 查询过期的已删除图片
            LambdaQueryWrapper<ImageEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ImageEntity::getStatus, 1)
                   .lt(ImageEntity::getDeleteTime, expireTime);

            List<ImageEntity> expiredImages = list(wrapper);
            int cleanedCount = 0;

            for (ImageEntity image : expiredImages) {
                try {
                    permanentDeleteImage(image.getId());
                    cleanedCount++;
                } catch (Exception e) {
                    log.error("清理过期图片失败: {}", image.getId(), e);
                }
            }

            log.info("清理过期图片完成，共清理 {} 张图片", cleanedCount);
            return cleanedCount;

        } catch (Exception e) {
            log.error("清理过期图片失败", e);
            throw new RuntimeException("清理过期图片失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> testMinioConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 测试存储桶是否存在
            String bucketName = minioProperties.getBucketName();
            boolean bucketExists = minioService.bucketExists(bucketName);

            result.put("bucketName", bucketName);
            result.put("bucketExists", bucketExists);
            result.put("endpoint", minioProperties.getEndpoint());
            result.put("connectionStatus", "SUCCESS");
            result.put("testTime", LocalDateTime.now());

            // 如果存储桶不存在，尝试创建
            if (!bucketExists) {
                minioService.createBucket(bucketName);
                result.put("bucketCreated", true);
                log.info("MinIO存储桶创建成功: {}", bucketName);
            }

            log.info("MinIO连接测试成功");
            return result;
        } catch (Exception e) {
            result.put("connectionStatus", "FAILED");
            result.put("error", e.getMessage());
            result.put("testTime", LocalDateTime.now());
            log.error("MinIO连接测试失败", e);
            return result;
        }
    }

    /**
     * 复制图片数据
     */
    private void copyImageData(ImageEntity source, ImageEntity target) {
        target.setId(source.getId());
        target.setName(source.getName());
        target.setOriginalName(source.getOriginalName());
        target.setExtension(source.getExtension());
        target.setSize(source.getSize());
        target.setContentType(source.getContentType());
        target.setMinioPath(source.getMinioPath());
        target.setBucketName(source.getBucketName());
        target.setUrl(source.getUrl());
        target.setWidth(source.getWidth());
        target.setHeight(source.getHeight());
        target.setMd5(source.getMd5());
        target.setCategory(source.getCategory());
        target.setBusinessId(source.getBusinessId());
        target.setBusinessType(source.getBusinessType());
        target.setStatus(source.getStatus());
        target.setUploaderId(source.getUploaderId());
        target.setUploaderName(source.getUploaderName());
        target.setCreateTime(source.getCreateTime());
        target.setUpdateTime(source.getUpdateTime());
        target.setDeleteTime(source.getDeleteTime());
        target.setRemark(source.getRemark());
    }
}
