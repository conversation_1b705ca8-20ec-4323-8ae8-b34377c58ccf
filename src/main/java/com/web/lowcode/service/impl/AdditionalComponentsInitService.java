package com.web.lowcode.service.impl;

import com.web.lowcode.entity.ComponentEntity;
import com.web.lowcode.entity.ComponentCategoryEntity;
import com.web.lowcode.service.ComponentService;
import com.web.lowcode.service.ComponentCategoryService;
import com.web.lowcode.util.Base64IconGenerator;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.List;

/**
 * 额外组件初始化服务
 * 用于添加更多组件到数据库
 */
@Service
public class AdditionalComponentsInitService {

    private static final Logger logger = LoggerFactory.getLogger(AdditionalComponentsInitService.class);

    @Autowired
    private ComponentService componentService;

    @Autowired
    private ComponentCategoryService componentCategoryService;

    @Autowired
    private DataSource dataSource;

    /**
     * 初始化额外组件数据
     */
    @PostConstruct
    public void initAdditionalComponents() {
        try {
            logger.info("Initializing additional components...");

            // 执行SQL脚本初始化额外组件数据
            ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
            populator.addScript(new ClassPathResource("db/additional_components.sql"));
            populator.execute(dataSource);

            // 更新组件图标为Base64格式
            updateComponentIconsToBase64();

            // 更新组件分类图标为Base64格式
            updateCategoryIconsToBase64();

            logger.info("Additional components initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize additional components", e);
        }
    }

    /**
     * 更新组件图标为Base64格式
     */
    private void updateComponentIconsToBase64() {
        try {
            logger.info("Updating component icons to Base64 format...");

            List<ComponentEntity> components = componentService.list();
            int updatedCount = 0;

            for (ComponentEntity component : components) {
                String icon = component.getIcon();
                String category = component.getCategory();

                // 如果图标不是Base64格式，则转换为Base64
                if (icon != null && !icon.isEmpty() && !icon.startsWith("data:")) {
                    String base64Icon = Base64IconGenerator.generateIconBase64(icon, category);
                    component.setIcon(base64Icon);
                    componentService.updateById(component);
                    updatedCount++;
                }
            }

            logger.info("Updated {} component icons to Base64 format", updatedCount);
        } catch (Exception e) {
            logger.error("Failed to update component icons to Base64 format", e);
        }
    }

    /**
     * 更新组件分类图标为Base64格式
     */
    private void updateCategoryIconsToBase64() {
        try {
            logger.info("Updating category icons to Base64 format...");

            List<ComponentCategoryEntity> categories = componentCategoryService.getAllCategories();
            int updatedCount = 0;

            for (ComponentCategoryEntity category : categories) {
                String icon = category.getIcon();
                String code = category.getCode();
                String name = category.getName();

                // 如果图标不是Base64格式，则转换为Base64
                if (icon != null && !icon.isEmpty() && !icon.startsWith("data:")) {
                    String base64Icon = Base64IconGenerator.generateCategoryIconBase64(code, name);
                    category.setIcon(base64Icon);
                    componentCategoryService.updateById(category);
                    updatedCount++;
                }
            }

            logger.info("Updated {} category icons to Base64 format", updatedCount);
        } catch (Exception e) {
            logger.error("Failed to update category icons to Base64 format", e);
        }
    }
}
