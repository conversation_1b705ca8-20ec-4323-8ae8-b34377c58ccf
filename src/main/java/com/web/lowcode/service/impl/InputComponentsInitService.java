package com.web.lowcode.service.impl;

import com.web.lowcode.service.ComponentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import javax.sql.DataSource;

/**
 * 输入框组件初始化服务
 */
@Service
public class InputComponentsInitService {
    private static final Logger logger = LoggerFactory.getLogger(InputComponentsInitService.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private ComponentService componentService;

    /**
     * 初始化输入框组件数据
     */
    @PostConstruct
    public void initInputComponents() {
        try {
//            logger.info("Initializing input components...");
//
//            // 执行SQL脚本初始化输入框组件数据
//            ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
//            populator.addScript(new ClassPathResource("db/input_components.sql"));
//            populator.execute(dataSource);
//
//            logger.info("Input components initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize input components", e);
        }
    }
}
