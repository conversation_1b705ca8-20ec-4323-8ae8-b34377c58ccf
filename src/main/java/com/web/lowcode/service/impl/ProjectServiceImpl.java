package com.web.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.mapper.ProjectMapper;
import com.web.lowcode.service.ProjectService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 项目服务实现类
 */
@Service
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, ProjectEntity> implements ProjectService {
    
    /**
     * 分页查询用户项目列表
     *
     * @param page      分页参数
     * @param userId    用户ID
     * @param name      项目名称（可选）
     * @return 分页结果
     */
    @Override
    public Page<ProjectEntity> pageByUser(Page<ProjectEntity> page, Long userId, String name) {
        LambdaQueryWrapper<ProjectEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectEntity::getCreatorId, userId);
        
        if (StringUtils.hasText(name)) {
            wrapper.like(ProjectEntity::getName, name);
        }
        
        wrapper.orderByDesc(ProjectEntity::getCreateTime);
        
        return page(page, wrapper);
    }
}
