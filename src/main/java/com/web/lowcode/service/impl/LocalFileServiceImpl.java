package com.web.lowcode.service.impl;

import com.web.lowcode.config.FileStorageConfig;
import com.web.lowcode.service.LocalFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 本地文件存储服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocalFileServiceImpl implements LocalFileService {
    
    private final FileStorageConfig.FileStorageProperties fileStorageProperties;
    
    /**
     * 初始化存储目录
     */
    @PostConstruct
    @Override
    public void initStorageDirectories() {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path baseDir = Paths.get(basePath);
            
            // 创建基础目录
            if (!Files.exists(baseDir)) {
                Files.createDirectories(baseDir);
                log.info("本地存储基础目录创建成功: {}", basePath);
            } else {
                log.info("本地存储基础目录已存在: {}", basePath);
            }
            
            // 创建分类子目录
            String[] categories = {"icons", "uploads", "system", "templates", "others"};
            for (String category : categories) {
                Path categoryDir = baseDir.resolve(category);
                if (!Files.exists(categoryDir)) {
                    Files.createDirectories(categoryDir);
                    log.info("分类目录创建成功: {}", categoryDir);
                }
            }
            
        } catch (Exception e) {
            log.error("初始化本地存储目录失败", e);
        }
    }
    
    @Override
    public String uploadFile(MultipartFile file, String relativePath) {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path targetPath = Paths.get(basePath, relativePath);
            
            // 确保目标目录存在
            Path parentDir = targetPath.getParent();
            if (!Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }
            
            // 保存文件
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
            }
            
            log.info("本地文件上传成功: {}", relativePath);
            return getFileUrl(relativePath);
            
        } catch (Exception e) {
            log.error("本地文件上传失败: {}", relativePath, e);
            throw new RuntimeException("本地文件上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public String uploadFile(InputStream inputStream, String relativePath, String contentType, long size) {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path targetPath = Paths.get(basePath, relativePath);
            
            // 确保目标目录存在
            Path parentDir = targetPath.getParent();
            if (!Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }
            
            // 保存文件
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
            
            log.info("本地文件流上传成功: {}", relativePath);
            return getFileUrl(relativePath);
            
        } catch (Exception e) {
            log.error("本地文件流上传失败: {}", relativePath, e);
            throw new RuntimeException("本地文件流上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public InputStream downloadFile(String relativePath) {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path filePath = Paths.get(basePath, relativePath);
            
            if (!Files.exists(filePath)) {
                throw new RuntimeException("文件不存在: " + relativePath);
            }
            
            return Files.newInputStream(filePath);
            
        } catch (Exception e) {
            log.error("本地文件下载失败: {}", relativePath, e);
            throw new RuntimeException("本地文件下载失败: " + e.getMessage());
        }
    }
    
    @Override
    public void deleteFile(String relativePath) {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path filePath = Paths.get(basePath, relativePath);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("本地文件删除成功: {}", relativePath);
            } else {
                log.warn("要删除的本地文件不存在: {}", relativePath);
            }
            
        } catch (Exception e) {
            log.error("本地文件删除失败: {}", relativePath, e);
            throw new RuntimeException("本地文件删除失败: " + e.getMessage());
        }
    }
    
    @Override
    public void deleteFiles(String[] relativePaths) {
        for (String relativePath : relativePaths) {
            try {
                deleteFile(relativePath);
            } catch (Exception e) {
                log.error("批量删除本地文件失败: {}", relativePath, e);
            }
        }
    }
    
    @Override
    public String getFileUrl(String relativePath) {
        String urlPrefix = fileStorageProperties.getLocal().getUrlPrefix();
        return urlPrefix + "/" + relativePath.replace("\\", "/");
    }
    
    @Override
    public Map<String, Object> getFileInfo(String relativePath) {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path filePath = Paths.get(basePath, relativePath);
            
            if (!Files.exists(filePath)) {
                throw new RuntimeException("文件不存在: " + relativePath);
            }
            
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("relativePath", relativePath);
            fileInfo.put("size", Files.size(filePath));
            fileInfo.put("lastModified", Files.getLastModifiedTime(filePath).toInstant());
            fileInfo.put("absolutePath", filePath.toAbsolutePath().toString());
            
            return fileInfo;
            
        } catch (Exception e) {
            log.error("获取本地文件信息失败: {}", relativePath, e);
            throw new RuntimeException("获取本地文件信息失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean fileExists(String relativePath) {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path filePath = Paths.get(basePath, relativePath);
            return Files.exists(filePath);
        } catch (Exception e) {
            log.error("检查本地文件是否存在失败: {}", relativePath, e);
            return false;
        }
    }
    
    @Override
    public void copyFile(String sourceRelativePath, String targetRelativePath) {
        try {
            String basePath = fileStorageProperties.getLocal().getBasePath();
            Path sourcePath = Paths.get(basePath, sourceRelativePath);
            Path targetPath = Paths.get(basePath, targetRelativePath);
            
            if (!Files.exists(sourcePath)) {
                throw new RuntimeException("源文件不存在: " + sourceRelativePath);
            }
            
            // 确保目标目录存在
            Path parentDir = targetPath.getParent();
            if (!Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }
            
            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("本地文件复制成功: {} -> {}", sourceRelativePath, targetRelativePath);
            
        } catch (Exception e) {
            log.error("本地文件复制失败: {} -> {}", sourceRelativePath, targetRelativePath, e);
            throw new RuntimeException("本地文件复制失败: " + e.getMessage());
        }
    }
}
