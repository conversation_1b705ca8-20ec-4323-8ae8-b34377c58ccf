package com.web.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.web.lowcode.entity.ComponentCategoryEntity;
import com.web.lowcode.mapper.ComponentCategoryMapper;
import com.web.lowcode.service.ComponentCategoryService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 组件分类服务实现
 */
@Service
public class ComponentCategoryServiceImpl extends ServiceImpl<ComponentCategoryMapper, ComponentCategoryEntity> implements ComponentCategoryService {

    private static final Logger logger = LoggerFactory.getLogger(ComponentCategoryServiceImpl.class);

    @Override
    public List<ComponentCategoryEntity> getAllCategories() {
        // 按排序字段升序排列
        LambdaQueryWrapper<ComponentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(ComponentCategoryEntity::getSort);
        return list(queryWrapper);
    }

    @Override
    public ComponentCategoryEntity getCategoryByCode(String code) {
        LambdaQueryWrapper<ComponentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ComponentCategoryEntity::getCode, code);
        return getOne(queryWrapper);
    }

    @Override
    @Transactional
    public ComponentCategoryEntity createCategory(ComponentCategoryEntity category) {
        // 检查编码是否已存在
        LambdaQueryWrapper<ComponentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ComponentCategoryEntity::getCode, category.getCode());
        if (count(queryWrapper) > 0) {
            throw new IllegalArgumentException("分类编码已存在");
        }

        // 设置默认值
        if (category.getSort() == null) {
            category.setSort(0);
        }
        if (category.getIsSystem() == null) {
            category.setIsSystem(0);
        }
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());

        // 保存分类
        save(category);
        return category;
    }

    @Override
    @Transactional
    public ComponentCategoryEntity updateCategory(Long id, ComponentCategoryEntity category) {
        // 检查分类是否存在
        ComponentCategoryEntity existingCategory = getById(id);
        if (existingCategory == null) {
            throw new IllegalArgumentException("分类不存在");
        }

        // 检查是否为系统内置分类
        if (existingCategory.getIsSystem() != null && existingCategory.getIsSystem() == 1) {
            throw new IllegalArgumentException("系统内置分类不允许修改");
        }

        // 检查编码是否已存在（排除自身）
        if (!existingCategory.getCode().equals(category.getCode())) {
            LambdaQueryWrapper<ComponentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ComponentCategoryEntity::getCode, category.getCode());
            if (count(queryWrapper) > 0) {
                throw new IllegalArgumentException("分类编码已存在");
            }
        }

        // 更新分类
        category.setId(id);
        category.setUpdateTime(LocalDateTime.now());
        updateById(category);
        return category;
    }

    @Override
    @Transactional
    public boolean deleteCategory(Long id) {
        // 检查分类是否存在
        ComponentCategoryEntity category = getById(id);
        if (category == null) {
            throw new IllegalArgumentException("分类不存在");
        }

        // 检查是否为系统内置分类
        if (category.getIsSystem() != null && category.getIsSystem() == 1) {
            throw new IllegalArgumentException("系统内置分类不允许删除");
        }

        // 删除分类
        return removeById(id);
    }

    /**
     * 初始化默认分类
     */
    @PostConstruct
    public void initDefaultCategories() {
        try {
            // 检查是否已有分类数据
            if (count() > 0) {
                logger.info("Component categories already exist, skipping initialization");
                return;
            }

            logger.info("Initializing default component categories...");

            List<ComponentCategoryEntity> defaultCategories = new ArrayList<>();

            // 基础组件
            defaultCategories.add(ComponentCategoryEntity.builder()
                    .code("basic")
                    .name("基础组件")
                    .icon("Document")
                    .sort(1)
                    .isSystem(1)
                    .creatorId(1L)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());

            // 表单组件
            defaultCategories.add(ComponentCategoryEntity.builder()
                    .code("form")
                    .name("表单组件")
                    .icon("Edit")
                    .sort(2)
                    .isSystem(1)
                    .creatorId(1L)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());

            // 布局组件
            defaultCategories.add(ComponentCategoryEntity.builder()
                    .code("layout")
                    .name("布局组件")
                    .icon("Grid")
                    .sort(3)
                    .isSystem(1)
                    .creatorId(1L)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());

            // 高级组件
            defaultCategories.add(ComponentCategoryEntity.builder()
                    .code("advanced")
                    .name("高级组件")
                    .icon("Star")
                    .sort(4)
                    .isSystem(1)
                    .creatorId(1L)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());

            // 流程组件
            defaultCategories.add(ComponentCategoryEntity.builder()
                    .code("flow")
                    .name("流程组件")
                    .icon("Connection")
                    .sort(5)
                    .isSystem(1)
                    .creatorId(1L)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());

            // 图标组件
            defaultCategories.add(ComponentCategoryEntity.builder()
                    .code("icon")
                    .name("图标组件")
                    .icon("PictureRounded")
                    .sort(6)
                    .isSystem(1)
                    .creatorId(1L)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());

            // 自定义组件
            defaultCategories.add(ComponentCategoryEntity.builder()
                    .code("custom")
                    .name("自定义组件")
                    .icon("SetUp")
                    .sort(7)
                    .isSystem(1)
                    .creatorId(1L)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build());

            // 保存默认分类
            saveBatch(defaultCategories);

            logger.info("Default component categories initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize default component categories", e);
        }
    }
}
