package com.web.lowcode.service.impl;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.generator.CodeGeneratorFactory;
import com.web.lowcode.service.ExportService;
import com.web.lowcode.service.PageService;
import com.web.lowcode.service.ProjectService;
import com.web.lowcode.util.ZipFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目导出服务实现类
 * 使用模板方法模式定义导出流程
 */
@Service
@Slf4j
public class ExportServiceImpl implements ExportService {

    private final ProjectService projectService;
    
    // 支持的框架类型
    private static final String[] SUPPORTED_FRAMEWORKS = {"vue", "uniapp", "vue-legacy"};

    @Autowired
    public ExportServiceImpl(ProjectService projectService) {
        this.projectService = projectService;
    }

    /**
     * 验证导出参数
     */
    @Override
    public Map<String, Object> validateExportParams(Long projectId, String framework) {
        Map<String, Object> result = new HashMap<>();
        
        // 验证项目ID
        if (projectId == null) {
            result.put("error", "项目ID不能为空");
            return result;
        }
        
        // 验证框架类型
        if (framework == null || !isValidFramework(framework)) {
            result.put("error", "不支持的框架类型: " + framework);
            return result;
        }
        
        // 验证项目是否存在
        ProjectEntity project = projectService.getById(projectId);
        if (project == null) {
            result.put("error", "项目不存在");
            return result;
        }
        
        // 验证通过，返回项目实体
        result.put("project", project);
        return result;
    }


    /**
     * 检查框架类型是否有效
     */
    private boolean isValidFramework(String framework) {
        for (String supportedFramework : SUPPORTED_FRAMEWORKS) {
            if (supportedFramework.equalsIgnoreCase(framework)) {
                return true;
            }
        }
        return false;
    }

}
