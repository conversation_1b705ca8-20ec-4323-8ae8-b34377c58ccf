package com.web.lowcode.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.mapper.PageMapper;
import com.web.lowcode.service.PageService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 页面服务实现类
 */
@Service
public class PageServiceImpl extends ServiceImpl<PageMapper, PageEntity> implements PageService {

    /**
     * 根据项目ID获取页面列表
     *
     * @param projectId 项目ID
     * @return 页面列表
     */
    @Override
    public List<PageEntity> getPagesByProjectId(Long projectId) {
        LambdaQueryWrapper<PageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PageEntity::getProjectId, projectId);
        wrapper.orderByAsc(PageEntity::getSort).orderByDesc(PageEntity::getCreateTime);
        return list(wrapper);
    }
}
