package com.web.lowcode.service.impl;

import com.web.lowcode.entity.PageEntity;
import com.web.lowcode.entity.ProjectEntity;
import com.web.lowcode.generator.CodeGenerator;
import com.web.lowcode.generator.CodeGeneratorFactory;
import com.web.lowcode.generator.decorator.CodeGeneratorDecoratorFactory;
import com.web.lowcode.service.ExportProjectService;
import com.web.lowcode.service.PageService;
import com.web.lowcode.service.ProjectService;
import com.web.lowcode.util.ZipFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目导出服务实现类
 * 使用模板方法模式定义导出流程
 */
@Service
@Slf4j
public class ExportProjectServiceImpl implements ExportProjectService {

    private final ProjectService projectService;
    private final PageService pageService;
    private final CodeGeneratorFactory generatorFactory;
    private final CodeGeneratorDecoratorFactory decoratorFactory;

    // 支持的框架类型
    private static final String[] SUPPORTED_FRAMEWORKS = {"vue", "uniapp", "vue-legacy"};

    @Autowired
    public ExportProjectServiceImpl(ProjectService projectService,
                                   PageService pageService,
                                   CodeGeneratorFactory generatorFactory,
                                   CodeGeneratorDecoratorFactory decoratorFactory) {
        this.projectService = projectService;
        this.pageService = pageService;
        this.generatorFactory = generatorFactory;
        this.decoratorFactory = decoratorFactory;
    }

    /**
     * 导出项目代码
     * 使用模板方法模式定义导出流程
     */
    @Override
    public ResponseEntity<byte[]> exportProjectCode(Long projectId, String framework) {
        log.info("开始导出项目代码，项目ID: {}，框架类型: {}", projectId, framework);

        try {
            // 1. 验证参数
            if (projectId == null) {
                return createErrorResponse("项目ID不能为空");
            }

            if (framework == null || !isValidFramework(framework)) {
                return createErrorResponse("不支持的框架类型: " + framework);
            }

            // 2. 获取项目信息
            ProjectEntity project = projectService.getById(projectId);
            if (project == null) {
                return createErrorResponse("项目不存在");
            }

            // 3. 获取项目页面
            List<PageEntity> pages = pageService.getPagesByProjectId(projectId);
            log.info("获取到项目页面数量: {}", pages.size());

            // 4. 获取代码生成器并应用装饰器
            log.info("使用 {} 框架的代码生成器", framework);
            CodeGenerator generator = generatorFactory.getGenerator(framework);
            log.info("原始代码生成器类型: {}", generator.getClass().getName());

            CodeGenerator decoratedGenerator = decoratorFactory.createDecorator(generator, framework);
            log.info("使用装饰器增强的代码生成器: {}", decoratedGenerator.getClass().getName());

            // 5. 生成项目文件
            log.info("开始生成项目文件...");
            Map<String, String> files = decoratedGenerator.generateProjectFiles(project, pages);
            log.info("生成项目文件数量: {}", files.size());

            // 记录生成的文件路径
            if (log.isDebugEnabled()) {
                log.debug("生成的文件路径:");
                for (String filePath : files.keySet()) {
                    log.debug(" - {}", filePath);
                }
            }

            // 分离文本文件和二进制文件
            Map<String, String> textFiles = new HashMap<>();
            Map<String, byte[]> binaryFiles = new HashMap<>();

            for (Map.Entry<String, String> entry : files.entrySet()) {
                String filePath = entry.getKey();
                String content = entry.getValue();

                if (isBinaryFile(filePath)) {
                    // 将二进制文件内容转换为字节数组
                    try {
                        binaryFiles.put(filePath, content.getBytes(StandardCharsets.ISO_8859_1));
                    } catch (Exception e) {
                        log.warn("处理二进制文件失败: {}", filePath, e);
                        textFiles.put(filePath, content); // 如果处理失败，作为文本文件处理
                    }
                } else {
                    textFiles.put(filePath, content);
                }
            }

            log.info("分离文件完成，文本文件: {}, 二进制文件: {}",
                    textFiles.size(), binaryFiles.size());

            // 6. 创建ZIP文件
            log.info("开始创建ZIP文件...");
            byte[] zipBytes = ZipFileUtil.createZip(textFiles, binaryFiles);
            log.info("创建ZIP文件成功，大小: {} 字节", zipBytes.length);

            // 7. 创建下载响应
            log.info("创建下载响应...");
            ResponseEntity<byte[]> response = createDownloadResponse(zipBytes, project.getName(), framework);
            log.info("导出项目代码成功，项目ID: {}，框架类型: {}", projectId, framework);
            return response;

        } catch (Exception e) {
            log.error("导出项目代码失败", e);
            return createErrorResponse("导出项目代码失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否为二进制文件
     * @param filePath 文件路径
     * @return 是否为二进制文件
     */
    private boolean isBinaryFile(String filePath) {
        String lowerPath = filePath.toLowerCase();
        return lowerPath.endsWith(".ico") ||
               lowerPath.endsWith(".png") ||
               lowerPath.endsWith(".jpg") ||
               lowerPath.endsWith(".jpeg") ||
               lowerPath.endsWith(".gif") ||
               lowerPath.endsWith(".bmp") ||
               lowerPath.endsWith(".pdf") ||
               lowerPath.endsWith(".zip") ||
               lowerPath.endsWith(".jar");
    }

    /**
     * 检查框架类型是否有效
     */
    @Override
    public boolean isValidFramework(String framework) {
        if (framework == null) {
            return false;
        }

        for (String supportedFramework : SUPPORTED_FRAMEWORKS) {
            if (supportedFramework.equalsIgnoreCase(framework)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 创建下载响应
     */
    private ResponseEntity<byte[]> createDownloadResponse(byte[] content, String projectName, String framework) {
        try {
            // 编码文件名
            String filename = projectName + "_" + framework + "_code.zip";
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString())
                .replace("+", "%20");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setContentLength(content.length);

            return ResponseEntity.ok()
                .headers(headers)
                .body(content);
        } catch (Exception e) {
            log.error("创建下载响应失败", e);
            return createErrorResponse("创建下载响应失败: " + e.getMessage());
        }
    }

    /**
     * 创建错误响应
     */
    private ResponseEntity<byte[]> createErrorResponse(String errorMessage) {
        log.error("导出错误: {}", errorMessage);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        return ResponseEntity.badRequest()
            .headers(headers)
            .body(errorMessage.getBytes(StandardCharsets.UTF_8));
    }
}
