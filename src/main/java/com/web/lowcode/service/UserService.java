package com.web.lowcode.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.web.lowcode.entity.UserEntity;

/**
 * 用户服务接口
 */
public interface UserService extends IService<UserEntity> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    UserEntity getByUsername(String username);

    /**
     * 创建用户
     *
     * @param user 用户信息
     * @return 创建的用户
     */
    UserEntity createUser(UserEntity user);
}
