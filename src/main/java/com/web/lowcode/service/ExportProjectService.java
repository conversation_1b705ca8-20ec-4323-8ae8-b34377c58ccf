package com.web.lowcode.service;

import org.springframework.http.ResponseEntity;

/**
 * 项目导出服务接口
 * 专门负责处理项目代码导出相关的业务逻辑
 */
public interface ExportProjectService {
    
    /**
     * 导出项目代码
     *
     * @param projectId 项目ID
     * @param framework 框架类型（vue, uniapp等）
     * @return 包含生成代码的HTTP响应
     */
    ResponseEntity<byte[]> exportProjectCode(Long projectId, String framework);
    
    /**
     * 检查框架类型是否有效
     * 
     * @param framework 框架类型
     * @return 是否有效
     */
    boolean isValidFramework(String framework);
}
