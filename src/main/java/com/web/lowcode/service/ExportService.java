package com.web.lowcode.service;

import com.web.lowcode.entity.ProjectEntity;
import org.springframework.http.ResponseEntity;

import java.util.Map;

/**
 * 项目导出服务接口
 * 负责处理项目代码导出相关的业务逻辑
 */
public interface ExportService {

    
    /**
     * 验证导出参数
     *
     * @param projectId 项目ID
     * @param framework 框架类型
     * @return 验证结果，包含项目实体和验证信息
     */
    Map<String, Object> validateExportParams(Long projectId, String framework);

}
