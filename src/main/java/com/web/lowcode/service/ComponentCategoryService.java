package com.web.lowcode.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.web.lowcode.entity.ComponentCategoryEntity;

import java.util.List;

/**
 * 组件分类服务接口
 */
public interface ComponentCategoryService extends IService<ComponentCategoryEntity> {

    /**
     * 获取所有组件分类
     *
     * @return 组件分类列表
     */
    List<ComponentCategoryEntity> getAllCategories();

    /**
     * 根据编码获取组件分类
     *
     * @param code 分类编码
     * @return 组件分类
     */
    ComponentCategoryEntity getCategoryByCode(String code);

    /**
     * 创建组件分类
     *
     * @param category 组件分类
     * @return 创建后的组件分类
     */
    ComponentCategoryEntity createCategory(ComponentCategoryEntity category);

    /**
     * 更新组件分类
     *
     * @param id       分类ID
     * @param category 组件分类
     * @return 更新后的组件分类
     */
    ComponentCategoryEntity updateCategory(Long id, ComponentCategoryEntity category);

    /**
     * 删除组件分类
     *
     * @param id 分类ID
     * @return 是否删除成功
     */
    boolean deleteCategory(Long id);
}
