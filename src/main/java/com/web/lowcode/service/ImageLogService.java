package com.web.lowcode.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.web.lowcode.entity.ImageEntity;
import com.web.lowcode.entity.ImageLogEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图片操作日志服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ImageLogService extends IService<ImageLogEntity> {
    
    /**
     * 记录操作日志
     */
    void recordLog(Long imageId, String operationType, String operationDesc, 
                  Object beforeData, Object afterData, String result, String errorMessage);
    
    /**
     * 分页查询操作日志
     */
    IPage<ImageLogEntity> getLogPage(int current, int size, Long imageId, String operationType, 
                                    Long operatorId, String result, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据图片ID查询操作日志
     */
    List<ImageLogEntity> getLogsByImageId(Long imageId);
    
    /**
     * 统计操作次数
     */
    Long countOperations(String operationType, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取操作统计信息
     */
    Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 清理过期日志
     */
    int cleanExpiredLogs(int days);
}
