package com.web.lowcode.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;

/**
 * MinIO服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MinioService {
    
    /**
     * 创建存储桶
     */
    void createBucket(String bucketName);
    
    /**
     * 检查存储桶是否存在
     */
    boolean bucketExists(String bucketName);
    
    /**
     * 上传文件
     */
    String uploadFile(MultipartFile file, String objectName);
    
    /**
     * 上传文件流
     */
    String uploadFile(InputStream inputStream, String objectName, String contentType, long size);
    
    /**
     * 下载文件
     */
    InputStream downloadFile(String objectName);
    
    /**
     * 删除文件
     */
    void deleteFile(String objectName);
    
    /**
     * 批量删除文件
     */
    void deleteFiles(String[] objectNames);
    
    /**
     * 获取文件访问URL
     */
    String getFileUrl(String objectName);
    
    /**
     * 获取文件预签名URL（临时访问）
     */
    String getPresignedUrl(String objectName, int expiry);
    
    /**
     * 获取文件信息
     */
    Map<String, Object> getFileInfo(String objectName);
    
    /**
     * 检查文件是否存在
     */
    boolean fileExists(String objectName);
    
    /**
     * 复制文件
     */
    void copyFile(String sourceObjectName, String targetObjectName);
}
