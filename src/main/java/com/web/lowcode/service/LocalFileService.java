package com.web.lowcode.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;

/**
 * 本地文件存储服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface LocalFileService {
    
    /**
     * 上传文件
     * @param file 文件
     * @param relativePath 相对路径
     * @return 文件访问URL
     */
    String uploadFile(MultipartFile file, String relativePath);
    
    /**
     * 上传文件流
     * @param inputStream 输入流
     * @param relativePath 相对路径
     * @param contentType 内容类型
     * @param size 文件大小
     * @return 文件访问URL
     */
    String uploadFile(InputStream inputStream, String relativePath, String contentType, long size);
    
    /**
     * 下载文件
     * @param relativePath 相对路径
     * @return 文件输入流
     */
    InputStream downloadFile(String relativePath);
    
    /**
     * 删除文件
     * @param relativePath 相对路径
     */
    void deleteFile(String relativePath);
    
    /**
     * 批量删除文件
     * @param relativePaths 相对路径数组
     */
    void deleteFiles(String[] relativePaths);
    
    /**
     * 获取文件访问URL
     * @param relativePath 相对路径
     * @return 文件访问URL
     */
    String getFileUrl(String relativePath);
    
    /**
     * 获取文件信息
     * @param relativePath 相对路径
     * @return 文件信息
     */
    Map<String, Object> getFileInfo(String relativePath);
    
    /**
     * 检查文件是否存在
     * @param relativePath 相对路径
     * @return 是否存在
     */
    boolean fileExists(String relativePath);
    
    /**
     * 复制文件
     * @param sourceRelativePath 源文件相对路径
     * @param targetRelativePath 目标文件相对路径
     */
    void copyFile(String sourceRelativePath, String targetRelativePath);
    
    /**
     * 初始化存储目录
     */
    void initStorageDirectories();
}
